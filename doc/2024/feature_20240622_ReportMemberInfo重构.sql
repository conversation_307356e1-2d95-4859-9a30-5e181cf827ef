-- ucenter上线后执行：
update ucenter.openmember set updatetime = addtime where updatetime is null;
create index updatetime on ucenter.openmember (updatetime);

-- sadcenter上线前执行：
-- auto-generated definition
create table report_member_action_stats_month
(
    id                 varchar(35)                 not null
        primary key,
    member_id          bigint                      not null comment '会员编号',
    company_id         bigint                      not null comment '企业编号',
    order_type         varchar(30)                 not null comment '订单类型',
    month              char(6)                     not null comment '年月，yyyyMM',
    updatetime         datetime                    not null comment '更新时间',
    order_count        int            default 0    not null comment '订单数量',
    expenditure_count  int            default 0    not null comment '消费商品数量',
    total_expenditure  decimal(12, 2) default 0.00 not null comment '总消费金额',
    refund_order_count int            default 0    not null comment '退款订单数量',
    refund_count       int            default 0    not null comment '退货数量',
    total_refund       decimal(12, 2) default 0.00 not null comment '总退款金额',
    constraint uk_member_id_company_id_order_type_month
        unique (member_id, company_id, order_type, month)
)
    comment '会员统计月表';

-- sadcenter上线后，重新刷新全表前执行：
truncate table report_member_info;

-- （1）/inner/report/member/updateNewMemberInfos.xhtml?datefrom=yyyy-MM1&dateto=yyyy-MM2
-- （2）/inner/report/member/updateThMemberInfos.xhtml?datefrom=yyyy-MM1&dateto=yyyy-MM2
-- 注1：必须等上面（1）会员新增链接updateNewMemberInfos.xhtml最终刷新完成后，再执行（2）会员更新链接updateThMemberInfos.xhtml（因为更新操作中有需要查询该报表最新数据），
--     而（1）最终刷新完成与否，可查日志："刷新新会员信息列表结束"关键词，紧跟其后的刷新结束日期应大于等于dateto的日期（可自行查询对应链接的对应方法查看对应打印日志）；
-- 注2：上面（1）、（2）两个更新连接的dateto取当前上线日期，datefrom取下面两个sql语句最小的日期：
-- select min(addtime) from ucenter.openmember;
-- select min(addtime) from ucenter.member;


-- 创建视图v_report_member_info，是等report_member_action_stats_month表创建完成后，还是等上面表刷新完后执行？
create definer = sadcenter@`%` view v_report_member_info as
select `rmi`.`id`                                         AS `id`,
       `rmi`.`member_id`                                  AS `member_id`,
       `rmi`.`mobile`                                     AS `mobile`,
       `rmi`.`email`                                      AS `email`,
       `rmi`.`nickname`                                   AS `nickname`,
       `rmi`.`headpic`                                    AS `headpic`,
       `rmi`.`openid`                                     AS `openid`,
       `rmi`.`appkey`                                     AS `appkey`,
       `rmi`.`create_time`                                AS `create_time`,
       `rmi`.`member_level_id`                            AS `member_level_id`,
       `rmi`.`end_time`                                   AS `end_time`,
       `rmi`.`point`                                      AS `point`,
       `rmi`.`growth`                                     AS `growth`,
       `rmi`.`first_order`                                AS `first_order`,
       `rmi`.`last_order`                                 AS `last_order`,
       `rmi`.`company_id`                                 AS `company_id`,
       `rmi`.`addtime`                                    AS `addtime`,
       `rmi`.`updatetime`                                 AS `updatetime`,
       `sm`.`order_num`                                   AS `order_num`,
       `sm`.`ticket_num`                                  AS `ticket_num`,
       `sm`.`total_amount`                                AS `total_amount`,
       `sm`.`refund_num`                                  AS `refund_num`,
       round((`sm`.`total_amount` / `sm`.`order_num`), 2) AS `avg_price`
from (`sadcenter`.`report_member_info` `rmi` left join (select `sadcenter`.`report_member_action_stats_month`.`member_id`              AS `member_id`,
                                                               `sadcenter`.`report_member_action_stats_month`.`company_id`             AS `company_id`,
                                                               sum(`sadcenter`.`report_member_action_stats_month`.`order_count`)       AS `order_num`,
                                                               sum(`sadcenter`.`report_member_action_stats_month`.`expenditure_count`) AS `ticket_num`,
                                                               sum(`sadcenter`.`report_member_action_stats_month`.`total_expenditure`) AS `total_amount`,
                                                               sum(`sadcenter`.`report_member_action_stats_month`.`refund_count`)      AS `refund_num`
                                                        from `sadcenter`.`report_member_action_stats_month`
                                                        group by `sadcenter`.`report_member_action_stats_month`.`member_id`,
                                                                 `sadcenter`.`report_member_action_stats_month`.`company_id`) `sm`
                                                       on (((`sm`.`member_id` = `rmi`.`member_id`) and (`sm`.`company_id` = `rmi`.`company_id`))));
-- comment on column v_report_member_info.nickname not supported: 昵称
-- comment on column v_report_member_info.headpic not supported: 头像
-- comment on column v_report_member_info.create_time not supported: 创建日期
-- comment on column v_report_member_info.member_level_id not supported: 会员等级信息
-- comment on column v_report_member_info.end_time not supported: 会员到期日
-- comment on column v_report_member_info.point not supported: 积分
-- comment on column v_report_member_info.growth not supported: 有效成长值
-- comment on column v_report_member_info.first_order not supported: 首单json串
-- comment on column v_report_member_info.last_order not supported: 末单json串


-- 等视图v_report_member_info创建完成后执行：
grant select on sadcenter.v_report_member_info to 'thvendor';
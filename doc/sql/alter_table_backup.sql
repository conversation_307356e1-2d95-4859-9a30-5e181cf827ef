-- 备份alter_table.sql文件中修改了字段长度和是否可空的报表的创建语句，供执行报错时查询相关表字段信息进行回滚。
-- discount_apportionment_check、report_balance_detail、report_balance_gather_day、report_check_record、
-- report_group_reserve_check_detail、report_group_reserve_check_gather由曾得晋已比对完并在测试环境执行，需从生产库查询这些报表的创建语句进行备份。


CREATE TABLE `report_mall_order_detail` (
    `id` varchar(20) NOT NULL,
    `order_type` varchar(20) NOT NULL,
    `trade_no` varchar(30) NOT NULL COMMENT '订单号',
    `order_time` datetime DEFAULT NULL COMMENT '订单时间',
    `order_status` varchar(20) DEFAULT NULL COMMENT '订单状态',
    `mobile` varchar(11) DEFAULT NULL COMMENT '注册手机号',
    `member_name` varchar(64) DEFAULT NULL COMMENT '客户名称',
    `product_category_id` bigint DEFAULT NULL COMMENT '商品类目id',
    `product_category_name` varchar(64) DEFAULT NULL COMMENT '商品类目名称',
    `detail_product_id` bigint DEFAULT NULL COMMENT '商品ID',
    `product_name` varchar(200) DEFAULT NULL COMMENT '商品名称',
    `sku_name` varchar(35) DEFAULT NULL COMMENT '规格名称',
    `sku_code` varchar(64) DEFAULT NULL COMMENT '规格编码',
    `pay_type` varchar(10) DEFAULT NULL COMMENT '类型',
    `unit_price` decimal(8,2) DEFAULT NULL COMMENT '单价',
    `quantity` int DEFAULT NULL COMMENT '数量',
    `amount` decimal(8,2) DEFAULT NULL COMMENT '金额',
    `discount` decimal(8,2) DEFAULT NULL COMMENT '优惠金额',
    `paid_amount` decimal(8,2) DEFAULT NULL COMMENT '支付金额',
    `gateway_code` varchar(20) DEFAULT NULL COMMENT '支付方式',
    `sale_user_id` bigint DEFAULT NULL COMMENT '销售用户',
    `sale_user_group_id` bigint DEFAULT NULL COMMENT '销售用户组',
    `company_id` bigint DEFAULT NULL,
    `updatetime` datetime DEFAULT NULL,
    `detail_sku_id` bigint DEFAULT NULL COMMENT '具体规格ID',
    `member_id` bigint DEFAULT NULL,
    `card_no` varchar(50) DEFAULT NULL COMMENT '会员卡号',
    `member_level_id` bigint DEFAULT NULL,
    `member_level_name` varchar(40) DEFAULT NULL,
    `member_status` varchar(1) DEFAULT NULL COMMENT '会员状态（Y:激活,N:未开通会员,P:暂停状态）',
    `realname` varchar(100) DEFAULT NULL COMMENT '用户真实姓名',
    `agency_id` bigint DEFAULT NULL,
    `agency_name` varchar(50) DEFAULT NULL,
    `main_trade_no` varchar(50) DEFAULT NULL COMMENT '加购主订单',
    `point_value` int DEFAULT NULL COMMENT '支付积分',
    `merchant_code`         varchar(20)   null,
    PRIMARY KEY (`id`),
    KEY `idx_order_time` (`order_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='积分商城订单明细表';


CREATE TABLE `report_mall_rent_amount_detail` (
  `id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `time` datetime DEFAULT NULL COMMENT '时间',
  `trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单号',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '注册手机号',
  `detail_product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品名称',
  `detail_sku_id` bigint DEFAULT NULL,
  `sku_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品规格编码',
  `product_attr` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品规格',
  `operation_amount` decimal(10,2) DEFAULT NULL COMMENT '金额',
  `operation_mode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作',
  `gateway_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '支付方式',
  `payseqno` varchar(40) DEFAULT NULL COMMENT '支付流水',
  `company_id` bigint NOT NULL,
  `updatetime` datetime NOT NULL,
  `agency_id` bigint DEFAULT NULL,
  `agency_name` varchar(50) DEFAULT NULL,
  `merchant_code`     varchar(20)    null,
  PRIMARY KEY (`id`),
  KEY `idx_time` (`time`) USING BTREE,
  KEY `idx_product_id` (`detail_product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='租赁商品金额明细报表';


CREATE TABLE `report_mall_rent_status_detail` (
  `id` varchar(30) NOT NULL,
  `time` datetime DEFAULT NULL COMMENT '时间',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '订单号',
  `mobile` varchar(11) DEFAULT NULL COMMENT '注册手机号',
  `detail_product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(64) DEFAULT NULL COMMENT '商品名称',
  `detail_sku_id` bigint DEFAULT NULL,
  `sku_code` varchar(64) DEFAULT NULL COMMENT '商品规格编码',
  `product_attr` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品规格',
  `rent_status` varchar(15) DEFAULT NULL COMMENT '租赁状态',
  `product_status` varchar(10) DEFAULT NULL COMMENT '物品状态',
  `company_id` bigint NOT NULL,
  `updatetime` datetime NOT NULL,
  `agency_id` bigint DEFAULT NULL,
  `agency_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_time` (`time`),
  KEY `idx_trade_no` (`trade_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物品租赁明细报表';


CREATE TABLE `report_member_info` (
  `id` varchar(35) NOT NULL,
  `member_id` bigint NOT NULL,
  `mobile` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `nickname` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '昵称',
  `headpic` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '头像',
  `openid` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '小程序openID',
  `appkey` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `create_time` datetime NOT NULL COMMENT '创建日期',
  `member_level_id` bigint DEFAULT NULL COMMENT '会员等级信息',
  `end_time` datetime DEFAULT NULL COMMENT '会员到期日',
  `point` int DEFAULT '0' COMMENT '积分',
  `growth` int DEFAULT '0' COMMENT '有效成长值',
  `first_order` json DEFAULT NULL COMMENT '首单json串',
  `last_order` json DEFAULT NULL COMMENT '末单json串',
  `order_num` int NOT NULL DEFAULT '0' COMMENT '总订单数量',
  `ticket_num` int NOT NULL DEFAULT '0' COMMENT '总票张数量',
  `refund_num` int NOT NULL DEFAULT '0' COMMENT '总票张数量',
  `total_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '订单总额',
  `avg_price` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '每单平均客单价',
  `company_id` bigint NOT NULL,
  `addtime` datetime NOT NULL,
  `updatetime` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_member` (`member_id`,`company_id`,`openid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员信息总表';


CREATE TABLE `report_memberinfo_statistics` (
    `id` varchar(35) NOT NULL,
    `member_id` bigint NOT NULL COMMENT '会员ID',
    `name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会员名称',
    `member_level_id` bigint DEFAULT NULL COMMENT '会员等级信息',
    `member_level_name` varchar(40) DEFAULT NULL COMMENT '会员等级名称',
    `begin_time` datetime DEFAULT NULL COMMENT '有效期开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '有效期结束时间',
    `effective_growth_value` int NOT NULL DEFAULT '0' COMMENT '有效成长值',
    `point` int NOT NULL DEFAULT '0' COMMENT '积分',
    `company_id` bigint NOT NULL COMMENT '企业ID',
    `addtime` datetime NOT NULL,
    `updatetime` datetime NOT NULL,
    `realname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '姓名',
    `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '性别 1男 2女 0未知',
    `birthday` date DEFAULT NULL COMMENT '出生年月日(yyyy-MM-dd)',
    `birth_date` varchar(4) DEFAULT NULL COMMENT '生日（MMdd）',
    `email` varchar(100) DEFAULT NULL,
    `mobile` varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
    `card_no` varchar(6) DEFAULT NULL COMMENT '会员卡号',
    `status` varchar(2) NOT NULL COMMENT '状态： Y：表示已开通会员，P：表示临时暂停状态（各类积分、成长值正常记录）N：未开通会员，memberLevelId为0时的状态',
    `active_time` datetime DEFAULT NULL COMMENT '会员激活时间',
    `favorite` varchar(300) DEFAULT NULL COMMENT '演出形式',
    `member_activitie_type` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '会员活动',
    `preferred_price` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '合理票价',
    `postcard` varchar(15) DEFAULT NULL COMMENT '邮编',
    `address` varchar(100) DEFAULT NULL COMMENT '地址',
    `telephone` varchar(30) DEFAULT NULL COMMENT '固定电话',
    `expend_total_amount` decimal(8,2) DEFAULT NULL COMMENT '消费总金额',
    `buy_schedule_count` smallint DEFAULT NULL COMMENT '购买场次数',
    `buy_count` smallint DEFAULT NULL COMMENT '购票总张数',
    PRIMARY KEY (`id`),
    KEY `idx_memberid` (`member_id`),
    KEY `idx_active_time` (`active_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员分析汇总';


CREATE TABLE `report_member_sale_detail` (
     `id` varchar(200) NOT NULL COMMENT 'id',
     `trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单号',
     `paidtime` datetime DEFAULT NULL COMMENT '购卡时间',
     `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
     `member_card_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会员卡卡种',
     `price` decimal(8,2) DEFAULT '0.00' COMMENT '原价',
     `discount` decimal(8,2) DEFAULT '0.00' COMMENT '折扣',
     `paid_amount` decimal(8,2) DEFAULT '0.00' COMMENT '实收金额',
     `refund_amount` decimal(8,2) DEFAULT '0.00' COMMENT '退款金额',
     `refundtime` datetime DEFAULT NULL COMMENT '退款时间',
     `real_name` varchar(50) DEFAULT NULL COMMENT '姓名',
     `certificate_type` varchar(50) DEFAULT NULL COMMENT '证件类型',
     `certificate_no` varchar(200) DEFAULT NULL COMMENT '证件号码',
     `paymethod` varchar(100) DEFAULT NULL COMMENT '支付方式',
     `user_group_name` varchar(100) DEFAULT NULL COMMENT '用户组名称',
     `user_name` varchar(50) DEFAULT NULL COMMENT '用户名称',
     `updatetime` datetime NOT NULL COMMENT '更新时间',
     `membership_card_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会员卡卡种ID',
     `company_id` bigint NOT NULL COMMENT '企业ID',
     `tbs_user_id` bigint DEFAULT NULL COMMENT 'tbs_user_id',
     `user_group_id` bigint DEFAULT NULL COMMENT '用户组ID',
     `pay_status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '原订单支付状态',
     `membership_status` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '卡状态',
     `cardno` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '会员卡号',
     `pay_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '支付还是退款：pay/refund',
     `gateway_code` varchar(20) DEFAULT NULL,
     `platform` varchar(20) DEFAULT NULL,
     PRIMARY KEY (`id`) USING BTREE,
     KEY `idx_paid_time` (`paidtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `report_member_sale_gather` (
     `id` varchar(100) NOT NULL COMMENT 'id',
     `membership_card_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会员卡卡种',
     `price` decimal(8,2) DEFAULT NULL COMMENT '价格',
     `sale_count` int DEFAULT '0' COMMENT '销售数量',
     `refund_count` int DEFAULT '0' COMMENT '退票数量',
     `quantity` int DEFAULT '0' COMMENT '数量',
     `add_date` date DEFAULT NULL COMMENT '下单日期',
     `user_group_name` varchar(200) DEFAULT NULL COMMENT '下单用户组',
     `paymethod` varchar(100) DEFAULT NULL COMMENT '用户组ID',
     `sale_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
     `refund_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
     `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
     `sale_discount` decimal(10,2) NOT NULL DEFAULT '0.00',
     `refund_discount` decimal(10,2) NOT NULL DEFAULT '0.00',
     `total_discount` decimal(10,2) NOT NULL DEFAULT '0.00',
     `sale_paid_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
     `refund_paid_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
     `total_paid_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
     `updatetime` datetime NOT NULL COMMENT '更新时间',
     `membership_card_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会员卡卡种ID',
     `company_id` bigint NOT NULL COMMENT '企业ID',
     `user_group_id` bigint DEFAULT NULL COMMENT '下单用户组ID',
     PRIMARY KEY (`id`),
     KEY `idx_add_date` (`add_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `report_membership_check_detail` (
  `id` bigint NOT NULL COMMENT 'id',
  `card_no` varchar(200) DEFAULT NULL COMMENT '卡号',
  `trade_no` varchar(200) DEFAULT NULL COMMENT '订单号',
  `membership_card_type` varchar(100) DEFAULT NULL COMMENT '会员卡卡种',
  `membership_card_id` varchar(100) DEFAULT NULL COMMENT '会员卡卡种ID',
  `price` decimal(8,2) DEFAULT NULL COMMENT '价格',
  `user_group_name` varchar(200) DEFAULT NULL COMMENT '销售用户组名称',
  `user_group_id` bigint DEFAULT NULL COMMENT '用户组ID',
  `check_user_group_name` varchar(200) DEFAULT NULL COMMENT '核销用户组名称',
  `check_no` varchar(200) DEFAULT NULL COMMENT '核销账号',
  `real_name` varchar(200) DEFAULT NULL COMMENT '姓名',
  `certificate_type` varchar(50) DEFAULT NULL COMMENT '证件类型',
  `certificate_no` varchar(100) DEFAULT NULL COMMENT '证件号码',
  `check_time` datetime DEFAULT NULL COMMENT '核销时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `company_id` bigint NOT NULL COMMENT '企业ID',
  `tbs_user_id` bigint DEFAULT NULL COMMENT 'tbs_user_id',
  `ticket_type_id` bigint DEFAULT NULL COMMENT '票种ID',
  `mobile` varchar(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_check_time` (`check_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `report_membership_verify_detail` (
   `id` bigint NOT NULL,
   `card_no` varchar(200) DEFAULT NULL COMMENT '卡号',
   `verify_time` datetime DEFAULT NULL,
   `membership_card_type` varchar(200) DEFAULT NULL COMMENT '会员卡卡种',
   `membership_card_id` varchar(50) DEFAULT NULL COMMENT '会员卡卡种ID',
   `price` decimal(8,2) DEFAULT NULL COMMENT '价格',
   `user_group_name` varchar(100) DEFAULT NULL COMMENT '销售用户组',
   `user_group_id` bigint DEFAULT NULL COMMENT '销售用户组ID',
   `check_group_name` varchar(100) DEFAULT NULL COMMENT '验证用户组',
   `check_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '核销账号',
   `real_name` varchar(100) DEFAULT NULL COMMENT '姓名',
   `certificate_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '证件类型',
   `certificate_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '证件号',
   `company_id` bigint NOT NULL COMMENT '企业ID',
   `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
   `tbs_user_id` bigint DEFAULT NULL COMMENT 'tbs_user_id',
   `ticket_type_id` bigint DEFAULT NULL COMMENT '票种ID',
   `check_group_id` bigint DEFAULT NULL COMMENT '验证用户组ID',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `report_multicard_detail` (
   `id` varchar(35) NOT NULL,
   `discount_id` bigint DEFAULT NULL COMMENT '优惠ID',
   `tickettime` datetime DEFAULT NULL COMMENT '出票/退票时间',
   `membership_type_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多次卡卡种ID',
   `membership_type_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多次卡名称',
   `membership_card_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多次卡卡号',
   `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '下单手机号',
   `coupon_batch_id` bigint DEFAULT NULL COMMENT '多次券批次ID',
   `coupon_name` varchar(100) DEFAULT NULL COMMENT '多次券名称',
   `coupon_card_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多次券号码',
   `totalnum` int DEFAULT NULL COMMENT '最大可用次数',
   `trade_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '销售订单号',
   `order_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单类型',
   `pay_type` varchar(10) DEFAULT NULL COMMENT 'pay/refund',
   `discount_info` json DEFAULT NULL COMMENT '优惠信息',
   `program_id` bigint DEFAULT NULL COMMENT '项目ID',
   `program_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目名称',
   `show_id` bigint DEFAULT NULL COMMENT '场次ID',
   `show_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场次名称',
   `ticket_type_id` bigint DEFAULT NULL COMMENT '票种ID',
   `ticket_type_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '票种名称',
   `price` decimal(8,2) DEFAULT NULL COMMENT '票价',
   `uuid` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '票号',
   `company_id` bigint NOT NULL COMMENT '企业ID',
   `updatetime` datetime NOT NULL COMMENT '更新时间',
   PRIMARY KEY (`id`),
   KEY `idx_tickettime` (`tickettime`) USING BTREE,
   KEY `idx_mobile` (`mobile`) USING BTREE,
   KEY `idx_membership_type_id` (`membership_type_id`) USING BTREE,
   KEY `idx_program_id` (`program_id`) USING BTREE,
   KEY `idx_membership_card_no` (`membership_card_no`) USING BTREE,
   KEY `idx_coupon_card_no` (`coupon_card_no`) USING BTREE,
   KEY `idx_trade_no` (`trade_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='次兑卡使用明细表';


CREATE TABLE `report_order_detail` (
   `id` varchar(35) NOT NULL,
   `order_detail_id` bigint DEFAULT NULL COMMENT '订单ID',
   `trade_no` varchar(30) DEFAULT NULL COMMENT '订单号',
   `pay_status` varchar(20) DEFAULT NULL COMMENT '状态',
   `member_id` bigint DEFAULT NULL COMMENT '会员ID',
   `amount` decimal(8,2) DEFAULT NULL COMMENT '订单金额',
   `program_id` bigint DEFAULT NULL COMMENT '项目ID',
   `play_time` datetime DEFAULT NULL COMMENT '演出时间',
   `gateway_code` varchar(20) DEFAULT NULL COMMENT '支付方式',
   `paidtime` datetime DEFAULT NULL COMMENT '支付时间',
   `program_attr` varchar(30) DEFAULT NULL COMMENT '项目属性',
   `updatetime` datetime NOT NULL COMMENT '更新时间',
   `ticket_type` varchar(30) DEFAULT NULL COMMENT 'show 站票 schedule 座票',
   `schedule_id` bigint DEFAULT NULL COMMENT '场次ID',
   `schedule_name` varchar(100) DEFAULT NULL COMMENT '场次名称',
   `quantity` smallint DEFAULT NULL COMMENT '购买数量',
   `schedule_time` datetime DEFAULT NULL COMMENT '场次时间',
   `stadium_name` varchar(30) DEFAULT NULL,
   `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
   `venue_name` varchar(30) DEFAULT NULL,
   `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
   `company_id` bigint NOT NULL COMMENT '企业ID',
   `platform` varchar(15) DEFAULT NULL COMMENT '来源',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单';


CREATE TABLE `report_order_discount_detail` (
    `id` varchar(35) NOT NULL,
    `platform` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `category` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `order_type` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `trade_no` varchar(30) DEFAULT NULL,
    `ordertime` datetime DEFAULT NULL,
    `pay_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `program_id` bigint DEFAULT NULL,
    `program_name` varchar(100) DEFAULT NULL,
    `schedule_id` bigint DEFAULT NULL,
    `schedule_name` varchar(100) DEFAULT NULL,
    `quantity` smallint DEFAULT NULL,
    `amount` decimal(8,2) DEFAULT NULL,
    `discount` decimal(8,2) DEFAULT NULL,
    `description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `card_no` varchar(32) DEFAULT NULL,
    `updatetime` datetime NOT NULL COMMENT '更新时间',
    `company_id` bigint NOT NULL COMMENT '企业ID',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_ordertime` (`ordertime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单优惠明细表';


CREATE TABLE `report_reserve_check_detail` (
   `id` bigint NOT NULL COMMENT 'id',
   `reserve_no` varchar(200) DEFAULT NULL COMMENT '预约单号',
   `program_name` varchar(255) DEFAULT NULL COMMENT '预约项目名称',
   `reservedate` date DEFAULT NULL COMMENT '预约日期',
   `starttime` datetime DEFAULT NULL COMMENT '预约开始时间',
   `endtime` datetime DEFAULT NULL COMMENT '预约结束时间',
   `real_name` varchar(255) DEFAULT NULL COMMENT '姓名',
   `certificate_type` varchar(255) DEFAULT NULL COMMENT '证件类型',
   `certificate_no` varchar(200) DEFAULT NULL COMMENT '证件号码',
   `mobile` varchar(50) DEFAULT NULL COMMENT '注册手机号',
   `addtime` datetime DEFAULT NULL COMMENT '下单时间',
   `check_time` datetime DEFAULT NULL COMMENT '核销时间',
   `status` varchar(50) DEFAULT NULL COMMENT '状态',
   `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
   `reserve_program_id` bigint DEFAULT NULL COMMENT '预约项目ID',
   `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
   `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
   `company_id` bigint NOT NULL COMMENT '企业ID',
   `user_id` bigint DEFAULT NULL,
   `user_group_id` bigint DEFAULT NULL,
   PRIMARY KEY (`id`),
   KEY `idx_reserve_date` (`reservedate`),
   KEY `idx_addtime` (`addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `report_reserve_check_gather` (
   `id` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'id',
   `reserve_date` date DEFAULT NULL COMMENT '预约日期',
   `stadium_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场馆名称',
   `venue_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场地名称',
   `program_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '预约项目名称',
   `reserve_time` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '预约时间段',
   `reserve_gather` bigint DEFAULT NULL COMMENT '预约合计',
   `check_gather` bigint DEFAULT NULL COMMENT '入场合计',
   `reserve_program_id` bigint DEFAULT NULL COMMENT '预约项目ID',
   `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
   `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
   `company_id` bigint NOT NULL COMMENT '企业ID',
   `updatetime` datetime NOT NULL COMMENT '更新时间',
   `user_group_id` bigint DEFAULT NULL,
   `user_id` bigint DEFAULT NULL,
   PRIMARY KEY (`id`),
   KEY `reservedate` (`reserve_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `report_seat_check_gather` (
  `id` varchar(35) NOT NULL,
  `check_date` date DEFAULT NULL COMMENT '核销日期',
  `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
  `stadium_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场馆名称',
  `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
  `venue_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场地名称',
  `program_id` bigint DEFAULT NULL COMMENT '项目ID',
  `program_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目名称',
  `play_time` datetime DEFAULT NULL COMMENT '场次时间',
  `ticket_price_id` bigint DEFAULT NULL COMMENT '票价ID',
  `ticket_price` double(8,2) DEFAULT '0.00' COMMENT '票价',
  `check_count` int DEFAULT '0' COMMENT '数量',
  `user_group_id` bigint DEFAULT NULL COMMENT '下单用户组ID',
  `user_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '下单用户组',
  `check_group_id` bigint DEFAULT NULL COMMENT '核销用户组ID',
  `check_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '核销用户组',
  `company_id` bigint NOT NULL COMMENT '企业ID',
  `show_id` bigint DEFAULT NULL COMMENT '场次ID',
  `show_name` varchar(100) DEFAULT NULL COMMENT '场次名称',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `tbs_user_id` bigint DEFAULT NULL COMMENT 'tbsUserId',
  `program_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA项目',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_ check_date` (`check_date`) USING BTREE,
  KEY `idx_stadium_id` (`stadium_id`) USING BTREE,
  KEY `idx_venue_id` (`venue_id`) USING BTREE,
  KEY `idx_program_id` (`program_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='座票入场汇总表（按天）';


CREATE TABLE `report_seat_discount_apportionment` (
  `id` varchar(35) NOT NULL,
  `discount_id` bigint DEFAULT NULL,
  `trade_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `uuid` varchar(16) DEFAULT NULL,
  `order_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `category` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `category_id` bigint DEFAULT NULL,
  `discount_amount` decimal(8,2) DEFAULT NULL,
  `description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `otherinfo` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `addtime` datetime DEFAULT NULL,
  `updatetime` datetime DEFAULT NULL,
  `company_id` bigint DEFAULT NULL,
  `add_user_id` bigint DEFAULT NULL,
  `user_group_id` bigint DEFAULT NULL,
  `rule_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `card_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `status` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `point_value` int DEFAULT '0',
  `cardtype` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `detail_count` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_trade_no` (`trade_no`) USING BTREE,
  KEY `idx_uuid` (`uuid`) USING BTREE,
  KEY `idx_addtime` (`addtime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单优惠分摊表';


CREATE TABLE `report_seat_sale_check_detail` (
     `id` varchar(20) NOT NULL COMMENT 'id',
     `trade_no` varchar(200) DEFAULT NULL COMMENT '订单号',
     `mobile` varchar(100) DEFAULT NULL COMMENT '手机号',
     `tickettime` datetime DEFAULT NULL COMMENT '出票/退票时间',
     `program_id` bigint DEFAULT NULL COMMENT '项目ID',
     `program_name` varchar(100) DEFAULT NULL COMMENT '项目名称',
     `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
     `stadium_name` varchar(50) DEFAULT NULL COMMENT '场馆名称',
     `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
     `venue_name` varchar(50) DEFAULT NULL COMMENT '场地名称',
     `show_id` bigint DEFAULT NULL COMMENT '场次ID',
     `show_name` varchar(100) DEFAULT NULL COMMENT '场次名称',
     `play_time` datetime DEFAULT NULL COMMENT '场次时间',
     `ticket_price_id` bigint DEFAULT NULL COMMENT '票价ID',
     `ticket_price` decimal(8,2) DEFAULT NULL COMMENT '座位售价',
     `venue_area_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区',
     `row_no` varchar(5) DEFAULT NULL COMMENT '行',
     `col_no` varchar(5) DEFAULT NULL COMMENT '列',
     `uuid` varchar(200) DEFAULT NULL COMMENT '票号',
     `real_name` varchar(50) DEFAULT NULL COMMENT '姓名',
     `certificate_type` varchar(50) DEFAULT NULL COMMENT '证件类型',
     `certificate_no` varchar(255) DEFAULT NULL COMMENT '证件号',
     `amount` double DEFAULT '0' COMMENT '金额',
     `discount` double DEFAULT '0' COMMENT '折扣',
     `paid_amount` double DEFAULT '0' COMMENT '实收金额',
     `paymethod` varchar(50) DEFAULT NULL COMMENT '支付方式',
     `logistics_mode` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物流方式',
     `ticket_message` varchar(2000) DEFAULT NULL COMMENT '票面附加信息',
     `logistics_address` varchar(255) DEFAULT NULL COMMENT '物流地址',
     `order_related_message` varchar(200) DEFAULT NULL COMMENT '订单关联信息',
     `description` varchar(200) DEFAULT NULL COMMENT '订单备注',
     `user_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '下单用户组',
     `user_name` varchar(50) DEFAULT NULL COMMENT '下单用户',
     `check_time` datetime DEFAULT NULL COMMENT '入场时间',
     `company_id` bigint NOT NULL COMMENT '企业ID',
     `updatetime` datetime NOT NULL COMMENT '更新时间',
     `pay_type` varchar(10) NOT NULL COMMENT '支付还是退款：pay/refund',
     `out_trade_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '外部订单号',
     `channel` varchar(100) DEFAULT NULL COMMENT '售票渠道',
     `contact_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机号',
     `ticket_user` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '购票用户',
     `payseqno` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '支付流水号',
     `volume` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场馆体量',
     `program_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA项目',
     `order_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单状态',
     `quantity` smallint DEFAULT NULL COMMENT '数量',
     `sell_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '出票类型',
     `category` varchar(15) DEFAULT NULL COMMENT '项目分类一级',
     `small_category` varchar(15) DEFAULT NULL COMMENT '项目分类二级',
     `settlement_amount` double(8,2) DEFAULT '0.00' COMMENT '结算金额',
     `print_num` smallint DEFAULT '0' COMMENT '打印次数',
     `print_time` datetime DEFAULT NULL COMMENT '打印时间',
     `customer_unit_name` varchar(50) DEFAULT NULL COMMENT '大客户单位名称',
     `card_no` varchar(6) DEFAULT NULL COMMENT '会员卡等级卡号',
     `member_id` bigint DEFAULT NULL COMMENT '会员ID',
     `customer_category` varchar(30) DEFAULT NULL COMMENT '大客户类型',
     `customer_id` bigint DEFAULT NULL COMMENT '大客户ID',
     `platform` varchar(15) DEFAULT NULL COMMENT '来源',
     `user_group_id` bigint DEFAULT NULL COMMENT '售票用户组ID',
     `member_level_id` bigint DEFAULT NULL,
     `member_level_name` varchar(40) DEFAULT NULL,
     `member_status` varchar(1) DEFAULT NULL COMMENT '会员状态（Y:激活,N:未开通会员,P:暂停状态）',
     `paidtime` datetime DEFAULT NULL COMMENT '支付时间',
     `add_user_id` bigint DEFAULT NULL,
     `city_code` varchar(10) DEFAULT NULL,
     `transport` varchar(20) DEFAULT NULL COMMENT '物流方式',
     `pack_trade_no` varchar(30) DEFAULT NULL COMMENT '套票订单号',
     `seat_attr` varchar(20) DEFAULT NULL COMMENT '座位属性',
     `organizer_type` varchar(30) DEFAULT NULL COMMENT '主办类型',
     `ordertime` datetime DEFAULT NULL COMMENT '下单时间',
     `gateway_code` varchar(20) DEFAULT NULL,
     `origin` varchar(20) DEFAULT NULL COMMENT '分销码',
     PRIMARY KEY (`id`) USING BTREE,
     KEY `idx_tickettime` (`tickettime`),
     KEY `idx_uuid` (`uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='座票销售明细表';


CREATE TABLE `report_seat_sale_gather_day` (
  `id` varchar(35) NOT NULL COMMENT 'id',
  `stadium_id` bigint NOT NULL COMMENT '场馆ID',
  `stadium_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '场馆名称',
  `venue_id` bigint NOT NULL COMMENT '场地ID',
  `venue_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '场地名称',
  `user_group_id` bigint NOT NULL COMMENT '用户组ID',
  `user_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户组名称',
  `program_id` bigint NOT NULL COMMENT '项目ID',
  `program_name` varchar(100) DEFAULT NULL COMMENT '项目名称',
  `show_id` bigint NOT NULL COMMENT '场次ID',
  `show_name` varchar(100) NOT NULL COMMENT '场次名次',
  `ticket_price_id` bigint NOT NULL COMMENT '票价ID',
  `ticket_price` double(8,2) DEFAULT NULL COMMENT '票价',
  `sale_count` int NOT NULL DEFAULT '0' COMMENT '销售数量',
  `amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额',
  `discount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `paid_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '实收金额',
  `refund_count` int NOT NULL DEFAULT '0' COMMENT '退票数量',
  `refund_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '退票金额',
  `refund_discount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '退票优惠金额',
  `refund_paid_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '退票实付金额',
  `total_quantity` int NOT NULL DEFAULT '0' COMMENT '数量合计',
  `total_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额合计',
  `total_discount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额合计',
  `total_paid_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '实收金额合计',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `ticket_date` date NOT NULL COMMENT '出票/退票日期',
  `company_id` bigint NOT NULL COMMENT '企业ID',
  `play_time` datetime NOT NULL COMMENT '场次时间',
  `program_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA项目',
  `city_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '城市名称',
  `category` varchar(25) DEFAULT NULL,
  `small_category` varchar(15) DEFAULT NULL COMMENT '项目分类二级',
  `sell_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '出票类型',
  `settlement_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '结算金额',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_ticket_date` (`ticket_date`) USING BTREE,
  KEY `idx_stadium_id` (`stadium_id`) USING BTREE,
  KEY `idx_venue_id` (`venue_id`) USING BTREE,
  KEY `idx_program_id` (`program_id`) USING BTREE,
  KEY `idx_show_id` (`show_id`) USING BTREE,
  KEY `idx_city_name` (`city_name`) USING BTREE,
  KEY `idx_program_code` (`program_code`) USING BTREE,
  KEY `idx_user_group_id` (`user_group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='座票销售汇总表（按天）';


CREATE TABLE `report_seat_sale_gather_program` (
  `id` varchar(35) NOT NULL,
  `stadium_id` bigint NOT NULL COMMENT '场馆ID',
  `stadium_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '场馆名称',
  `venue_id` bigint NOT NULL COMMENT '场地ID',
  `venue_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '场地名称',
  `user_group_id` bigint NOT NULL COMMENT '用户组ID',
  `user_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户组名称',
  `program_id` bigint NOT NULL COMMENT '项目ID',
  `program_name` varchar(100) DEFAULT NULL COMMENT '项目名称',
  `show_id` bigint NOT NULL COMMENT '场次ID',
  `show_name` varchar(100) NOT NULL COMMENT '场次名次',
  `ticket_price_id` bigint NOT NULL COMMENT '票价ID',
  `ticket_price` double(8,2) NOT NULL COMMENT '票价',
  `sale_count` int NOT NULL DEFAULT '0' COMMENT '销售数量',
  `amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额',
  `discount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `paid_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '实收金额',
  `refund_count` int NOT NULL DEFAULT '0' COMMENT '退票数量',
  `refund_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '退票金额',
  `refund_discount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '退票优惠金额',
  `refund_paid_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '退票实付金额',
  `total_quantity` int NOT NULL DEFAULT '0' COMMENT '数量合计',
  `total_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额合计',
  `total_discount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额合计',
  `total_paid_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '实收金额合计',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `ticket_date` date NOT NULL COMMENT '出票/退票日期',
  `company_id` bigint NOT NULL COMMENT '企业ID',
  `play_time` datetime NOT NULL COMMENT '场次时间',
  `program_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA项目',
  `city_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '城市名称',
  `category` varchar(25) DEFAULT NULL,
  `small_category` varchar(15) DEFAULT NULL COMMENT '项目分类二级',
  `sell_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '出票类型',
  `settlement_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '结算金额',
  `add_user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
  `real_name` varchar(30) DEFAULT NULL COMMENT '用户姓名',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_ticket_date` (`ticket_date`) USING BTREE,
  KEY `idx_stadium_id` (`stadium_id`) USING BTREE,
  KEY `idx_venue_id` (`venue_id`) USING BTREE,
  KEY `idx_program_id` (`program_id`) USING BTREE,
  KEY `idx_show_id` (`show_id`) USING BTREE,
  KEY `idx_city_name` (`city_name`) USING BTREE,
  KEY `idx_program_code` (`program_code`) USING BTREE,
  KEY `idx_user_group_id` (`user_group_id`) USING BTREE,
  KEY `idx_ play_time` (`play_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='座票销售汇总(按项目)';


CREATE TABLE `report_seat_sale_gather_seat_attr` (
     `id` varchar(35) NOT NULL,
     `program_id` bigint NOT NULL,
     `program_name` varchar(255) NOT NULL,
     `schedule_id` bigint NOT NULL,
     `play_time` timestamp NOT NULL,
     `remark` varchar(50) DEFAULT NULL,
     `price` decimal(8,2) NOT NULL,
     `description` varchar(50) DEFAULT NULL,
     `attr_id` varchar(20) DEFAULT NULL COMMENT '座位属性id',
     `attr` varchar(20) DEFAULT NULL,
     `total_num` int NOT NULL DEFAULT '0',
     `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
     `hold_num` int NOT NULL DEFAULT '0',
     `hold_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
     `plan_num` int NOT NULL DEFAULT '0',
     `plan_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
     `remaining_num` int NOT NULL DEFAULT '0',
     `remaining_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
     `reserve_num` int NOT NULL DEFAULT '0',
     `reserve_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
     `sale_num` int NOT NULL DEFAULT '0',
     `sale_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
     `lock_back_num` int DEFAULT '0',
     `lock_back_amount` decimal(12,2) DEFAULT '0.00',
     `ticket_price_id` bigint DEFAULT NULL,
     `company_id` bigint NOT NULL,
     `updatetime` timestamp NOT NULL,
     `stadium_id` bigint NOT NULL,
     `stadium_name` varchar(30) NOT NULL,
     `venue_id` bigint NOT NULL,
     `venue_name` varchar(30) NOT NULL,
     PRIMARY KEY (`id`),
     KEY `idx_schedule_id` (`schedule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='坐票票房汇总(按座位属性)';


CREATE TABLE `report_seat_sale_gather_venue_area` (
  `id` varchar(35) NOT NULL,
  `platform` varchar(15) NOT NULL,
  `trade_no` varchar(30) NOT NULL,
  `mobile` varchar(11) DEFAULT NULL,
  `member_id` bigint DEFAULT NULL,
  `tickettime` datetime DEFAULT NULL COMMENT '出票时间',
  `paidtime` datetime DEFAULT NULL COMMENT '支付时间',
  `program_id` bigint NOT NULL,
  `program_code` varchar(50) NOT NULL,
  `program_start_time` datetime NOT NULL COMMENT '项目开始时间',
  `program_end_time` datetime NOT NULL COMMENT '项目结束时间',
  `program_name` varchar(100) DEFAULT NULL COMMENT '项目中文名称',
  `stadium_id` bigint NOT NULL,
  `stadium_name` varchar(30) DEFAULT NULL,
  `venue_id` bigint NOT NULL,
  `venue_name` varchar(30) DEFAULT NULL,
  `show_id` bigint DEFAULT NULL COMMENT '场次ID',
  `show_name` varchar(50) DEFAULT NULL COMMENT '场次名称',
  `play_time` datetime NOT NULL,
  `venue_area_id` bigint DEFAULT NULL,
  `venue_area_name` varchar(30) DEFAULT NULL,
  `ticket_price_id` bigint DEFAULT NULL,
  `ticket_price_name` varchar(30) DEFAULT NULL,
  `ticket_price` decimal(8,2) NOT NULL DEFAULT '0.00',
  `row_nos` varchar(2000) DEFAULT NULL,
  `col_nos` varchar(2000) DEFAULT NULL,
  `refund_row_nos` varchar(2000) DEFAULT NULL,
  `refund_col_nos` varchar(2000) DEFAULT NULL,
  `sell_type` varchar(2) NOT NULL,
  `sale_count` int DEFAULT NULL,
  `amount` decimal(8,2) NOT NULL DEFAULT '0.00',
  `discount` decimal(8,2) NOT NULL DEFAULT '0.00',
  `paid_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `refund_count` int DEFAULT NULL,
  `refund_amount` decimal(8,2) NOT NULL DEFAULT '0.00',
  `refund_discount` decimal(8,2) NOT NULL DEFAULT '0.00',
  `refund_paid_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `total_quantity` int DEFAULT NULL,
  `total_amount` decimal(8,2) NOT NULL DEFAULT '0.00',
  `total_discount` decimal(8,2) NOT NULL DEFAULT '0.00',
  `total_paid_amount` decimal(8,2) NOT NULL COMMENT '支付金额',
  `settlement_amount` decimal(8,2) NOT NULL COMMENT '支付金额',
  `paymethod` varchar(20) DEFAULT NULL,
  `payseqno` varchar(40) DEFAULT NULL,
  `ticket_message` varchar(500) DEFAULT NULL,
  `order_related_message` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `pay_type` varchar(20) DEFAULT NULL,
  `add_user_id` bigint DEFAULT NULL,
  `user_name` varchar(100) NOT NULL,
  `user_group_id` bigint DEFAULT NULL,
  `user_group_name` varchar(100) NOT NULL,
  `out_trade_no` varchar(30) NOT NULL,
  `contact_mobile` varchar(20) DEFAULT NULL,
  `contact_name` varchar(30) DEFAULT NULL,
  `delivery_status` varchar(20) DEFAULT NULL,
  `delivery_company` varchar(64) DEFAULT NULL,
  `delivery_company_code` varchar(30) DEFAULT NULL COMMENT '物流公司编码',
  `delivery_sn` varchar(64) DEFAULT NULL COMMENT '物流单号',
  `delivery_address` json DEFAULT NULL,
  `company_id` bigint DEFAULT NULL,
  `updatetime` datetime DEFAULT NULL,
  `realname` varchar(40) DEFAULT NULL,
  `ticket_user` varchar(50) DEFAULT NULL,
  `member_level_id` bigint DEFAULT NULL,
  `member_level_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_program_id` (`program_id`),
  KEY `idx_stadium_id` (`stadium_id`),
  KEY `idx_venue_area_id` (`venue_area_id`),
  KEY `idx_venue_id` (`venue_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='选座订单明细报表（区域票种细分）';


    -- 该表对应实体类未被使用
CREATE TABLE `report_show_price_sale_day` (
  `id` varchar(64) NOT NULL,
  `stadium_id` varchar(50) DEFAULT NULL,
  `stadium_name` varchar(90) DEFAULT NULL COMMENT '场馆名称',
  `program_id` varchar(50) DEFAULT NULL,
  `program_name` varchar(100) DEFAULT NULL COMMENT '项目名称',
  `show_id` bigint DEFAULT NULL COMMENT '场次ID',
  `show_name` varchar(100) DEFAULT NULL COMMENT '场次名称',
  `ticket_price` decimal(8,2) DEFAULT NULL COMMENT '场次价格',
  `ticket_name` varchar(30) DEFAULT NULL COMMENT '票价名称',
  `settlement_price` decimal(8,2) DEFAULT NULL COMMENT '结算价格',
  `ticket_date` date DEFAULT NULL COMMENT '日期',
  `play_start_time` date DEFAULT NULL COMMENT '场次演出开始时间',
  `play_end_time` date DEFAULT NULL COMMENT '场次演出结束时间',
  `sale_count` int DEFAULT NULL COMMENT '销售数量',
  `refund_count` int DEFAULT NULL COMMENT '退款数量',
  `company_id` bigint DEFAULT NULL COMMENT '公司ID',
  `ticket_type_id` bigint DEFAULT NULL COMMENT '票种ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='站票场次价格统计（按天）';


CREATE TABLE `report_sport_check_detail` (
  `id` varchar(255) NOT NULL,
  `platform` varchar(255) NOT NULL,
  `tickettime` timestamp NULL DEFAULT NULL,
  `trade_no` varchar(255) NOT NULL,
  `payseqno` varchar(255) DEFAULT NULL,
  `out_trade_no` varchar(255) NOT NULL,
  `pay_status` varchar(255) NOT NULL,
  `mobile` varchar(255) DEFAULT NULL,
  `contact_mobile` varchar(255) DEFAULT NULL,
  `program_id` bigint NOT NULL,
  `program_name` varchar(255) NOT NULL,
  `stadium_id` bigint NOT NULL,
  `stadium_name` varchar(255) NOT NULL,
  `sport_court_id` bigint NOT NULL,
  `sport_court_name` varchar(255) NOT NULL,
  `sport_piece_id` bigint NOT NULL,
  `sport_piece_name` varchar(255) NOT NULL,
  `sport_piece_matrix_id` bigint NOT NULL,
  `play_date` date NOT NULL,
  `play_time` timestamp NOT NULL,
  `play_end_time` timestamp NOT NULL,
  `quantity` int NOT NULL DEFAULT '0',
  `ticket_price` double(8,2) NOT NULL DEFAULT '0.00',
  `discount` double(8,2) NOT NULL DEFAULT '0.00',
  `real_pay` double(8,2) NOT NULL DEFAULT '0.00',
  `settlement_amount` double(8,2) NOT NULL DEFAULT '0.00',
  `uuid` varchar(255) NOT NULL,
  `paymethod` varchar(255) DEFAULT NULL,
  `paidtime` timestamp NULL DEFAULT NULL,
  `user_id` bigint NOT NULL,
  `user_name` varchar(255) NOT NULL,
  `user_group_id` bigint NOT NULL,
  `user_group_name` varchar(255) NOT NULL,
  `updatetime` timestamp NOT NULL,
  `company_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_tickettime` (`tickettime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='体育订场订单明细表';


CREATE TABLE `report_sport_sale_gather` (
    `id` varchar(35) NOT NULL,
    `platform` varchar(255) NOT NULL,
    `ticket_date` timestamp NULL DEFAULT NULL,
    `program_id` bigint NOT NULL,
    `program_name` varchar(255) NOT NULL,
    `stadium_id` bigint NOT NULL,
    `stadium_name` varchar(255) NOT NULL,
    `court_id` bigint NOT NULL,
    `court_name` varchar(255) NOT NULL,
    `user_id` bigint NOT NULL,
    `user_name` varchar(255) NOT NULL,
    `user_group_id` bigint NOT NULL,
    `user_group_name` varchar(255) NOT NULL,
    `gateway_code` varchar(255) DEFAULT NULL,
    `updatetime` timestamp NOT NULL,
    `company_id` bigint NOT NULL,
    `sale_count` int NOT NULL DEFAULT '0',
    `amount` double(8,2) NOT NULL DEFAULT '0.00',
    `discount` double(8,2) NOT NULL DEFAULT '0.00',
    `paid_amount` double(8,2) NOT NULL DEFAULT '0.00',
    `refund_count` int NOT NULL DEFAULT '0',
    `refund_amount` double(8,2) NOT NULL DEFAULT '0.00',
    `refund_discount` double(8,2) NOT NULL DEFAULT '0.00',
    `refund_paid_amount` double(8,2) NOT NULL DEFAULT '0.00',
    `total_quantity` int NOT NULL DEFAULT '0',
    `total_amount` double(8,2) NOT NULL DEFAULT '0.00',
    `total_discount` double(8,2) NOT NULL DEFAULT '0.00',
    `total_paid_amount` double(8,2) NOT NULL DEFAULT '0.00',
    `settlement_amount` double(8,2) NOT NULL DEFAULT '0.00',
    PRIMARY KEY (`id`),
    KEY `idx_ticket_date` (`ticket_date`),
    KEY `idx_program_id` (`program_id`),
    KEY `idx_stadium_id` (`stadium_id`),
    KEY `idx_court_id` (`court_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='体育订场订单汇总表';


    -- 该表对应实体类未被使用
CREATE TABLE `report_stadium_sale_day` (
    `id` varchar(35) NOT NULL,
    `stadium_id` bigint NOT NULL COMMENT '场馆ID',
    `stadium_name` varchar(90) DEFAULT NULL COMMENT '场馆名称',
    `ticket_date` date DEFAULT NULL COMMENT '日期',
    `activate_card_count` int DEFAULT NULL COMMENT '开卡数量',
    `reserve_count` int DEFAULT NULL COMMENT '预约数量',
    `check_count` int DEFAULT NULL COMMENT '核销数量',
    `offline_sale_count` int DEFAULT NULL COMMENT '线下销售',
    `online_sale_count` int DEFAULT NULL COMMENT '线上销售',
    `online_refund_count` int DEFAULT NULL COMMENT '线上退单数量',
    `offline_refund_count` int DEFAULT NULL COMMENT '线下退单数量',
    `company_id` bigint DEFAULT NULL COMMENT '公司ID',
    PRIMARY KEY (`id`),
    KEY `idx_ticket_date` (`ticket_date`),
    KEY `idx_stadium_id` (`stadium_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='场管统计（按天）';


CREATE TABLE `report_stand_check` (
  `id` varchar(35) NOT NULL,
  `uuid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `device_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `check_count` int NOT NULL DEFAULT '0',
  `check_time` datetime NOT NULL COMMENT '检票时间',
  `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
  `stadium_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场馆名称',
  `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
  `venue_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场地名称',
  `program_id` bigint DEFAULT NULL COMMENT '项目ID',
  `program_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA项目',
  `program_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目名称',
  `ticket_type_id` bigint DEFAULT NULL COMMENT '票价ID',
  `ticket_price` double(8,2) DEFAULT '0.00' COMMENT '票价',
  `ticket_name` varchar(90) DEFAULT NULL COMMENT '票名称',
  `show_id` bigint DEFAULT NULL COMMENT '场次ID',
  `show_name` varchar(100) DEFAULT NULL COMMENT '场次名称',
  `play_time` datetime DEFAULT NULL COMMENT '场次时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `company_id` bigint NOT NULL COMMENT '企业ID',
  `check_user_group_id` bigint NOT NULL COMMENT '核销用户组ID',
  `check_user_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户组名称',
  `tbs_user_id` bigint DEFAULT NULL COMMENT '核销用户ID',
  `tbs_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户名称',
  `sale_user_id` bigint DEFAULT NULL COMMENT '销售用户ID',
  `sale_user_group_id` bigint DEFAULT NULL COMMENT '销售用户组ID',
  `sale_user_group_name` varchar(100) DEFAULT NULL COMMENT '销售用户组名称',
  `gateway_code` varchar(20) DEFAULT NULL COMMENT '支付方式',
  `trade_no` varchar(30) DEFAULT NULL COMMENT '订单号',
  `last_check_time` datetime DEFAULT NULL COMMENT '最后核销时间',
  `merchant_code`         varchar(20)               null,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_stadium_id` (`stadium_id`),
  KEY `idx_sale_user_id` (`sale_user_id`),
  KEY `idx_sale_user_group_id` (`sale_user_group_id`),
  KEY `idx_check_time` (`check_time`),
  KEY `idx_play_time` (`play_time`),
  KEY `idx_trade_no` (`trade_no`),
  KEY `idx_venue_id` (`venue_id`),
  KEY `idx_program_id` (`program_id`),
  KEY `idx_ticket_type_id` (`ticket_type_id`),
  KEY `idx_show_id` (`show_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='站票入场明细表';


CREATE TABLE `report_stand_check_detail` (
    `id` varchar(20) NOT NULL COMMENT 'id',
    `trade_no` varchar(200) DEFAULT NULL COMMENT '销售订单号',
    `mobile` varchar(100) DEFAULT NULL COMMENT '下单手机号',
    `tickettime` datetime DEFAULT NULL COMMENT '出票/退票时间',
    `program_name` varchar(100) DEFAULT NULL COMMENT '项目名称',
    `stadium_name` varchar(30) DEFAULT NULL COMMENT '场馆名称',
    `venue_name` varchar(30) DEFAULT NULL COMMENT '场地名称',
    `show_name` varchar(100) DEFAULT NULL COMMENT '场次名称',
    `play_time` datetime DEFAULT NULL COMMENT '场次时间',
    `ticket_type_name` varchar(30) DEFAULT NULL COMMENT '票种名称',
    `uuid` varchar(200) DEFAULT NULL COMMENT '票号',
    `entry_num` int DEFAULT '0' COMMENT '可入场人数',
    `real_name` varchar(40) DEFAULT NULL COMMENT '姓名',
    `certificate_type` varchar(30) DEFAULT NULL COMMENT '证件类型',
    `certificate_no` varchar(255) DEFAULT NULL COMMENT '证件号码',
    `ticket_price` decimal(8,2) DEFAULT NULL COMMENT '原价',
    `discount` decimal(8,2) DEFAULT '0.00' COMMENT '折扣',
    `real_pay` decimal(8,2) DEFAULT '0.00' COMMENT '实收金额',
    `paymethod` varchar(30) DEFAULT NULL COMMENT '支付方式',
    `logistics_mode` varchar(200) DEFAULT NULL COMMENT '物流方式',
    `logistics_address` varchar(200) DEFAULT NULL COMMENT '物流地址',
    `ticket_message` varchar(500) DEFAULT NULL,
    `order_related_message` varchar(255) DEFAULT NULL COMMENT '订单关联信息',
    `description` varchar(255) DEFAULT NULL COMMENT '订单备注',
    `user_group_name` varchar(100) DEFAULT NULL COMMENT '下单用户组',
    `user_name` varchar(100) DEFAULT NULL COMMENT '下单用户',
    `check_time` datetime DEFAULT NULL COMMENT '入场时间',
    `updatetime` datetime NOT NULL COMMENT '更新时间',
    `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
    `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
    `program_id` bigint DEFAULT NULL COMMENT '项目ID',
    `show_id` bigint DEFAULT NULL COMMENT '场次ID',
    `ticket_type_id` bigint DEFAULT NULL COMMENT '票种ID',
    `company_id` bigint NOT NULL COMMENT '企业ID',
    `pay_type` varchar(10) NOT NULL COMMENT '支付还是退款：pay/refund',
    `out_trade_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '外部订单号',
    `channel` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '售票渠道',
    `contact_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机号',
    `ticket_user` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '购票用户',
    `payseqno` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '支付流水号',
    `volume` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场馆体量',
    `program_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA项目',
    `order_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '订单状态',
    `quantity` smallint DEFAULT NULL COMMENT '数量',
    `sell_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '出票类型',
    `category` varchar(15) DEFAULT NULL COMMENT '项目分类一级',
    `small_category` varchar(15) DEFAULT NULL COMMENT '项目分类二级',
    `origin` varchar(40) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '分销码',
    `settlement_amount` double(8,2) DEFAULT '0.00' COMMENT '结算金额',
    `platform` varchar(15) DEFAULT NULL COMMENT '来源',
    `user_group_id` bigint DEFAULT NULL COMMENT '售票用户组ID',
    `sale_user_id` bigint DEFAULT NULL COMMENT '售票用户ID',
    `barcode` varchar(10) DEFAULT NULL COMMENT '票号',
    `unit_name` varchar(50) DEFAULT NULL COMMENT '大客户名称',
    `paidtime` datetime DEFAULT NULL COMMENT '支付时间',
    `print_num` smallint DEFAULT '0' COMMENT '打印次数',
    `print_time` datetime DEFAULT NULL COMMENT '打印时间',
    `transport` varchar(20) DEFAULT NULL COMMENT '物流方式',
    `member_id` bigint DEFAULT NULL,
    `organizer_type` varchar(30) DEFAULT NULL COMMENT '主办类型',
    `amount` decimal(8,2) DEFAULT NULL COMMENT '票金额',
    `card_no` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '会员卡等级卡号',
    `member_level_id` bigint DEFAULT NULL,
    `member_level_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `member_status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '会员状态（Y:激活,N:未开通会员,P:暂停状态）',
    `ordertime` datetime DEFAULT NULL COMMENT '下单时间',
    `gateway_code` varchar(20) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_tickettime` (`tickettime`),
    KEY `idx_uuid` (`uuid`) USING BTREE,
    KEY `idx_play_time` (`play_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='站票销售明细表';


CREATE TABLE `report_stand_check_gather` (
     `id` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'id',
     `check_date` date DEFAULT NULL COMMENT '核销日期',
     `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
     `stadium_name` varchar(100) DEFAULT NULL COMMENT '场馆名称',
     `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
     `venue_name` varchar(100) DEFAULT NULL COMMENT '场地名称',
     `program_id` bigint DEFAULT NULL COMMENT '项目ID',
     `program_name` varchar(100) DEFAULT NULL COMMENT '项目名称',
     `play_time` datetime DEFAULT NULL COMMENT '场次时间',
     `ticket_type_id` bigint DEFAULT NULL COMMENT '票种ID',
     `ticket_type_name` varchar(100) DEFAULT NULL COMMENT '票种名称',
     `check_count` int DEFAULT '0' COMMENT '入场数',
     `ticket_num` int DEFAULT '0' COMMENT '票张数',
     `user_group_id` bigint DEFAULT NULL COMMENT '下单用户组ID',
     `user_group_name` varchar(100) DEFAULT NULL COMMENT '下单用户组',
     `check_group_id` bigint DEFAULT NULL COMMENT '核销用户组ID',
     `check_group_name` varchar(100) DEFAULT NULL COMMENT '核销用户组',
     `company_id` bigint NOT NULL COMMENT '企业ID',
     `show_id` bigint DEFAULT NULL COMMENT '场次ID',
     `show_name` varchar(100) DEFAULT NULL COMMENT '场次名称',
     `updatetime` datetime NOT NULL COMMENT '更新时间',
     `tbs_user_id` bigint DEFAULT NULL COMMENT 'tbsUserId',
     `program_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA项目',
     `sale_user_id` bigint DEFAULT NULL COMMENT '销售用户ID',
     `reserve_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '预约类型',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `report_stand_check_gather_ticket` (
    `id` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'id',
    `check_date` date DEFAULT NULL COMMENT '核销日期',
    `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
    `stadium_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场馆名称',
    `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
    `venue_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场地名称',
    `program_id` bigint DEFAULT NULL COMMENT '项目ID',
    `program_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目名称',
    `play_time` datetime DEFAULT NULL COMMENT '场次时间',
    `ticket_type_id` bigint DEFAULT NULL COMMENT '票种ID',
    `ticket_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '票种名称',
    `check_count` int DEFAULT '0' COMMENT '入场数',
    `ticket_num` int DEFAULT '0' COMMENT '票张数',
    `user_group_id` bigint DEFAULT NULL COMMENT '下单用户组ID',
    `user_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '下单用户组',
    `check_group_id` bigint DEFAULT NULL COMMENT '核销用户组ID',
    `check_group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '核销用户组',
    `company_id` bigint NOT NULL COMMENT '企业ID',
    `show_id` bigint DEFAULT NULL COMMENT '场次ID',
    `show_name` varchar(100) DEFAULT NULL COMMENT '场次名称',
    `updatetime` datetime NOT NULL COMMENT '更新时间',
    `tbs_user_id` bigint DEFAULT NULL COMMENT 'tbsUserId',
    `program_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA项目',
    `sale_user_id` bigint DEFAULT NULL COMMENT '销售用户ID',
    `reserve_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '预约类型',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='展览活动入场汇总表（按票汇总）';


CREATE TABLE `report_stand_discount_apportionment` (
   `id` varchar(35) NOT NULL,
   `discount_id` bigint DEFAULT NULL,
   `trade_no` varchar(30) DEFAULT NULL,
   `uuid` varchar(16) DEFAULT NULL,
   `order_type` varchar(10) DEFAULT NULL,
   `category` varchar(20) DEFAULT NULL,
   `category_id` bigint DEFAULT NULL,
   `discount_amount` decimal(8,2) DEFAULT NULL,
   `description` varchar(50) DEFAULT NULL,
   `otherinfo` varchar(200) DEFAULT NULL,
   `addtime` datetime DEFAULT NULL,
   `updatetime` datetime DEFAULT NULL,
   `company_id` bigint DEFAULT NULL,
   `add_user_id` bigint DEFAULT NULL,
   `user_group_id` bigint DEFAULT NULL,
   `rule_name` varchar(30) DEFAULT NULL,
   `card_no` varchar(32) DEFAULT NULL,
   `status` varchar(15) DEFAULT NULL,
   `point_value` int DEFAULT '0',
   `cardtype` varchar(15) DEFAULT NULL,
   `detail_count` int DEFAULT '1',
   PRIMARY KEY (`id`),
   KEY `idx_addtime` (`addtime`),
   KEY `idx_trade_no` (`trade_no`),
   KEY `idx_uuid` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单优惠分摊表';


CREATE TABLE `report_stand_sale_check_gather` (
  `id` varchar(35) NOT NULL COMMENT 'id',
  `stadium_id` bigint NOT NULL COMMENT '场馆ID',
  `stadium_name` varchar(50) NOT NULL COMMENT '场馆名称',
  `venue_id` bigint NOT NULL COMMENT '场地ID',
  `venue_name` varchar(50) NOT NULL COMMENT '场地名称',
  `user_group_id` bigint NOT NULL COMMENT '用户组ID',
  `user_group_name` varchar(100) NOT NULL COMMENT '用户组名称',
  `program_id` bigint NOT NULL COMMENT '项目ID',
  `program_name` varchar(100) NOT NULL COMMENT '项目名称',
  `show_id` bigint NOT NULL COMMENT '场次ID',
  `show_name` varchar(100) NOT NULL COMMENT '场次名次',
  `ticket_type_id` bigint NOT NULL COMMENT '票种ID',
  `ticket_type_name` varchar(50) NOT NULL COMMENT '票种名称',
  `ticket_price` double(8,2) NOT NULL COMMENT '票价',
  `sale_count` int NOT NULL DEFAULT '0' COMMENT '销售数量',
  `amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额',
  `discount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `paid_amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '实收金额',
  `refund_count` int NOT NULL DEFAULT '0' COMMENT '退票数量',
  `refund_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '退票金额',
  `refund_discount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '退票优惠金额',
  `refund_paid_amount` double(8,2) NOT NULL DEFAULT '0.00' COMMENT '退票实付金额',
  `total_quantity` int NOT NULL DEFAULT '0' COMMENT '数量合计',
  `total_amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额合计',
  `total_discount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额合计',
  `total_paid_amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '实收金额合计',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `ticket_date` date NOT NULL COMMENT '出票/退票日期',
  `company_id` bigint NOT NULL COMMENT '企业ID',
  `play_time` datetime NOT NULL COMMENT '场次时间',
  `program_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA项目',
  `city_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '城市名称',
  `category` varchar(15) DEFAULT NULL COMMENT '项目分类一级',
  `small_category` varchar(15) DEFAULT NULL COMMENT '项目分类二级',
  `sell_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '出票类型',
  `settlement_amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '结算金额',
  `add_user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
  `real_name` varchar(30) DEFAULT NULL COMMENT '用户姓名',
  `gateway_code` varchar(20) DEFAULT NULL COMMENT '支付方式',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_ticket_date` (`ticket_date`),
  KEY `idx_stadium_id` (`stadium_id`),
  KEY `idx_venue_id` (`venue_id`),
  KEY `idx_program_id` (`program_id`),
  KEY `idx_show_id` (`show_id`),
  KEY `idx_ticket_type_id` (`ticket_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `report_stand_sale_gather` (
    `id` varchar(35) NOT NULL COMMENT 'id',
    `stadium_id` bigint NOT NULL COMMENT '场馆ID',
    `stadium_name` varchar(50) NOT NULL COMMENT '场馆名称',
    `venue_id` bigint NOT NULL COMMENT '场地ID',
    `venue_name` varchar(50) NOT NULL COMMENT '场地名称',
    `user_group_id` bigint NOT NULL COMMENT '用户组ID',
    `user_group_name` varchar(100) NOT NULL COMMENT '用户组名称',
    `program_id` bigint NOT NULL COMMENT '项目ID',
    `program_name` varchar(100) DEFAULT NULL COMMENT '项目名称',
    `show_id` bigint NOT NULL COMMENT '场次ID',
    `show_name` varchar(100) NOT NULL COMMENT '场次名次',
    `ticket_type_id` bigint NOT NULL COMMENT '票种ID',
    `ticket_type_name` varchar(50) NOT NULL COMMENT '票种名称',
    `ticket_price` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '票价',
    `sale_count` int NOT NULL DEFAULT '0' COMMENT '销售数量',
    `amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额',
    `discount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
    `paid_amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '实收金额',
    `refund_count` int NOT NULL DEFAULT '0' COMMENT '退票数量',
    `refund_amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '退票金额',
    `refund_discount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '退票优惠金额',
    `refund_paid_amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '退票实付金额',
    `total_quantity` int NOT NULL DEFAULT '0' COMMENT '数量合计',
    `total_amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额合计',
    `total_discount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额合计',
    `total_paid_amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '实收金额合计',
    `updatetime` datetime NOT NULL COMMENT '更新时间',
    `ticket_date` date NOT NULL COMMENT '出票/退票日期',
    `company_id` bigint NOT NULL COMMENT '企业ID',
    `play_time` datetime NOT NULL COMMENT '场次时间',
    `program_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA项目',
    `city_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '城市名称',
    `category` varchar(15) DEFAULT NULL COMMENT '项目分类一级',
    `small_category` varchar(15) DEFAULT NULL COMMENT '项目分类二级',
    `sell_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '出票类型',
    `settlement_amount` double(14,2) NOT NULL DEFAULT '0.00' COMMENT '结算金额',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_ticket_date` (`ticket_date`),
    KEY `idx_stadium_id` (`stadium_id`),
    KEY `idx_venue_id` (`venue_id`),
    KEY `idx_program_id` (`program_id`),
    KEY `idx_show_id` (`show_id`),
    KEY `idx_ticket_type_id` (`ticket_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `report_ticket_transaction_summary` (
    `id` varchar(35) NOT NULL,
    `uuid` varchar(100) DEFAULT NULL,
    `order_detail_id` bigint DEFAULT NULL COMMENT '订单ID',
    `trade_no` varchar(30) DEFAULT NULL COMMENT '订单号',
    `paid_amount` decimal(8,2) DEFAULT NULL COMMENT '支付金额',
    `program_id` bigint DEFAULT NULL COMMENT '项目ID',
    `program_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目名称',
    `user_group_id` bigint DEFAULT NULL COMMENT '用户组ID',
    `group_name` varchar(30) DEFAULT NULL COMMENT '用户组名称',
    `schedule_id` bigint DEFAULT NULL COMMENT '场次ID',
    `schedule_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场次名称',
    `paidtime` datetime DEFAULT NULL COMMENT '支付时间',
    `sale_date` date DEFAULT NULL COMMENT '销售日期',
    `updatetime` datetime NOT NULL COMMENT '更新时间',
    `organizer_type` varchar(30) DEFAULT NULL COMMENT '主办类型',
    `ticket_type` varchar(30) DEFAULT NULL COMMENT 'show 站票 schedule 座票',
    `search_time` datetime DEFAULT NULL COMMENT '查询时间',
    `schedule_time` datetime DEFAULT NULL COMMENT '场次时间',
    `stadium_name` varchar(30) DEFAULT NULL,
    `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
    `venue_name` varchar(30) DEFAULT NULL,
    `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
    `source` varchar(30) DEFAULT NULL COMMENT '来源',
    `gateway_code` varchar(20) DEFAULT NULL COMMENT '支付方式',
    `pay_status` varchar(20) DEFAULT NULL COMMENT '状态',
    `company_id` bigint NOT NULL COMMENT '企业ID',
    `pay_type` varchar(15) DEFAULT NULL COMMENT '支付类型  pay 支付  refund 退款',
    `add_user_id` bigint DEFAULT NULL,
    `add_user_name` varchar(100) DEFAULT NULL,
    `merchant_code` varchar(20) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_program_id` (`program_id`),
    KEY `idx_uuid` (`uuid`),
    KEY `idx_search_time` (`search_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='票款交易汇总表';


CREATE TABLE `report_ticket_transaction_summary_comb_pay` (
  `id` varchar(50) NOT NULL,
  `trade_no` varchar(30) DEFAULT NULL COMMENT '订单号',
  `paid_amount` decimal(8,2) DEFAULT NULL COMMENT '支付金额',
  `program_id` bigint DEFAULT NULL COMMENT '项目ID',
  `program_name` varchar(200) DEFAULT NULL COMMENT '项目名称',
  `user_group_id` bigint DEFAULT NULL COMMENT '用户组ID',
  `group_name` varchar(30) DEFAULT NULL COMMENT '用户组名称',
  `schedule_id` bigint DEFAULT NULL COMMENT '场次ID',
  `schedule_name` varchar(100) DEFAULT NULL COMMENT '场次名称',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `time` datetime DEFAULT NULL COMMENT '查询时间',
  `schedule_time` datetime DEFAULT NULL COMMENT '场次时间',
  `stadium_name` varchar(30) DEFAULT NULL,
  `stadium_id` bigint DEFAULT NULL COMMENT '场馆ID',
  `venue_name` varchar(30) DEFAULT NULL,
  `venue_id` bigint DEFAULT NULL COMMENT '场地ID',
  `ticket_type` varchar(30) DEFAULT NULL COMMENT 'show 站票 schedule 座票',
  `gateway_code` varchar(20) DEFAULT NULL COMMENT '支付方式',
  `company_id` bigint NOT NULL COMMENT '企业ID',
  `pay_type` varchar(15) DEFAULT NULL COMMENT '支付类型  pay 支付  refund 退款',
  `add_user_id` bigint DEFAULT NULL,
  `add_user_name` varchar(100) DEFAULT NULL,
  `merchant_code` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_program_id` (`program_id`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_time` (`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='票款交易聚合表';
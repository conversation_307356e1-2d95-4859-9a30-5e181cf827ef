# antank 标准报表

## 1.预约及入场明细表

- a.主表：order_visitor_info
- b.来源：按照order_visitor_info的updatetime进行更新时间增量查询

```sql
select t0.reserve_no as reserveNo,
               t1.reserve_program_name as programName,
               t0.reservedate as reservedate,
               t2.starttime as starttime,
               t2.endtime as endtime,
               t0.realname as realName,
               t0.certificate_type as certificateType,
               t0.certificate_no as certificateNo,
               t1.member_mobile as mobile,
               t0.addtime as addtime,
               t0.status as status,
               t0.id as id,
               t1.reserve_program_id,
               t3.stadium_id,
               t3.venue_id,
               t0.company_id as companyId,
               t0.check_time
        from order_visitor_info t0
            left join reserve_order t1 on t1.reserve_no = t0.reserve_no
            left join reserve_period t2 on t2.id = t0.reserve_period_id
            left join reserve_program t3 on t3.id = t1.reserve_program_id
        where t0.updatetime &gt;= #{startTime}
             and t0.updatetime &lt;  #{endTime}
```

## 2.预约及入场汇总表

- a.主表：order_visitor_info
- b. 根据时间进行批量更新： t0.reservedate in(SELECT DISTINCT reservedate from order_visitor_info where updatetime &gt;= #{startTime}
- c. 状态: t0.status != 'N'

```sql
  SELECT
        t0.reservedate AS reservedate,
        t3.stadium_name AS stadiumName,
        t3.venue_name AS venueName,
        t1.reserve_program_name AS programName,
        CONCAT(date_format(t2.starttime,'%H:%i:%s'),"-",date_format(t2.endtime,'%H:%i:%s')) AS reserve_time,
        count(1) as reserveGather,
        count(t0.uuid) as checkGather,
        t1.reserve_program_id,
        t3.stadium_id,
        t3.venue_id,
        t0.company_id as companyId
        FROM
        order_visitor_info t0
        LEFT JOIN reserve_order t1 ON t1.reserve_no = t0.reserve_no
        LEFT JOIN reserve_period t2 ON t2.id = t0.reserve_period_id
        LEFT JOIN reserve_program t3 ON t3.id = t1.reserve_program_id
        WHERE
        t0.reservedate in(SELECT DISTINCT reservedate from order_visitor_info where updatetime &gt;= #{startTime}
        and updatetime &lt; #{endTime})
        AND t0.status != 'N'
        GROUP BY t1.reserve_program_id,t0.reservedate,t2.starttime,t2.endtime,
        t3.stadium_name,t3.venue_name,t1.reserve_program_name,
        t3.stadium_id,t3.venue_id,t0.company_id
        ORDER BY t0.reservedate,t2.starttime,t2.endtime
```

## 3.站票销售及入场明细表

### 3.1 销售

- a.主表: show_order_detail
- b. 根据时间进行批量更新：t0.updatetime &gt;= #{startTime} and t0.updatetime &lt;  #{endTime}
- c.状态：  show_order_detail.t0.pay_status like 'paid%' and t0.status != 'T'

```sql
select t0.trade_no           as tradeNo,
       t1.mobile             as mobile,
       t1.tickettime         as tickettime,
       t1.program_id         as programId,
       t2.cn_name            as programName,
       t1.stadium_id         as stadiumId,
       t3.cn_name            as stadiumName,
       t2.venue_id           as venueId,
       t4.cn_name            as venueName,
       t1.show_id            as showId,
       t5.cn_name            as showName,
       t5.play_time          as playTime,
       t0.ticket_type_id     as ticketTypeId,
       t6.cn_name            as ticketTypeName,
       t0.uuid               as uuid,
       t6.entrynum           as entryNum,
       t0.realname           as realName,
       t0.certificate_type   as certificateType,
       t0.certificate_no     as certificateNo,
       t0.ticket_price       as ticketPrice,
       t0.discount           as discount,
       t0.paid_amount        as realPay,
       t0.gateway_code       as payMethod,
       ''                    as logisticsMode,
       ''                    as logisticsAddress,
       ''                    as ticketMessage,
       ''                    as orderRelatedMessage,
       ''                    as description,
       t7.group_name         as userGroupName,
       t8.username           as userName,
       concat('sale', t0.id) as id,
       'pay'                 as payType,
       t0.company_id         as companyId,
       t0.check_time         as checkTime
from show_order_detail t0
         left join show_order t1 on t1.trade_no = t0.trade_no
         left join program t2 on t2.id = t1.program_id
         left join stadium t3 on t3.id = t1.stadium_id
         left join venue t4 on t4.id = t2.venue_id
         left join open_show t5 on t5.id = t1.show_id
         left join ticket_type t6 on t6.id = t0.ticket_type_id
         left join user_group t7 on t7.id = t1.user_group_id
         left join tbs_user t8 on t8.id = t0.sale_user_id
where t0.pay_status like 'paid%'
  and t0.updatetime >= '2022-05-01'
  and t0.updatetime < '2022-05-11';
```

### 3.2 退款

- a.主表：order_refund_detail
- b.根据时间进行批量更新： and t0.updatetime &gt;= #{startTime}
  and t0.updatetime &lt; #{endTime}
- c.状态：t1.order_type = 'show' and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success')

```sql
select t0.refund_serial_no as tradeNo,
               t3.mobile as mobile,
               t0.tickettime as tickettime,
               t3.program_id as programId,
               t4.cn_name as programName,
               t1.stadium_id as stadiumId,
               t5.cn_name as stadiumName,
               t4.venue_id as venueId,
               t6.cn_name as venueName,
               t3.show_id as showId,
               t7.cn_name as showName,
               t7.play_time as playTime,
               t2.ticket_type_id as ticketTypeId,
               t8.cn_name as ticketTypeName,
               t0.uuid as uuid,
               -t8.entrynum as entryNum,
               t2.realname as realName,
               t2.certificate_type as certificateType,
               t2.certificate_no as certificateNo,
               t2.ticket_price as ticketPrice,
               t2.discount as discount,
               -t0.real_pay as realPay,
               t0.gateway_code as paymethod,
               t9.group_name as userGroupName,
               t10.username as userName,
               '' as logisticsMode,
               '' as logisticsAddress,
               '' as ticketMessage,
               '' as orderRelatedMessage,
               '' as description,
               concat('refund',t0.id) as id,
               'refund'               as payType,
               t0.company_id as companyId,
               t2.check_time as checkTime
        from order_refund_detail t0
                 left join order_refund t1 on t1.serial_no = t0.refund_serial_no
                 left join show_order_detail t2 on t2.uuid = t0.uuid
                 left join show_order t3 on t3.trade_no = t1.trade_no
                 left join program t4 on t4.id = t3.program_id
                 left join stadium t5 on t5.id = t1.stadium_id
                 left join venue t6 on t6.id = t4.venue_id
                 left join open_show t7 on t7.id = t3.show_id
                 left join ticket_type t8 on t8.id = t2.ticket_type_id
                 left join user_group t9 on t9.id = t0.user_group_id
                 left join tbs_user t10 on t10.id = t0.sale_user_id
        where t1.order_type = 'show'
        and t1.origin_status like 'paid%'
        and t0.updatetime &gt;= #{startTime}
        and t0.updatetime &lt; #{endTime}
        and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success')
```

### 3.3 检票

```sql
select uuid, check_time
from check_record
where reserve_type = 'show_check'
  and updatetime >= ''
  and updatetime < '';
```

## 4 站票销售汇总表

### 4.1 变更日期

```sql
select distinct date(tickettime) as ticketdate
from show_order
where pay_status like 'paid%'
  and updatetime >= '2022-04-01'
  and updatetime < '2022-05-11'
union
select distinct date(ord.tickettime) as ticketdate
from order_refund_detail ord
         left join order_refund o on ord.refund_serial_no = o.serial_no
where o.order_type = 'show'
  and o.origin_status like 'paid%'
  and ord.updatetime >= '2022-04-01'
  and ord.updatetime < '2022-05-11'
  and ord.refund_status in ('ticket_success', 'refund_apply', 'refund_success')
```

### 4.2 销售

- a.主表：show_order_detail
- b.根据时间进行批量更新：date(t1.tickettime) in (select date(tickettime) from show_order where updatetime &gt;= #{startTime} and updatetime &lt; #{endTime} and pay_status like 'paid%')
- c.状态： and t0.pay_status like 'paid%' and t0.status != 'T'

```sql
select t0.stadium_id        as stadiumId,
       t3.cn_name           as stadiumName,
       t2.venue_id          as venueId,
       t4.cn_name           as venueName,
       t1.user_group_id     as userGroupId,
       t7.group_name        as userGroupName,
       t1.program_id        as programId,
       t2.cn_name           as programName,
       t0.ticket_type_id    as ticketTypeId,
       t6.cn_name              ticketTypeName,
       t1.show_id           as showId,
       t5.cn_name           as showName,
       t0.company_id        as companyId,
       date(t1.tickettime)  as ticketDate,
       t5.play_time         as playTime,
       count(1)             as saleCount,
       sum(t0.ticket_price) as amount,
       sum(t0.discount)     as discount,
       sum(t0.paid_amount)  as paidAmount
from show_order_detail t0
         left join show_order t1 on t0.trade_no = t1.trade_no
         left join program t2 on t2.id = t1.program_id
         left join stadium t3 on t3.id = t1.stadium_id
         left join venue t4 on t4.id = t2.venue_id
         left join open_show t5 on t5.id = t1.show_id
         left join ticket_type t6 on t6.id = t0.ticket_type_id
         left join user_group t7 on t7.id = t1.user_group_id
where t0.pay_status like 'paid%'
  and t1.tickettime >= '2022-04-01'
  and t1.tickettime < '2022-05-01'
GROUP BY t0.stadium_id, t2.venue_id, t1.user_group_id, t1.program_id, t0.ticket_type_id,
         t3.cn_name, t4.cn_name, t7.group_name, t2.cn_name, t6.cn_name,
         t1.show_id, t5.cn_name, date(t1.tickettime), t0.company_id, t5.play_time
```

### 4.3 退款

- a.主表：order_refund_detail
- b.根据时间进行批量更新：  and t0.tickettime in (select tickettime from order_refund_detail where updatetime &gt;= #{startTime} and updatetime &lt; #{endTime}
  and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success'))
- c.状态：t1.order_type = 'show'
  and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success')

```sql
select t1.stadium_id         as stadiumId,
       t5.cn_name            as stadiumName,
       t4.venue_id           as venueId,
       t6.cn_name            as venueName,
       t0.user_group_id      as userGroupId,
       t9.group_name         as userGroupName,
       t3.program_id         as programId,
       t4.cn_name            as programName,
       t2.ticket_type_id     as ticketTypeId,
       t8.cn_name            as ticketTypeName,
       t3.show_id            as showId,
       t7.cn_name            as showName,
       date(t0.tickettime)   as ticketDate,
       t0.company_id         as companyId,
       t7.play_time          as playTime,
       -count(1)             as refundCount,
       -sum(t0.ticket_price) as refundAmount,
       -sum(t2.discount)     as refundDiscount,
       -sum(t0.real_pay)     as refundPaidAmount
from order_refund_detail t0
         left join order_refund t1 on t1.serial_no = t0.refund_serial_no
         left join show_order_detail t2 on t2.uuid = t0.uuid
         left join show_order t3 on t3.trade_no = t1.trade_no
         left join program t4 on t4.id = t3.program_id
         left join stadium t5 on t5.id = t1.stadium_id
         left join venue t6 on t6.id = t4.venue_id
         left join open_show t7 on t7.id = t3.show_id
         left join ticket_type t8 on t8.id = t2.ticket_type_id
         left join user_group t9 on t9.id = t0.user_group_id
where t1.order_type = 'show'
  and t1.origin_status like 'paid%'
  and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success')
  and t0.tickettime >= '2022-04-01'
  and t0.tickettime < '2022-05-11'
group by t1.stadium_id, t5.cn_name, t4.venue_id, t6.cn_name, t0.user_group_id, t9.group_name, t3.program_id, t4.cn_name,
         t2.ticket_type_id, t8.cn_name, t3.show_id, t7.cn_name, date(t0.tickettime), t0.company_id, t7.play_time
```

## 5 站票销售汇总表（按天）

### 5.1 变更日期

```sql
select distinct date(tickettime) as ticketdate
from show_order
where pay_status like 'paid%'
  and updatetime >= '2022-04-01'
  and updatetime < '2022-05-11'
union
select distinct date(ord.tickettime) as ticketdate
from order_refund_detail ord
         left join order_refund o on ord.refund_serial_no = o.serial_no
where o.order_type = 'show'
  and o.origin_status like 'paid%'
  and ord.updatetime >= '2022-04-01'
  and ord.updatetime < '2022-05-11'
  and ord.refund_status in ('ticket_success', 'refund_apply', 'refund_success')
```

- 根据日期轮询统计

```sql
select t0.stadium_id        as stadiumId,
       t3.cn_name           as stadiumName,
       t2.venue_id          as venueId,
       t4.cn_name           as venueName,
       t1.user_group_id     as userGroupId,
       t7.group_name        as userGroupName,
       t1.program_id        as programId,
       t2.cn_name           as programName,
       t0.ticket_type_id    as ticketTypeId,
       t6.cn_name              ticketTypeName,
       t1.show_id           as showId,
       t5.cn_name           as showName,
       t0.company_id        as companyId,
       t5.play_time         as playTime,
       date(t1.tickettime)  as ticketDate,
       count(1)             as saleCount,
       sum(t0.ticket_price) as amount,
       sum(t0.discount)     as discount,
       sum(t0.paid_amount)  as paidAmount
from show_order_detail t0
         left join show_order t1 on t0.trade_no = t1.trade_no
         left join program t2 on t2.id = t1.program_id
         left join stadium t3 on t3.id = t1.stadium_id
         left join venue t4 on t4.id = t2.venue_id
         left join open_show t5 on t5.id = t1.show_id
         left join ticket_type t6 on t6.id = t0.ticket_type_id
         left join user_group t7 on t7.id = t1.user_group_id
where t1.tickettime >= ''
  and t1.tickettime < ''
  and t0.pay_status like 'paid%'
GROUP BY t0.stadium_id, t2.venue_id, t1.user_group_id, t1.program_id, t0.ticket_type_id,
         t3.cn_name, t4.cn_name, t7.group_name, t2.cn_name, t6.cn_name, t0.ticket_type_id, t1.show_id,
         t5.cn_name, date(t1.tickettime), t0.company_id, t5.play_time
```

### 5.2退款

- a.主表：order_refund_detail
- b.根据时间进行批量更新：t0.tickettime in (select distinct tickettime from order_refund_detail where updatetime &gt;= #{startTime} and updatetime &lt; #{endTime}
  and refund_status in ('ticket_success', 'refund_apply', 'refund_success'))
- c.状态：t1.order_type = 'show'
  and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success')

```sql
select t1.stadium_id         as stadiumId,
       t5.cn_name            as stadiumName,
       t4.venue_id           as venueId,
       t6.cn_name            as venueName,
       t0.user_group_id      as userGroupId,
       t9.group_name         as userGroupName,
       t3.program_id         as programId,
       t4.cn_name            as programName,
       t2.ticket_type_id     as ticketTypeId,
       t8.cn_name            as ticketTypeName,
       t3.show_id            as showId,
       t7.cn_name            as showName,
       t0.company_id         as companyId,
       t7.play_time          as playTime,
       date(t0.tickettime)   as ticketDate,
       -count(1)             as refundCount,
       -sum(t0.ticket_price) as refundAmount,
       -sum(t2.discount)     as refundDiscount,
       -sum(t0.real_pay)     as refundPaidAmount
from order_refund_detail t0
         left join order_refund t1 on t1.serial_no = t0.refund_serial_no
         left join show_order_detail t2 on t2.uuid = t0.uuid
         left join show_order t3 on t3.trade_no = t1.trade_no
         left join program t4 on t4.id = t3.program_id
         left join stadium t5 on t5.id = t1.stadium_id
         left join venue t6 on t6.id = t4.venue_id
         left join open_show t7 on t7.id = t3.show_id
         left join ticket_type t8 on t8.id = t2.ticket_type_id
         left join user_group t9 on t9.id = t0.user_group_id
where t1.order_type = 'show'
  and t1.origin_status like 'paid%'
  and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success')
  and t0.tickettime >= ''
  and t0.tickettime < ''
group by t1.stadium_id, t5.cn_name, t4.venue_id, t6.cn_name, t0.user_group_id, t9.group_name, t3.program_id, t4.cn_name,
         t2.ticket_type_id, t8.cn_name, t3.show_id, t7.cn_name, date(t0.tickettime), t0.company_id, t7.play_time
```

## 6 会员卡销售明细表

- a.主表：membership_order
- b.根据时间进行批量更新：t0.updatetime &gt;= #{startTime}
  and t0.updatetime &lt; #{endTime}

```sql
 select t0.trade_no as tradeNo,
               t0.paidtime as paidtime,
               t0.mobile as mobile,
               t1.name as memberCardType,
               t0.amount as price,
               t0.discount as discount,
               t0.paid_amount as paidAmount,
               t0.member_name as  realName,
               t0.certificate_type as certificateType,
               t0.certificate_no as certificateNo,
               t0.gateway_code as paymethod,
               '' as userGroupName,
               '' as userName,
               t1.id as membershipCardId,
               concat('sale',t0.id) as id,
               t0.company_id as companyId,
               t0.user_id as tbsUserId,
               t0.user_group_id as userGroupId
        from membership_order t0
                 left join membership_type t1 on t0.membership_type_id = t1.id
        where t0.updatetime &gt;= #{startTime}
        and t0.updatetime &lt; #{endTime}
```

## 7 会员卡销售汇总表

- a.主表：membership_order
- b.根据时间进行批量更新：t0.addtime in (select distinct addtime from membership_order where updatetime &gt;= #{startTime} and updatetime &lt; #{endTime})

```sql
 select t1.name as membershipCardType,
               t0.gateway_code as paymethod,
               t1.add_user_id as userGroupId,
               date(t0.addtime) as addDate,
               t1.id as membershipCardId,
               t0.company_id as companyId,
               sum(amount + t0.other_fee) as saleAmount,
               sum(discount) as saleDiscount,
               sum(t0.paid_amount) as salePaidAmount,
               count(t0.quantity) as saleCount
               from membership_order t0
               left join membership_type t1 on t0.membership_type_id = t1.id
               where t0.addtime in (select distinct addtime from membership_order where updatetime &gt;= #{startTime} and updatetime &lt; #{endTime})
               GROUP BY t1.name,t0.gateway_code,date(t0.addtime),t1.id,t0.company_id,t1.add_user_id
```

## 8 会员卡入场明细表

- a.主表：check_record
- b.根据时间进行批量更新：and t0.updatetime &gt;= #{startTime}
  and t0.updatetime &lt; #{endTime}
- c.状态：reserve_type = 'membership_check'

```sql
  select t0.check_time as checkTime,
               t0.ticket_type_id as ticketTypeId,
               t1.username as checkNo,
               t2.group_name as checkUserGroupName,
               t0.tbs_user_id as tbsUserId,
               t0.company_id as companyId,
               t0.id as id
        from check_record t0
        left join tbs_user t1 on t1.id = t0.tbs_user_id
        left join user_group t2 on t2.id = t1.group_id
        where reserve_type = 'membership_check'
        and t0.updatetime &gt;= #{startTime}
        and t0.updatetime &lt; #{endTime}
```

注：冗余会员卡

```sql
 select t0.cardno as cardNo,
               t0.trade_no as tradeNo,
               t1.name as membershipCardType,
               t1.price as price,
               t0.contact_name as realName,
               t0.contact_cert_type as certificateType,
               t0.contact_cert_no as certificateNo,
               t0.membership_type_id as membershipCardId,
               t1.add_user_id as tbsUserId
        from membership t0
        left join membership_type t1 on t1.id = t0.membership_type_id
        where t0.id = #{ticketTypeId}
```

## 9.站票入场汇总表（按天）第二批

- a.主表：check_record
- b.根据时间进行批量更新：t0.check_date in (select distinct check_date from check_record where updatetime &gt;= #{startTime} and updatetime &lt; #{endTime})
- c.状态：t0.reserve_type = 'show_check'

```sql
 select date(t0.check_time) as checkDate,
   t1.stadium_id as stadiumId,
   t3.cn_name as stadiumName,
   t2.venue_id as venueId,
   t4.cn_name as venueName,
   t1.program_id as programId,
   t2.cn_name as programName,
   t5.play_time as playTime,
   t1.show_id as showId,
   t0.ticket_type_id as ticketTypeId,
   t6.cn_name as ticketTypeName,
   t1.user_group_id as userGroupId,
   t7.group_name as userGroupName,
   t0.company_id as companyId,
   t0.check_user_id as tbsUserId,
   t9.group_name as checkGroupName,
   t9.id as checkGroupId,
   count(1) as checkCount
 from show_order_detail t0
   left join show_order t1 on t1.trade_no = t0.trade_no
   left join program t2 on t2.id = t1.program_id
   left join stadium t3 on t3.id = t1.stadium_id
   left join venue t4 on t4.id = t2.venue_id
   left join open_show t5 on t5.id = t1.show_id
   left join ticket_type t6 on t6.id = t0.ticket_type_id
   left join user_group t7 on t7.id = t1.user_group_id
   left join tbs_user t8 on t8.id = t0.check_user_id
   left join user_group t9 on t9.id = t8.group_id
 where date(t0.check_time) in (select distinct date(check_time) from show_order_detail where updatetime &gt;= #{startTime} and updatetime &lt; #{endTime})
 GROUP BY date(t0.check_time),t1.stadium_id,t3.cn_name,t2.venue_id,t4.cn_name,t1.program_id,t2.cn_name,
   t5.play_time,t1.show_id,t0.ticket_type_id,t6.cn_name,t1.user_group_id,t7.group_name,t0.company_id,t0.check_user_id,
   t9.group_name,t9.id
```

## 10.会员卡入场汇总表（按天）

- a.主表：report_membership_check_detail
- 直接获取明细数据进行汇总

```sql
 select date(check_time) as checkDate,
            membership_card_type as membershipCardType,
            price as price,
            membership_card_id as membershipCardId,
            check_user_group_name as checkGroupName,
            count(1) as checkCount
            from report_membership_check_detail
        where check_time &gt;= #{datefrom}
        and check_time &lt; #{dateto}
        and company_id = #{companyId}
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(membershipCardTypeId)">
            and membership_card_id = #{membershipCardTypeId}
        </if>
        group by date(check_time),membership_card_type,price,check_user_group_name,membership_card_id
        order by date(check_time)
```

## 11.会员卡验证明细表

- a.主表：verify_record
- b.根据时间进行批量更新：t0.updatetime &gt;= #{startTime} and t0.updatetime &lt; #{endTime}

```sql
  select t0.check_time as verifyTime,
               t0.tbs_user_id as tbsUserId,
               t2.group_name as checkGroupName,
               t0.ticket_type_id as ticketTypeId,
               t0.id as id,
               t0.company_id as companyId,
               t1.username as checkNo,
               t2.id as checkGroupId
        from verify_record t0
        left join tbs_user t1 on t1.id = t0.tbs_user_id
        left join user_group t2 on t2.id = t1.group_id
        where t0.updatetime &gt;= #{startTime}
            and t0.updatetime &lt; #{endTime}
```

注：冗余会员卡数据

```sql
 select t0.cardno as cardNo,
               t1.name as membershipCardType,
               t1.price as price,
               t0.contact_name as realName,
               t0.contact_cert_type as certificateType,
               t0.contact_cert_no as certificateNo,
               t0.membership_type_id as membershipCardId,
               t1.add_user_id as tbsUserId
        from membership t0
        left join membership_type t1 on t1.id = t0.membership_type_id
        where t0.id = #{ticketTypeId}
```

## 12. 会员卡验证汇总表

- a.数据直接从明细里面进行汇总

```sql
 select
          date(verify_time) as verifyDate,
          check_group_name as verifyUserGroupName,
          membership_card_type as membershipCardType,
          membership_card_id as membershipCardTypeId,
          price as price,
          count(1) as verityCount
          from report_membership_verify_detail
        where verify_time &gt;= #{datefrom}
        and verify_time &lt; #{dateto}
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(membershipCardTypeId)">
            and membership_card_id = #{membershipCardTypeId}
        </if>
        GROUP BY date(verify_time),check_group_name,membership_card_type, membership_card_id,price
        order by verify_time
```

## 13.入场明细表

直接获取anterminal数据库的源数据表：check_record

```sql
 select * from check_record
        where check_time &gt;= #{startTime}
          and check_time &lt; #{endTime}
          and company_id = #{companyId}
          order by check_time asc
```

## 14.座票销售及入场明细表

### 14.1 销售

- a.主表: seat_detail
- b. 根据时间进行批量更新：t0.updatetime &gt;= #{startTime}
  and t0.updatetime &lt;  #{endTime}
- c.状态：  seat_detail.t0.pay_status like 'paid%' and t0.status != 'T'

```sql
select t0.trade_no           as tradeNo,
       t1.mobile             as mobile,
       t1.tickettime         as tickettime,
       t1.program_id         as programId,
       t2.cn_name            as programName,
       t1.stadium_id         as stadiumId,
       t3.cn_name            as stadiumName,
       t2.venue_id           as venueId,
       t4.cn_name            as venueName,
       t1.schedule_id        as showId,
       t5.cn_name            as showName,
       t5.play_time          as play_time,
       t0.row_no             as rowNo,
       t0.col_no             as colNo,
       t0.uuid               as uuid,
       t0.realname           as realName,
       t0.certificate_type   as certificateType,
       t0.certificate_no     as certificateNo,
       t0.ticket_price       as amount,
       t0.discount           as discount,
       t0.paid_amount        as paidAmount,
       'pay'                 as payType,
       t0.gateway_code       as paymethod,
       ''                    as logisticsMode,
       ''                    as logisticsAddress,
       ''                    as ticketMessage,
       ''                    as orderRelatedMessage,
       ''                    as description,
       t6.group_name         as userGroupName,
       t7.username           as userName,
       t0.company_id         as companyId,
       t0.check_time         as checkTime,
       concat('sale', t0.id) as id
from seat_detail t0
         left join ticket_order t1 on t1.trade_no = t0.trade_no
         left join program t2 on t2.id = t1.program_id
         left join stadium t3 on t3.id = t1.stadium_id
         left join venue t4 on t4.id = t2.venue_id
         left join open_show t5 on t5.id = t1.schedule_id
         left join user_group t6 on t6.id = t1.user_group_id
         left join tbs_user t7 on t7.id = t0.add_user_id
where t0.pay_status like 'paid%'
  and t0.updatetime >= ''
  and t0.updatetime < ''
```

### 14.2 退款

- a.主表：order_refund_detail
- b.根据时间进行批量更新： and t0.updatetime &gt;= #{startTime}
  and t0.updatetime &lt; #{endTime}
- c.状态：t1.order_type = 'show' and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success')
  and t1.origin_status like 'paid%'
  and t1.order_type = 'ticket'

```sql
select t0.refund_serial_no     as tradeNo,
       t3.mobile               as mobile,
       t0.tickettime           as tickettime,
       t3.program_id           as programId,
       t4.cn_name              as programName,
       t1.stadium_id           as stadiumId,
       t5.cn_name              as stadiumName,
       t4.venue_id             as venueId,
       t6.cn_name              as venueName,
       t3.schedule_id          as showId,
       t7.cn_name              as showName,
       t7.play_time            as playTime,
       t2.row_no               as rowNo,
       t2.col_no               as colNo,
       t0.uuid                 as uuid,
       t2.realname             as realName,
       t2.certificate_type     as certificateType,
       t2.certificate_no       as certificateNo,
       -t2.ticket_price        as amount,
       t2.discount             as discount,
       -t0.real_pay            as paidAmount,
       'refund'                as payType,
       t0.gateway_code         as paymethod,
       t9.group_name           as userGroupName,
       t10.username            as userName,
       ''                      as logisticsMode,
       ''                      as logisticsAddress,
       ''                      as ticketMessage,
       ''                      as orderRelatedMessage,
       ''                      as description,
       concat('refund', t0.id) as id,
       t0.company_id           as companyId,
       t11.check_time          as checkTime
from order_refund_detail t0
         left join order_refund t1 on t1.serial_no = t0.refund_serial_no
         left join seat_detail t2 on t2.uuid = t0.uuid
         left join ticket_order t3 on t3.trade_no = t1.trade_no
         left join program t4 on t4.id = t3.program_id
         left join stadium t5 on t5.id = t1.stadium_id
         left join venue t6 on t6.id = t4.venue_id
         left join open_show t7 on t7.id = t3.schedule_id
         left join user_group t9 on t9.id = t0.user_group_id
         left join tbs_user t10 on t10.id = t0.sale_user_id
where t1.order_type = 'ticket'
  and t1.origin_status like 'paid%'
  and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success')
  and t0.updatetime >= ''
  and t0.updatetime < '';
```

### 14.3 检票

```sql
select uuid, check_time
from check_record
where reserve_type = 'ticket_check'
  and updatetime >= ''
  and updatetime < '';
```

## 15.报表刷新接口数据文档

### 15.1 刷新站票入场明细表

路径：http://127.0.0.1:33180/sadcenter/inner/report/sale/updateStandCheckDetailCount.xhtml?datefrom=&dateto=
表名：report_stand_check_detail

### 15.2 刷新站票销售及入场汇总表（按项目）

路径：http://127.0.0.1:33180/sadcenter/inner/report/reserve/updateStandSaleCheckGatherCount.xhtml?datefrom=2022-03-01&dateto=2022-05-10
表名：report_stand_sale_check_gather

### 15.3 刷新站票销售汇总表（按天）

路径：http://127.0.0.1:33180/sadcenter/inner/report/reserve/updateStandSaleGatherCount.xhtml?datefrom=2022-03-01&dateto=2022-05-10
表名：report_stand_sale_gather

### 15.4 刷新座票入场明细表

路径：http://127.0.0.1:33180/sadcenter/inner/report/seat/updateSaleCheckDetailCount.xhtml?datefrom=&dateto=
表名：report_seat_sale_check_detail

### 15.4 预约及入场明细表

路径：http://127.0.0.1:33180/sadcenter/inner/report/reserve/updateReserveCheckDetailCount.xhtml?datefrom=&dateto=
表名：report_reserve_check_detail

### 15.5 刷新会员卡销售明细表

路径：http://127.0.0.1:33180/sadcenter/inner/report/member/updateMemberSaleDetailCount.xhtml?datefrom=&dateto=
表名：report_member_sale_detail

### 15.6 刷新预约及入场汇总表

路径：http://127.0.0.1:33180/sadcenter/inner/report/reserve/updateReserveCheckGatherCount.xhtml?datefrom=2022-03-01&dateto=2022-05-01
表名：report_reserve_check_gather

### 15.7 刷新会员卡销售汇总表

路径：http://127.0.0.1:33180/sadcenter/inner/report/reserve/updateReportMemberSaleGatherCount.xhtml?datefrom=&dateto=
表名：report_member_sale_gather

### 15.8 刷新坐票票房汇总(按座位属性)表
路径：http://127.0.0.1:33180/sadcenter/inner/report/reserve/updateSeatSaleGatherSeatAttrCount.xhtml?datefrom=&dateto=
表名：report_seat_sale_gather_seat_attr
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llwh.dcenter.mapper.ReportMemberDashboardMapper">

    <sql id="commonWhere4MemberInfo">
        where company_id = #{search.companyId}
        and status != 'D'
        <if test="search.addtimeFrom != null">
            and addtime &gt;= #{search.addtimeFrom}
        </if>
        <if test="search.addtimeTo != null">
            and addtime &lt;= #{search.addtimeTo}
        </if>
    </sql>

    <select id="getMemberLevelDistribution" resultType="java.util.Map">
        select
        member_level_id as memberLevelId,
        count(*) as num
        from member_info
        <include refid="commonWhere4MemberInfo"/>
        group by 1
    </select>

    <select id="getTodayAddNum" resultType="java.lang.Integer">
        select
        count(*) as num
        from member_info
        <include refid="commonWhere4MemberInfo"/>
    </select>

    <select id="getMemberIncrementMonthly" resultType="java.util.Map">
        select
        date_format(addtime, '%Y-%m') as month,
        count(*) as num
        from member_info
        <include refid="commonWhere4MemberInfo"/>
        group by 1
    </select>

    <select id="getMemberIncrementDaily" resultType="java.util.Map">
        select
        date_format(addtime, '%Y-%m-%d') as date,
        count(*) as num
        from member_info
        <include refid="commonWhere4MemberInfo"/>
        group by 1
    </select>

    <select id="getMemberLevelConsumptionAbility4Seat" resultType="java.util.Map">
        select
        member_level_id as memberLevelId,
        sum(paid_amount) as paidAmount,
        count(distinct trade_no) as orderNum
        from report_seat_sale_check_detail
        where company_id = #{search.companyId}
          and member_level_id is not null
          and status != 'T'
        group by 1
    </select>

    <select id="getMemberLevelConsumptionAbility4Stand" resultType="java.util.Map">
        select
        member_level_id as memberLevelId,
        sum(real_pay) as paidAmount,
        count(distinct trade_no) as orderNum
        from report_stand_check_detail
        where company_id = #{search.companyId}
          and member_level_id is not null
          and status != 'T'
        group by 1
    </select>

    <select id="getMemberLevelConsumptionAbilityTrends4Seat" resultType="java.util.Map">
        select distinct
        date_format(paidtime, '%Y-%m') as month,
        member_level_id as memberLevelId,
        member_id as memberId
        from report_seat_sale_check_detail
        where company_id = #{search.companyId}
          and member_level_id is not null
        <if test="search.paidtimeFrom != null">
            and paidtime &gt;= #{search.paidtimeFrom}
        </if>
        <if test="search.paidtimeTo != null">
            and paidtime &lt;= #{search.paidtimeTo}
        </if>
    </select>

    <select id="getMemberLevelConsumptionAbilityTrends4Stand" resultType="java.util.Map">
        select distinct
        date_format(paidtime, '%Y-%m') as month,
        member_level_id as memberLevelId,
        member_id as memberId
        from report_stand_check_detail
        where company_id = #{search.companyId}
          and member_level_id is not null
        <if test="search.paidtimeFrom != null">
            and paidtime &gt;= #{search.paidtimeFrom}
        </if>
        <if test="search.paidtimeTo != null">
            and paidtime &lt;= #{search.paidtimeTo}
        </if>
    </select>

    <select id="getMemberConsumptionActivityInfo" resultType="java.util.Map">
        select
        create_time as createTime,
        last_order ->> '$.lastOrder.tickettime' as lastOrderTime
        from report_member_info
        where company_id = #{search.companyId}
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llwh.dcenter.mapper.CombRefundFlowMapper">

    <select id="getCount" resultType="com.llwh.dcenter.model.CombRefundFlow">
        select *
        from comb_refund_flow
        where
        <choose>
            <when test="@org.apache.commons.lang3.StringUtils@isNotBlank(tradeNo)">
                trade_no = #{tradeNo}
            </when>
            <otherwise>
                updatetime &gt;= #{startTime}
                and updatetime &lt; #{endTime}
            </otherwise>
        </choose>
    </select>

    <select id="getRefundAmountCount" resultType="map">
        select gateway_code as gatewayCode, -sum(refund_amount) as refundAmount from comb_refund_flow where refund_no in
        <foreach collection="list" item="refundNo" open="(" close=")" separator=",">
            #{refundNo}
        </foreach>
        group by gateway_code
    </select>

</mapper>

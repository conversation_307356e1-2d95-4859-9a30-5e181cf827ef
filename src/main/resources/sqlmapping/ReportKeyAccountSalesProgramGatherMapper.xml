<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.ReportKeyAccountSalesProgramGatherMapper">

    <sql id="commonWhere4DataAuthority">
        <choose>
            <when test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityProgramIds)">
                and (rsscd.program_id in
                <foreach collection="search.authorityProgramIds" item="programId" separator="," open="(" close=")">
                    #{programId}
                </foreach>
                <trim prefix="or (" prefixOverrides="and" suffix=")">
                    <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                        and rsscd.stadium_id in
                        <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                            #{stadiumId}
                        </foreach>
                    </if>
                    <if test="search.authorityUserGroupId != null">
                        and rsscd.user_group_id = #{search.authorityUserGroupId}
                    </if>
                    <if test="search.authorityUserId != null">
                        and rsscd.add_user_id = #{search.authorityUserId}
                    </if>
                </trim>
                )
            </when>
            <otherwise>
                <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                    and rsscd.stadium_id in
                    <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                        #{stadiumId}
                    </foreach>
                </if>
                <if test="search.authorityUserGroupId != null">
                    and rsscd.user_group_id = #{search.authorityUserGroupId}
                </if>
                <if test="search.authorityUserId != null">
                    and rsscd.add_user_id = #{search.authorityUserId}
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="commonWhere">
        where company_id = #{companyId}
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.scheduleIdList)">
            and show_id in
            <foreach collection="search.scheduleIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search.tickettimeFrom != null">
            and tickettime &gt;= #{search.tickettimeFrom}
        </if>
        <if test="search.tickettimeTo != null">
            and tickettime &lt;= #{search.tickettimeTo}
        </if>
        <if test="search.paidtimeFrom != null">
            and paidtime &gt;= #{search.paidtimeFrom}
        </if>
        <if test="search.paidtimeTo != null">
            and paidtime &lt;= #{search.paidtimeTo}
        </if>
        <if test="search.customerId != null">
            and customer_id = #{search.customerId}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.customerUnitName)">
            and customer_unit_name like #{search.customerUnitName}
        </if>
        <include refid="commonWhere4DataAuthority"></include>
    </sql>

    <select id="getCounts" resultType="java.util.Map">
        select
        program_id as programId,
        ifnull(customer_id, 0) as customerId,
        ticket_price_id as ticketPriceId,
        sell_type as sellType,
        sum(quantity) as quantity,
        sum(amount) as amount,
        sum(paid_amount) as paidAmount,
        sum(settlement_amount) as settlementAmount
        from report_seat_sale_check_detail rsscd
        <include refid="commonWhere"></include>
        group by 1,2,3,4
    </select>

    <select id="getTotals" resultType="java.util.Map">
        select
        sum(quantity) as quantity,
        sum(amount) as amount,
        sum(paid_amount) as paidAmount,
        sum(settlement_amount) as settlementAmount
        from report_seat_sale_check_detail rsscd
        <include refid="commonWhere"></include>
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.DiscountApportionmentCheckMapper">

    <select id="getShowDiscountApportionment" resultType="com.llwh.dcenter.vo.report.ApportionmentCheckVo">
        select t0.trade_no           as tradeNo,
               t0.uuid               as uuid,
               'show'                as orderType,
               t0.discount           as discount,
               t0.company_id         as companyId
        from show_order_detail t0
        where t0.pay_status like 'paid%'
          and t0.updatetime &gt;= #{startTime}
          and t0.updatetime &lt;  #{endTime}
    </select>

    <select id="getSeatDiscountApportionment" resultType="com.llwh.dcenter.vo.report.ApportionmentCheckVo">
        select t0.trade_no           as tradeNo,
               t0.uuid               as uuid,
               'ticket'              as orderType,
               t0.discount           as discount,
               t0.company_id         as companyId
        from seat_detail t0
        where t0.pay_status like 'paid%'
          and t0.updatetime &gt;= #{startTime}
          and t0.updatetime &lt;  #{endTime}
    </select>
</mapper>

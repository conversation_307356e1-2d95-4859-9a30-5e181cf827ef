<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.SeatProgramSourceMapper">

    <sql id="commonWhere4DataAuthority">
        <choose>
            <when test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityProgramIds)">
                and (rsscd.program_id in
                <foreach collection="search.authorityProgramIds" item="programId" separator="," open="(" close=")">
                    #{programId}
                </foreach>
                <trim prefix="or (" prefixOverrides="and" suffix=")">
                    <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                        and rsscd.stadium_id in
                        <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                            #{stadiumId}
                        </foreach>
                    </if>
                    <if test="search.authorityUserGroupId != null">
                        and rsscd.user_group_id = #{search.authorityUserGroupId}
                    </if>
                    <if test="search.authorityUserId != null">
                        and rsscd.add_user_id = #{search.authorityUserId}
                    </if>
                </trim>
                )
            </when>
            <otherwise>
                <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                    and rsscd.stadium_id in
                    <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                        #{stadiumId}
                    </foreach>
                </if>
                <if test="search.authorityUserGroupId != null">
                    and rsscd.user_group_id = #{search.authorityUserGroupId}
                </if>
                <if test="search.authorityUserId != null">
                    and rsscd.add_user_id = #{search.authorityUserId}
                </if>
            </otherwise>
        </choose>
    </sql>

    <select id="getSaleDetailsByDiscount" resultType="com.llwh.dcenter.vo.programStatistics.ProgramTicketPriceVo">
        select program_id,
        show_id as scheduleId,
        add_user_id,
        user_group_id,
        platform,
        sell_type,
        (select group_concat(DISTINCT description SEPARATOR ' + ') from report_seat_discount_apportionment
            where uuid = rsscd.uuid and trade_no = rsscd.trade_no order by description) as discountNames,
        if(convert(paid_amount / amount, DECIMAL(12, 2)) is null, 1, convert(paid_amount / amount, DECIMAL(12, 2))) as discountRate,
        ticket_price_id as ticketPriceId,
        convert(sum(quantity), DECIMAL(15, 2) ) as quantity,
        convert(sum(amount), DECIMAL(15, 2) ) as amount,
        convert(sum(paid_amount), DECIMAL(15, 2) ) as paidAmount,
        convert(sum(settlement_amount), DECIMAL(15, 2) ) as settlementAmount
        from report_seat_sale_check_detail rsscd
        where company_id = #{companyId}
        <if test="search.scheduleIdList != null">
            and show_id in
            <foreach collection="search.scheduleIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search.ticketDateFrom != null">
            and tickettime &gt;= #{search.ticketDateFrom}
        </if>
        <if test="search.ticketDateTo != null">
            and tickettime &lt;= #{search.ticketDateTo}
        </if>
        <include refid="commonWhere4DataAuthority"></include>
        group by 1,2,3,4,5,6,7,8,9
    </select>

    <select id="getTotals" resultType="com.llwh.dcenter.vo.programStatistics.ProgramTicketPriceVo">
        select
        sum(quantity) as quantity,
        sum(amount) as amount,
        sum(paid_amount) as paidAmount,
        sum(settlement_amount) as settlementAmount
        from report_seat_sale_check_detail rsscd
        where company_id = #{companyId}
        <if test="search.scheduleIdList != null">
            and show_id in
            <foreach collection="search.scheduleIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search.ticketDateFrom != null">
            and tickettime &gt;= #{search.ticketDateFrom}
        </if>
        <if test="search.ticketDateTo != null">
            and tickettime &lt;= #{search.ticketDateTo}
        </if>
        <include refid="commonWhere4DataAuthority"></include>
    </select>

    <select id="getBalanceTotalAmount" resultType="java.lang.Double">
        select sum(discount_amount) from report_seat_discount_apportionment rsda
        inner join report_seat_sale_check_detail rsscd on rsscd.uuid = rsda.uuid and rsscd.pay_type= rsda.pay_type
        and rsscd.company_id = rsda.company_id
        where rsda.category = 'balance' and rsda.company_id = #{companyId}
        <if test="search.scheduleIdList != null">
            and rsscd.show_id in
            <foreach collection="search.scheduleIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search.ticketDateFrom != null">
            and rsscd.tickettime &gt;= #{search.ticketDateFrom}
        </if>
        <if test="search.ticketDateTo != null">
            and rsscd.tickettime &lt;= #{search.ticketDateTo}
        </if>
        <include refid="commonWhere4DataAuthority"></include>
    </select>
</mapper>

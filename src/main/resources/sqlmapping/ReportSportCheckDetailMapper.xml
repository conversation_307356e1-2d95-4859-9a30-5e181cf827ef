<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llwh.dcenter.mapper.ReportSportCheckDetailMapper">
    <select id="getOrderCount" resultType="com.llwh.dcenter.model.ReportSportCheckDetail">
        select t1.platform                                         as platform,
               t1.tickettime                                       as tickettime,
               t1.trade_no                                         as tradeNo,
               t1.payseqno                                         as payseqno,
               t1.out_trade_no                                     as outTradeNo,
               t1.pay_status                                       as payStatus,
               t1.mobile                                           as mobile,
               t0.realname                                         as realName,
               t1.contact_mobile                                   as contactMobile,
               t1.program_id                                       as programId,
               t2.name                                             as programName,
               t1.stadium_id                                       as stadiumId,
               t3.name                                             as stadiumName,
               t1.sport_court_id                                   as sportCourtId,
               t4.name                                             as sportCourtName,
               t1.sport_piece_id                                   as sportPieceId,
               t5.name                                             as sportPieceName,
               t0.sport_piece_matrix_id                            as sportPieceMatrixId,
               t0.play_date                                        as playDate,
               t0.play_time                                        as playTime,
               t0.play_end_time                                    as playEndTime,
               1                                                   as quantity,
               t0.ticket_price                                     as ticketPrice,
               t0.discount                                         as discount,
               t0.paid_amount                                      as realPay,
               if(t0.gateway_code like 'free%', 0, t0.paid_amount) as settlementAmount,
               concat('sale', t0.id)                               as id,
               t0.uuid                                             as uuid,
               t0.gateway_code                                     as paymethod,
               t1.merchant_code                                    as merchantCode,
               t0.paidtime                                         as paidtime,
               t8.id                                               as userId,
               t8.username                                         as userName,
               t7.id                                               as userGroupId,
               t7.group_name                                       as userGroupName,
               t0.company_id                                       as companyId
        from sport_order_detail t0
                 left join sport_order t1 on t1.trade_no = t0.trade_no
                 left join sport_program t2 on t2.id = t1.program_id
                 left join sport_stadium t3 on t3.id = t1.stadium_id
                 left join sport_court t4 on t4.id = t1.sport_court_id
                 left join sport_piece t5 on t5.id = t1.sport_piece_id
                 left join sport_piece_matrix t6 on t6.id = t0.sport_piece_matrix_id
                 left join user_group t7 on t7.id = t1.user_group_id
                 left join tbs_user t8 on t8.id = t0.sale_user_id
        where t0.pay_status like 'paid%'
          and t0.updatetime &gt;= #{startTime}
          and t0.updatetime &lt; #{endTime}
    </select>
    <select id="getRefundDetailCount" resultType="com.llwh.dcenter.model.ReportSportCheckDetail">
        select t1.platform                                           as platform,
               t1.tickettime                                         as tickettime,
               t1.trade_no                                           as tradeNo,
               t1.payseqno                                           as payseqno,
               t1.out_trade_no                                       as outTradeNo,
               t1.pay_status                                         as payStatus,
               t1.mobile                                             as mobile,
               t10.realname                                          as realName,
               t1.contact_mobile                                     as contactMobile,
               t1.program_id                                         as programId,
               t2.name                                               as programName,
               t1.stadium_id                                         as stadiumId,
               t3.name                                               as stadiumName,
               t1.sport_court_id                                     as sportCourtId,
               t4.name                                               as sportCourtName,
               t1.sport_piece_id                                     as sportPieceId,
               t5.name                                               as sportPieceName,
               t10.sport_piece_matrix_id                             as sportPieceMatrixId,
               t10.play_date                                         as playDate,
               t10.play_time                                         as playTime,
               t10.play_end_time                                     as playEndTime,
               -1                                                    as quantity,
               -t0.ticket_price                                      as ticketPrice,
               -t10.discount                                         as discount,
               -t0.real_pay                                          as realPay,
               -if(t0.gateway_code like 'free%', 0, t10.paid_amount) as settlementAmount,
               concat('refund', t0.id)                               as id,
               t0.uuid                                               as uuid,
               t0.gateway_code                                       as paymethod,
               t1.merchant_code                                      as merchantCode,
               t10.paidtime                                          as paidtime,
               t8.id                                                 as userId,
               t8.username                                           as userName,
               t7.id                                                 as userGroupId,
               t7.group_name                                         as userGroupName,
               t0.company_id                                         as companyId
        from order_refund_detail t0
                 left join order_refund t9 on t9.serial_no = t0.refund_serial_no
                 left join sport_order t1 on t1.trade_no = t0.trade_no
                 left join sport_order_detail t10 on t10.uuid = t0.uuid
                 left join sport_program t2 on t2.id = t1.program_id
                 left join sport_stadium t3 on t3.id = t1.stadium_id
                 left join sport_court t4 on t4.id = t1.sport_court_id
                 left join sport_piece t5 on t5.id = t1.sport_piece_id
                 left join sport_piece_matrix t6 on t6.id = t1.sport_piece_matrix_id
                 left join user_group t7 on t7.id = t9.user_group_id
                 left join tbs_user t8 on t8.id = t0.sale_user_id
        where t9.order_type = 'sport'
          and t9.origin_status like 'paid%'
          and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success', 'refund_audited', 'refund_running')
          and t0.updatetime &gt;= #{startTime}
          and t0.updatetime &lt; #{endTime};
    </select>
    <select id="getTotals" resultType="com.llwh.dcenter.model.ReportSportCheckDetail">
        select  sum(quantity) as quantity,
                sum(ticket_price) as ticketPrice,
                sum(discount) as discount,
                sum(real_pay) as realPay
        from report_sport_check_detail
        <where>
            company_id = #{search.companyId}
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.platform)">
                and platform = #{search.platform}
            </if>
            <if test="search.startTime != null">
                and tickettime &gt;= #{search.startTime}
            </if>
            <if test="search.endTime != null">
                and tickettime &lt;= #{search.endTime}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.tradeNo)">
                and trade_no = #{search.tradeNo}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.payseqno)">
                and payseqno = #{search.payseqno}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.outTradeNo)">
                and out_trade_no = #{search.outTradeNo}
            </if>
            <if test="search.orderStatus =='paidNoTickets'">
                and pay_status like 'paid%'
                and pay_status not
                in('paid_success','paid_return','paid_return_cert','paid_return_succ','paid_return_fail')
            </if>
            <if test="search.orderStatus =='paidSuccess'">
                and pay_status = 'paid_success'
            </if>
            <if test="search.orderStatus =='paidRefund'">
                and pay_status like 'paid_return%'
            </if>
            <if test="search.orderStatus =='unPaid'">
                and (pay_status like 'cancel%' or pay_status like 'new%' )
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.mobile)">
                and mobile = #{search.mobile}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.contactMobile)">
                and contact_mobile = #{search.contactMobile}
            </if>
            <if test="search.stadiumId != null">
                and stadium_id = #{search.stadiumId}
            </if>
            <if test="search.pieceId != null">
                and sport_piece_id = #{search.pieceId}
            </if>
            <if test="search.programId != null">
                and program_id = #{search.programId}
            </if>
            <if test="search.courtId != null">
                and sport_court_id = #{search.courtId}
            </if>
            <if test="search.userGroupId != null">
                and user_group_id = #{search.userGroupId}
            </if>
            <if test="search.saleUserId != null">
                and user_id = #{search.saleUserId}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.paymethod)">
                and paymethod = #{search.paymethod}
            </if>
            <if test="search.playDateStart != null">
                and play_date &gt;= #{search.playDateStart}
            </if>
            <if test="search.playDateEnd != null">
                and play_date &lt;= #{search.playDateEnd}
            </if>
            <if test="search.playStartTime != null">
                and play_time &gt;= #{search.playStartTime}
            </if>
            <if test="search.playEndTime != null">
                and play_time &lt;= #{search.playEndTime}
            </if>
        </where>
    </select>

</mapper>

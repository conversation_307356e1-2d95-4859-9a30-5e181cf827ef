<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.common.MemberInfoMapper">

    <select id="getByMemberId" resultType="com.llwh.dcenter.vo.common.MemberInfoVo">
        SELECT
            id,
            addtime as addtime,
            member_level_id as memberLevelId
        FROM
            member_info
        where company_id=#{companyId}
              and member_id=#{memberId}
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.common.MemberActionStatsMapper">

    <select id="getList" resultType="com.llwh.dcenter.vo.common.MemberActionStatsVo">
        SELECT
            id,
            order_count as orderCount,
            expenditure_count-refund_count as expenditureCount,
            total_expenditure-total_refund as totalExpenditure,
            total_expenditure_point-total_refund_point as totalExpenditurePoint,
            updatetime,
            member_id memberId,
            action_type as actionType
        FROM
            member_action_stats
        where company_id=#{companyId}
              and member_id=#{memberId}
    </select>

</mapper>

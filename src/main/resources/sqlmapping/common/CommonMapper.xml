<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llwh.dcenter.mapper.common.CommonMapper">


    <select id="getReservePeriod" resultType="com.llwh.dcenter.vo.report.ReservePeriod">
        select id, starttime, endtime
        from reserve_period
        where reservedate &gt;= #{dateFrom}
          and reservedate &lt;= #{dateTo}
          and company_id = #{companyId}
    </select>

    <select id="getStadium" resultType="com.llwh.dcenter.vo.common.StadiumVo">
        select id, cn_name as stadiumName
        from stadium
        where company_id = #{companyId}
    </select>

    <select id="getMemberLevelByCompanyId" resultType="com.llwh.dcenter.vo.common.MemberLevelVo">
        select id,name as memberLevelName,growth_value,level_code as levelCode from member_level
        where company_id = #{companyId}
        and status = 'Y'
        <choose>
            <when test='special == "Y"'>
                and level_code in ('X', 'Y', 'Z')
            </when>
            <when test='special == "N"'>
                and level_code not in ('X', 'Y', 'Z')
            </when>
        </choose>
        order by growth_value asc
    </select>

    <select id="getMemberLevelByIds" resultType="com.llwh.dcenter.vo.common.MemberLevelVo">
        select id,name as memberLevelName,level_code,growth_value from member_level
        where company_id = #{companyId}
        and status = 'Y'
        and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by growth_value asc
    </select>

    <select id="getVenue" resultType="com.llwh.dcenter.vo.common.VenueVo">
        select id, cn_name as venueName
        from venue
        where stadium_id = #{stadiumId}
    </select>

    <select id="searchVenue" resultType="com.llwh.dcenter.vo.common.VenueSearchVo">
        select id,cn_name as venueName from venue
        where company_id = #{companyId}
        <if test="stadiumId != null">
            and stadium_id = #{stadiumId}
        </if>
    </select>

    <select id="getProgram" resultType="com.llwh.dcenter.vo.common.ProgramVo">
        select id, cn_name as programName, program_code
        from program
        where venue_id = #{venueId}
          and support_seat = #{supportSeat}
    </select>

    <select id="searchProgram" resultType="com.llwh.dcenter.vo.common.ProgramSearchVo">
        select id,
        cn_name as programName,
        start_time as startTime,
        end_time as endTime
        from program
        where company_id = #{companyId}
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(supportSeat)">
            and support_seat = #{supportSeat}
        </if>
        order by addtime desc
    </select>

    <select id="searchMerchant" resultType="com.llwh.dcenter.vo.common.MerchantVo">
        select id, realname as username
        from merchant
        where company_id = #{companyId}
    </select>
    <select id="listMerchantById" resultType="com.llwh.dcenter.vo.common.MerchantVo">
        select id, username
        from merchant
        where company_id = #{companyId} and id in
        <foreach item="id" collection="merchantIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getOpenShow" resultType="com.llwh.dcenter.vo.common.OpenShowVo">
        select id, cn_name as showName
        from open_show
        where program_id = #{programId}
    </select>

    <select id="getSchedule" resultType="com.llwh.dcenter.vo.common.OpenShowVo">
        select id, cn_name as showName
        from schedule
        where program_id = #{programId}
    </select>

    <select id="getTicketType" resultType="com.llwh.dcenter.vo.common.TicketTypeVo">
        select t0.id, t0.cn_name as ticketTypeName
        from ticket_type t0
                 left join show_ticket_type t1 on t0.id = t1.ticket_type_id
        where t1.show_id = #{showId}
    </select>

    <select id="getTicketType2" resultType="com.llwh.dcenter.vo.common.TicketTypeVo">
        select DISTINCT t0.id, t0.cn_name as ticketTypeName
        from ticket_type t0
                 left join show_ticket_type t1 on t0.id = t1.ticket_type_id
        where t0.program_id = #{programId}
    </select>

    <select id="getMembershipType" resultType="com.llwh.dcenter.vo.common.MemberCardTypeVo">
        select id as id, name as memberCardTypeName
        from membership_type
        where company_id = #{companyId}
    </select>

    <select id="getReserveProgram" resultType="com.llwh.dcenter.vo.common.ReserveProgramVo">
        select id, program_name
        from reserve_program
        where venue_id = #{venueId}
    </select>

    <select id="getPayMethod" resultType="java.lang.String">
        select cn_name as payMethodName
        from sys_paymethod
        where name = #{name}
    </select>

    <select id="getPayMethods" resultType="com.llwh.dcenter.vo.common.PaymethodVo">
        select name, cn_name as cnName from sys_paymethod where name in
        <foreach item="name" collection="list" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>

    <select id="getUserGroup" resultType="com.llwh.dcenter.vo.common.UserGroupVo">
        select id, group_name as userGroupName, group_type as userGroupType
        from user_group
        where company_id = #{companyId}
    </select>

    <select id="getUserGroupByIds" resultType="com.llwh.dcenter.vo.common.UserGroupVo">
        select id,
        group_name as userGroupName,
        group_type as userGroupType
        from user_group
        where id in
        <foreach item="userGroupId" collection="userGroupIds" open="(" separator="," close=")">
            #{userGroupId}
        </foreach>
    </select>

    <select id="getUserByGroupId" resultType="com.llwh.dcenter.vo.common.TbsUserVo">
        select id,username from tbs_user
        where company_id = #{companyId}
        <if test="userGroupId != null">
            and group_id = #{userGroupId}
        </if>
    </select>

    <select id="getAllPayMethod" resultType="com.llwh.dcenter.vo.common.PaymethodVo">
        select name, cn_name as cnName
        from sys_paymethod
    </select>

    <select id="getProgram2" resultType="com.llwh.dcenter.vo.common.ProgramVo">
        select id, cn_name as programName
        from program
        where company_id = #{companyId}
    </select>

    <select id="getCheckDetail" resultType="com.llwh.dcenter.vo.report.CheckDetailVo">
        select uuid, check_time
        from check_record
        where reserve_type = #{reserveType}
          and updatetime &gt;= #{startTime}
          and updatetime &lt; #{endTime}
    </select>

    <select id="getUserNames" resultType="com.llwh.dcenter.vo.report.ReportMemberSaleDetailVo">
        select t1.id as tbsUserId,
        t1.realname as realName,
        t1.username as userName
        from tbs_user t1
        where t1.id in
        <foreach item="tbsUserId" collection="tbsUserIds" open="(" separator="," close=")">
            #{tbsUserId}
        </foreach>
    </select>

    <select id="getTbsUserById" resultType="com.llwh.dcenter.vo.common.TbsUserVo">
        select t1.id       as tbsUserId,
               t1.username as userName,
               t1.data_level,
               t1.group_id
        from tbs_user t1
        where t1.id = #{id}
    </select>

    <select id="getUserGroupNames" resultType="com.llwh.dcenter.vo.report.ReportMemberSaleDetailVo">
        select t2.id as userGroupId,
        t2.group_name as userGroupName
        from user_group t2
        where t2.id in
        <foreach item="userGroupId" collection="userGroupIds" open="(" separator="," close=")">
            #{userGroupId}
        </foreach>
    </select>

    <select id="getStadiums" resultType="com.llwh.dcenter.vo.common.StadiumVo">
        select id as id,
        cn_name as stadiumName
        from stadium
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getVenues" resultType="com.llwh.dcenter.vo.common.VenueVo">
        select id as id,
        cn_name as venueName
        from venue
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getPrograms" resultType="com.llwh.dcenter.vo.common.ProgramVo">
        select id as id,
        cn_name as programName,
        program_code as programCode,
        category as category,
        small_category as smallCategory,
        start_time as startTime,
        end_time as endTime
        from program
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getProgramById" resultType="com.llwh.dcenter.vo.common.ProgramVo">
        select id             as id,
               cn_name        as programName,
               program_code   as programCode,
               category       as category,
               small_category as smallCategory,
               start_time     as startTime,
               end_time       as endTime
        from program
        where id = #{programId}
    </select>

    <select id="getShows" resultType="com.llwh.dcenter.vo.common.OpenShowVo">
        select id as id,
        cn_name as showName,
        play_time as playTime,
        play_end_time as playEndTime
        from open_show
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getSchedules" resultType="com.llwh.dcenter.vo.common.ScheduleVo">
        select id as id,
        cn_name as showName,
        play_time as playTime,
        play_end_time as playEndTime
        from schedule
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getTicketTypes" resultType="com.llwh.dcenter.vo.common.TicketTypeVo">
        select id as id,
        cn_name as ticketTypeName,
        price as ticketPrice,
        remark as remark,
        description as description
        from ticket_type
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getTicketPrices" resultType="com.llwh.dcenter.vo.common.TicketTypeVo">
        select id as id,
        price as ticketPrice,
        remark as remark,
        cn_name as cnName,
        description as description
        from ticket_price
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getCities" resultType="com.llwh.dcenter.vo.common.CityVo">
        select city_code as cityCode,
        city_name as cityName
        from city
        where city_code in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getAllCities" resultType="com.llwh.dcenter.vo.common.CitySearchVo">
        select city_code as cityCode,
               city_name as cityName
        from city
    </select>

    <select id="getCouponPromotions" resultType="com.llwh.dcenter.vo.common.CouponPromotionSearchVo">
        select id, name
        from coupon_promotion
        where company_id = #{companyId}
    </select>

    <select id="getMembershipTypes" resultType="com.llwh.dcenter.vo.common.MemberCardTypeVo">
        select id as id,
        name as memberCardTypeName,
        price as price
        from membership_type
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getGroupTicketTypes" resultType="com.llwh.dcenter.vo.common.TicketTypeVo">
        select ticket_type_id as id,
        ticket_type_name as ticketTypeName
        from group_ticket_type_detail
        where ticket_type_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getReservePeriods" resultType="com.llwh.dcenter.vo.common.ReservePeriodVo">
        select id as id,
        CONCAT(date_format(starttime,'%H:%i:%s'),"-",date_format(endtime,'%H:%i:%s')) AS reserve_time,
        reservedate as reservedate
        from reserve_period
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getReservePrograms" resultType="com.llwh.dcenter.vo.common.ReserveProgramVo">
        select id as id,
        program_name as programName
        from reserve_program
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getSponsorProgramByUserGroupId" resultType="Long">
        select distinct p.id as id
        from program p
        left join program_user_group pug on pug.program_id = p.id
        where pug.user_group_id = #{userGroupId}
        and p.company_id = #{companyId}
        and pug.type_id = '2'
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(supportSeat)">
            and p.support_seat = #{supportSeat}
        </if>
    </select>

    <select id="getUserGroupById" resultType="com.llwh.dcenter.vo.common.UserGroupVo">
        select id, group_name as userGroupName, group_type as userGroupType
        from user_group
        where id = #{id}
    </select>

    <select id="getSponsorReserveProgramByUserGroupId" resultType="Long">
        select reserve_program_id as reserveProgramId
        from reserve_program_user_group
        where user_group_id = #{userGroupId}
          and company_id = #{companyId}
          and type_id = '2'
    </select>

    <select id="getAllReserveProgram" resultType="com.llwh.dcenter.vo.common.ReserveProgramSearchVo">
        select id, program_name
        from reserve_program
        where company_id = #{companyId}
        order by addtime
    </select>

    <select id="getSaleProgramByAuthority" resultType="com.llwh.dcenter.vo.common.ProgramSearchVo">
        select distinct p.id as id,
        p.cn_name as programName,
        p.addtime as addtime
        from program p
        left join open_show os on p.id = os.program_id
        left join show_user_group sug on sug.show_id = os.id
        where p.company_id = #{companyId}
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(supportSeat)">
            and p.support_seat = #{supportSeat}
        </if>
        <if test="!@com.llwh.base.constant.SpecialRights@isSuper(dataLevel)">
            and sug.user_group_id = #{userGroupId}
        </if>
        union
        select distinct p.id as id,
        p.cn_name as programName,
        p.addtime as addtime
        from program p
        left join program_user_group pug on pug.program_id = p.id
        where pug.user_group_id = #{userGroupId}
        and p.company_id = #{companyId}
        and pug.type_id = '2'
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(supportSeat)">
            and p.support_seat = #{supportSeat}
        </if>
        order by addtime;
    </select>

    <select id="getCheckProgramByAuthority" resultType="com.llwh.dcenter.vo.common.ProgramSearchVo">
        select distinct p.id as id,
        p.cn_name as programName,
        p.addtime as addtime
        from program p
        left join open_show os on p.id = os.program_id
        left join show_user_group sug on sug.show_id = os.id
        where p.company_id = #{companyId}
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(supportSeat)">
            and p.support_seat = #{supportSeat}
        </if>
        <if test="userGroupId != null">
            and sug.user_group_id = #{userGroupId}
        </if>
        order by addtime;
    </select>

    <select id="getProgramByUserGroupId" resultType="com.llwh.dcenter.vo.common.ProgramSearchVo">
        select distinct p.id      as id,
                        p.cn_name as programName,
                        p.addtime as addtime
        from program p
                 left join program_user_group pug on pug.program_id = p.id
        where pug.user_group_id = #{userGroupId}
          and p.company_id = #{companyId}
          and pug.type_id = '2'
          and p.support_seat = 'N'
        order by addtime;
    </select>

    <select id="getProgramByUserGroupId2" resultType="com.llwh.dcenter.vo.common.ProgramSearchVo">
        select distinct p.id as id,
        p.cn_name as programName,
        p.addtime as addtime
        from program p
        left join program_user_group pug on pug.program_id = p.id
        where pug.user_group_id = #{userGroupId}
        and p.company_id = #{companyId}
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(supportSeat)">
            and p.support_seat = #{supportSeat}
        </if>
        order by addtime;
    </select>

    <select id="getReserveProgramByUserGroupId" resultType="com.llwh.dcenter.vo.common.ReserveProgramSearchVo">
        select distinct p.id           as id,
                        p.program_name as programName,
                        p.addtime      as addtime
        from reserve_program p
                 left join reserve_program_user_group pug on pug.reserve_program_id = p.id
        where pug.user_group_id = #{userGroupId}
          and p.company_id = #{companyId}
          and pug.type_id = '2'
        order by addtime;
    </select>

    <select id="getPaymentGateway" resultType="com.llwh.dcenter.vo.common.PaymentGatewayVo">
        select *
        from payment_gateway
        where gateway_code = #{gatewayCode}
          and company_id = #{companyId}
    </select>

    <select id="searchPointProduct" resultType="com.llwh.dcenter.vo.common.ProductSearchVo">
        select id,
               shelf_name as productName
        from pms_point_product
        where company_id = #{companyId}
        order by addtime desc
    </select>

    <select id="searchCulturalProduct" resultType="com.llwh.dcenter.vo.common.ProductSearchVo">
        select id,
               shelf_name as productName
        from pms_cultural_creation_product
        where company_id = #{companyId}
          and retail_type = #{retailType}
        order by addtime desc
    </select>

    <select id="searchRentProduct" resultType="com.llwh.dcenter.vo.common.ProductSearchVo">
        select id,
               shelf_name as productName
        from pms_rent_product
        where company_id = #{companyId}
        order by addtime desc
    </select>

    <select id="getUserGroupStadiumIds" resultType="long">
        select stadium_id
        from group_stadium_relation
        where user_group_id = #{userGroupId}
        union
        select id as stadium_id
        from stadium
        where add_user_group_id = #{userGroupId}
    </select>

    <select id="getProgramSettleSeat" resultType="com.llwh.dcenter.vo.common.ProgramSettleVo">
        select s2.cn_name as stadiumName,
        v.cn_name as venueName,
        p.id as programId,
        p.cn_name as programName,
        dv.json_value ->> '$.organizerType' as organizerType,
        s.play_time
        from schedule s
        left join program p on s.program_id = p.id
        left join stadium s2 on s.stadium_id = s2.id
        left join venue v on v.id = s.venue_id
        left join dynamic_value dv on dv.tag = 'Program' and dv.category = 'global' and dv.related_id = p.id
        where s.id in
        <foreach item="id" collection="showIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getProgramSettleStand" resultType="com.llwh.dcenter.vo.common.ProgramSettleVo">
        select s2.cn_name as stadiumName,
        v.cn_name as venueName,
        p.id as programId,
        p.cn_name as programName,
        dv.json_value ->> '$.organizerType' as organizerType,
        s.play_time
        from open_show s
        left join program p on s.program_id = p.id
        left join stadium s2 on s.stadium_id = s2.id
        left join venue v on v.id = s.venue_id
        left join dynamic_value dv on dv.tag = 'Program' and dv.category = 'global' and dv.related_id = p.id
        where s.id in
        <foreach item="id" collection="showIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getDynamicFields" resultType="String">
        select fields
        from dynamic_field
        where tag = 'program'
          and category = 'global'
          and company_id = #{companyId}
    </select>

    <select id="getProgramDynamicValue" resultType="String">
        select json_value as jsonValue
        from dynamic_value
        where tag = 'program'
          and category = 'global'
          and related_id = #{programId}
    </select>

    <select id="getVenueArea" resultType="com.llwh.dcenter.vo.common.VenueAreaVo">
        select id as venueAreaId,
        cn_name as venueAreaCnName
        from venue_area
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getScheduleIdByTradeNo" resultType="java.lang.Long">
        select schedule_id
        from ticket_order
        where trade_no = #{tradeNo};
    </select>

    <insert id="saveDateQuery">
        INSERT INTO date_query (date, quantity)
        VALUES (#{date}, 0);
    </insert>

    <select id="getDiscountTradeNos" resultType="map">
        select distinct sd.trade_no, 'ticket' as orderType from seat_detail sd where sd.discount_info is not null
        <if test="timefrom != null">
            and sd.updatetime &gt;= #{timefrom}
        </if>
        <if test="timeto != null">
            and sd.updatetime &lt; #{timeto}
        </if>
        union
        select distinct sod.trade_no, 'show' as orderType from show_order_detail sod where sod.discount_info is not null
        <if test="timefrom != null">
            and sod.updatetime &gt;= #{timefrom}
        </if>
        <if test="timeto != null">
            and sod.updatetime &lt; #{timeto}
        </if>
    </select>

    <select id="getOrderDiscounts" resultType="map">
        select id, trade_no, discount_amount from order_discount where trade_no in
        <foreach item="tradeNo" collection="tradeNos" open="(" separator="," close=")">
            #{tradeNo}
        </foreach>
    </select>

    <select id="getSeatDiscounts" resultType="map">
        select id, trade_no, discount_info from seat_detail where trade_no in
        <foreach item="tradeNo" collection="tradeNos" open="(" separator="," close=")">
            #{tradeNo}
        </foreach>
    </select>

    <select id="getShowDiscounts" resultType="map">
        select id, trade_no, discount_info from show_order_detail where trade_no in
        <foreach item="tradeNo" collection="tradeNos" open="(" separator="," close=")">
            #{tradeNo}
        </foreach>
    </select>


    <select id="listProgramStadium" resultType="com.llwh.dcenter.vo.common.ProgramSettleVo">
        select s2.cn_name as stadiumName,
        v.cn_name as venueName,
        p.id as programId,
        p.cn_name as programName
        from program p
        left join stadium s2 on p.stadium_id = s2.id
        left join venue v on v.id = p.venue_id
        where p.id in
        <foreach item="id" collection="programIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getTicketPrice" resultType="com.llwh.dcenter.vo.common.TicketPriceVo">
        select distinct t0.id,
        t0.program_id as programId,
        t1.schedule_id as scheduleId,
        t0.price as ticketPrice,
        t0.cn_name as cnName,
        t0.description as description,
        t0.remark as remark
        from ticket_price t0
        left join schedule_ticket_price t1 on t0.id = t1.ticket_price_id
        where t1.schedule_id in
        <foreach item="id" collection="scheduleIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getTicketPriceByProgramId" resultType="com.llwh.dcenter.vo.common.TicketPriceVo">
        select distinct t0.id,
        t0.program_id as programId,
        t0.price as ticketPrice,
        t0.cn_name as cnName,
        t0.description as description,
        t0.remark as remark
        from ticket_price t0
        inner join schedule_ticket_price t1 on t0.id = t1.ticket_price_id
        where t0.program_id in
        <foreach item="id" collection="programIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getScheduleExtendVos" resultType="com.llwh.dcenter.vo.common.ScheduleExtendVo">
        select s.id as id,
        s.cn_name as showName,
        s.play_time as playTime,
        p.id as programId,
        p.cn_name as programName
        from schedule s left join program p on s.program_id = p.id
        where s.id in
        <foreach item="id" collection="scheduleIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getTicketPriceByIds" resultType="com.llwh.dcenter.vo.common.TicketPriceVo">
        select distinct t0.id,
        t0.program_id as programId,
        t0.price as ticketPrice,
        t0.cn_name as cnName,
        t0.description as description,
        t0.remark as remark
        from ticket_price t0
        where t0.id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="findItemsByCompanyId" resultType="com.llwh.dcenter.vo.common.ApiUserVo">
        select id, appkey, company_id as companyId, guid, status
        from api_user
        where company_id = #{companyId};
    </select>
    <select id="getUserGroupIdsListByGroupType" resultType="Long">
        select id
        from user_group
        where company_id = #{companyId}
          and timeto > now()
          and status = 'Y'
          and group_type = #{groupType}
    </select>

    <select id="getSeatProgramIdList" resultType="java.lang.Long">
        select distinct program_id
        from schedule
        where company_id = #{companyId}
        and status != 'D'
        <if test="search.showStartTime != null and search.showEndTime != null">
            and play_time &gt;= #{search.showStartTime}
            and play_time &lt;= #{search.showEndTime}
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isPendingSales(search.saleStatus)">
            and external_time &gt; now()
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isInSales(search.saleStatus)">
            and external_time &lt; now()
            and external_end_time &gt; now()
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isSalesConcluded(search.saleStatus)">
            and external_end_time &lt; now()
        </if>
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.showIdList)">
            and id in
            <foreach collection="search.showIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getShowProgramIdList" resultType="java.lang.Long">
        select distinct program_id
        from open_show
        where company_id = #{companyId}
        and status != 'D'
        <if test="search.showStartTime != null and search.showEndTime != null">
            and play_time &gt;= #{search.showStartTime}
            and play_time &lt;= #{search.showEndTime}
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isPendingSales(search.saleStatus)">
            and external_time &gt; now()
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isInSales(search.saleStatus)">
            and external_time &lt; now()
            and external_end_time &gt; now()
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isSalesConcluded(search.saleStatus)">
            and external_end_time &lt; now()
        </if>
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.showIdList)">
            and id in
            <foreach collection="search.showIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getProgramListByAuthority" resultType="java.util.Map">
        select id,
        cn_name as cnName,
        support_seat as supportSeat
        from program
        where company_id = #{companyId}
        and status != 'D'
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
            and stadium_id in
            <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                #{stadiumId}
            </foreach>
        </if>
        <if test="search.stadiumId != null">
            and stadium_id = #{search.stadiumId}
        </if>
        <if test="search.venueId != null">
            and venue_id = #{search.venueId}
        </if>
        <if test="search.startTime != null and search.endTime != null">
            and start_time &gt;= #{search.startTime}
            and start_time &lt;= #{search.endTime}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.supportSeat)">
            and support_seat = #{search.supportSeat}
        </if>
        order by start_time desc
    </select>

    <select id="getApiUsersByGuids" resultType="com.llwh.dcenter.vo.common.ApiUserVo">
        select id, appkey, company_id as companyId, guid, status
        from api_user
        where guid in
        <foreach item="guid" collection="guidList" open="(" separator="," close=")">
            #{guid}
        </foreach>
    </select>

    <select id="getAllUser" resultType="com.llwh.dcenter.vo.common.TbsUserVo">
        select t1.id       as id,
               t1.realname as realName
        from tbs_user t1
        where t1.company_id = #{companyId}
    </select>
    <select id="getKeyAccountsByIds" resultType="com.llwh.dcenter.vo.common.KeyAccountVo">
        select
        id,
        unit_name as unitName,
        company_id as companyId
        from key_account
        <where>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(idList)">
                id in
                <foreach collection="idList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>


    <select id="getTicketTypesByProgramIds" resultType="com.llwh.dcenter.vo.common.TicketTypeVo">
        select distinct t0.id,
        t0.program_id as programId,
        t0.price as ticketPrice,
        t0.cn_name as cnName,
        t0.description as description,
        t0.remark as remark
        from ticket_type t0
        inner join show_ticket_type t1 on t0.id = t1.ticket_type_id
        where t0.program_id in
        <foreach item="programId" collection="programIdList" open="(" separator="," close=")">
            #{programId}
        </foreach>
    </select>

    <select id="searchScheduleList" resultType="java.util.Map">
        select
        id,
        cn_name as cnName,
        play_time as playTime,
        play_end_time as playEndTime
        from schedule
        where company_id = #{companyId}
        and status != 'D'
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(programIds)">
            and program_id in
            <foreach collection="programIds" item="programId" separator="," open="(" close=")">
                #{programId}
            </foreach>
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isPendingSales(saleStatus)">
            and external_time &gt; now()
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isInSales(saleStatus)">
            and external_time &lt; now()
            and external_end_time &gt; now()
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isSalesConcluded(saleStatus)">
            and external_end_time &lt; now()
        </if>
    </select>

    <select id="searchOpenShowList" resultType="java.util.Map">
        select
        id,
        cn_name as cnName,
        play_time as playTime,
        play_end_time as playEndTime
        from open_show
        where company_id = #{companyId}
        and status != 'D'
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(programIds)">
            and program_id in
            <foreach collection="programIds" item="programId" separator="," open="(" close=")">
                #{programId}
            </foreach>
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isPendingSales(saleStatus)">
            and external_time &gt; now()
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isInSales(saleStatus)">
            and external_time &lt; now()
            and external_end_time &gt; now()
        </if>
        <if test="@com.llwh.dcenter.constant.SaleStatus@isSalesConcluded(saleStatus)">
            and external_end_time &lt; now()
        </if>
    </select>

    <select id="getCulturalSkuSpData" resultType="com.llwh.dcenter.vo.common.SkuSpDataVo">
        select
        pccs.id as detailSkuId,
        pss.sp_data as spData
        from pms_cultural_creation_sku pccs
        left join pms_sku_stock pss on pss.id = pccs.sku_id
        where pccs.company_id = #{companyId}
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(detailSkuIdList)">
            and pccs.id in
            <foreach collection="detailSkuIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getCulturalSkuStockNum" resultType="java.util.Map">
        select
        id as detailSkuId,
        stock as stockNum,
        stock - sale as remainingNum
        from pms_cultural_creation_sku
        where company_id = #{companyId}
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(detailSkuIdList)">
            and id in
            <foreach collection="detailSkuIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getAllowCardIdList" resultType="com.llwh.dcenter.vo.common.GroupCardTypeRelationVo">
        select card_type_id, card_type from group_card_type_relate
        where company_id = #{companyId}
        and user_group_id = #{groupId}
    </select>

    <select id="getMembershipCardIdList" resultType="string">
        select id from membership_type
        where company_id = #{companyId}
        and user_group_id = #{groupId}
    </select>

    <select id="getBalanceCardIdList" resultType="string">
        select code from balance_card_type
        where company_id = #{companyId}
          and user_group_id = #{groupId}
    </select>

</mapper>

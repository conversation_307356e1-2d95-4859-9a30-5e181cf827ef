<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llwh.dcenter.mapper.ReportTicketTransactionGatherMapper">

    <sql id="commonWhere4DataAuthority">
        <choose>
            <when test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityProgramIds)">
                and (program_id in
                <foreach collection="search.authorityProgramIds" item="programId" separator="," open="(" close=")">
                    #{programId}
                </foreach>
                <trim prefix="or (" prefixOverrides="and" suffix=")">
                    <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                        and stadium_id in
                        <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                            #{stadiumId}
                        </foreach>
                    </if>
                    <if test="search.authorityUserGroupId != null">
                        and user_group_id = #{search.authorityUserGroupId}
                    </if>
                    <if test="search.authorityUserId != null">
                        and add_user_id = #{search.authorityUserId}
                    </if>
                </trim>
                )
            </when>
            <otherwise>
                <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                    and stadium_id in
                    <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                        #{stadiumId}
                    </foreach>
                </if>
                <if test="search.authorityUserGroupId != null">
                    and user_group_id = #{search.authorityUserGroupId}
                </if>
                <if test="search.authorityUserId != null">
                    and add_user_id = #{search.authorityUserId}
                </if>
            </otherwise>
        </choose>
    </sql>

    <select id="getCounts" resultType="java.util.Map">
        select program_id as programId,
               schedule_id as scheduleId,
               gateway_code as gatewayCode,
               sum(if(pay_type = 'pay', 1, -1)) as num,
               sum(IF(pay_type = 'pay', paid_amount, 0))    as paidAmount,
               sum(IF(pay_type = 'refund', paid_amount, 0)) as refundAmount
        from report_ticket_transaction_summary
        <include refid="commonWhere"></include>
        group by program_id, schedule_id, gateway_code;
    </select>

    <sql id="commonWhere">
        where ticket_type = 'ticket'
        and company_id = #{search.companyId}
        <if test="search.scheduleIdList !=null">
            and schedule_id in
            <foreach collection="search.scheduleIdList" item="scheduleId" open="(" close=")" separator=",">
                #{scheduleId}
            </foreach>
        </if>
        <if test="search.tickettimeFrom != null">
            and tickettime &gt;= #{search.ticketTimeFrom}
        </if>
        <if test="search.tickettimeTo != null">
            and tickettime &lt;= #{search.ticketTimeTo}
        </if>
        <include refid="commonWhere4DataAuthority"></include>
    </sql>

    <select id="getSourceCounts" resultType="java.util.Map">
        select program_id                                   as programId,
        schedule_id                                  as scheduleId,
        gateway_code                                 as gatewayCode,
        source_new as sourceNew,
        source_channel as sourceChannel,
        add_user_id as addUserId,
        sum(if(pay_type = 'pay', 1, -1))             as num,
        sum(IF(pay_type = 'pay', paid_amount, 0))    as paidAmount,
        sum(IF(pay_type = 'refund', paid_amount, 0)) as refundAmount
        from report_ticket_transaction_summary
        <include refid="commonWhere4Source"></include>
        group by program_id, schedule_id, gateway_code, source_new, source_channel, add_user_id
    </select>

    <sql id="commonWhere4Source">
        <include refid="commonWhere"></include>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.source)">
            and source_new = #{search.source}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.platform)">
            and source_channel = #{search.platform}
        </if>
        <if test="search.userGroupId != null">
            and user_group_id = #{search.userGroupId}
        </if>
    </sql>

    <select id="getOrderDetailIds" resultType="java.util.Map">
        select order_detail_id as orderDetailId, program_id as programId, schedule_id as scheduleId, gateway_code as gatewayCode, trade_no as tradeNo, refund_no as refundNo,
        pay_type as payType
        from report_ticket_transaction_summary
        <include refid="commonWhere"></include>
    </select>

    <select id="getInfos4pay" resultType="java.util.Map">
        select distinct
        o.trade_no as tradeNo,
        o.program_id as programId,
        o.schedule_id as scheduleId
        FROM
        seat_detail detail
        LEFT JOIN ticket_order o on o.trade_no=detail.trade_no
        where detail.id in
        <foreach collection="orderDetailIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getInfos4refund" resultType="java.util.Map">
        select distinct
        rfd.refund_serial_no as refundSerialNo,
        o.program_id as programId,
        o.schedule_id as scheduleId
        FROM
        order_refund_detail rfd
        LEFT JOIN ticket_order o on o.trade_no=rfd.trade_no
        where rfd.related_id in
        <foreach collection="orderDetailIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getSeatDiscountApportionmentCounts" resultType="java.util.Map">
        select
        trade_no as tradeNo,
        category,
        description,
        sum(if(pay_type = 'pay', 1, -1)) as num,
        sum(IF(pay_type = 'pay', discount_amount, 0))    as discountAmount,
        sum(IF(pay_type = 'refund', discount_amount, 0)) as refundDiscountAmount
        from report_seat_discount_apportionment
        where order_type = 'ticket'
        and category in ('balance', 'multicard', 'points')
        and trade_no in
        <foreach collection="nos" item="num" open="(" close=")" separator=",">
            #{num}
        </foreach>
        group by 1,2,3;
    </select>

    <select id="getOrderDetailIds4Source" resultType="java.util.Map">
        select order_detail_id as orderDetailId, program_id as programId, schedule_id as scheduleId, gateway_code as gatewayCode, trade_no as tradeNo, refund_no as refundNo, add_user_id as addUserId, source_new as sourceNew, source_channel as sourceChannel,
        pay_type as payType
        from report_ticket_transaction_summary
        <include refid="commonWhere4Source"></include>
    </select>

    <select id="getInfos4pay4Source" resultType="java.util.Map">
        select distinct
        o.trade_no as tradeNo,
        o.program_id as programId,
        o.schedule_id as scheduleId,
        case
        when (select count(1) from api_user au where au.guid = o.add_user_id) > 0 then '线上自营'
        when g.group_type = 'ota' then '线上代理'
        when g.group_type = 'agent' then '线下代理'
        when o.platform = 'OFFLINE' then '线下票房'
        end as sourceNew,
        case
        when (select count(1) from api_user au where au.guid = o.add_user_id) > 0 then o.platform
        when g.group_type in ('ota', 'agent') or o.platform = 'OFFLINE' then g.group_name
        end as sourceChannel,
        detail.add_user_id as addUserId
        FROM
        seat_detail detail
        LEFT JOIN ticket_order o on o.trade_no=detail.trade_no
        LEFT JOIN user_group g on o.user_group_id=g.id
        where detail.id in
        <foreach collection="orderDetailIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getInfos4refund4Source" resultType="java.util.Map">
        select distinct
        rfd.refund_serial_no as refundSerialNo,
        o.program_id as programId,
        o.schedule_id as scheduleId,
        case
        when (select count(1) from api_user au where au.guid = o.add_user_id) > 0 then '线上自营'
        when g.group_type = 'ota' then '线上代理'
        when g.group_type = 'agent' then '线下代理'
        when o.platform = 'OFFLINE' then '线下票房'
        end as sourceNew,
        case
        when (select count(1) from api_user au where au.guid = o.add_user_id) > 0 then o.platform
        when g.group_type in ('ota', 'agent') or o.platform = 'OFFLINE' then g.group_name
        end as sourceChannel,
        o.add_user_id as addUserId
        FROM
        order_refund_detail rfd
        LEFT JOIN ticket_order o on o.trade_no=rfd.trade_no
        LEFT JOIN user_group g on o.user_group_id=g.id
        where rfd.related_id in
        <foreach collection="orderDetailIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>

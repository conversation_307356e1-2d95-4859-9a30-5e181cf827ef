<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.ReportMembershipMapper">


    <select id="getCounts" resultType="com.llwh.dcenter.vo.report.ReportMemberShipVo">
        select t0.id as id,
        t0.cardno as cardno,
        t1.name as membershipTypeName,
        t0.card_type as cardType,
        t0.totalnum as totalnum,
        t0.usednum as usednum,
        t0.contact_name as contactName,
        t0.contact_mobile as contactMobile,
        t0.contact_cert_no as contactCertNo,
        t0.status as status,
        t0.timefrom as timefrom,
        t0.timeto as timeto,
        t0.trade_no as tradeNo,
        t1.price as price,
        t0.paid_amount
        from membership t0
        left join membership_type t1 on t1.id = t0.membership_type_id
        <where>
            t0.company_id = #{search.companyId}
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.contactMobile)">
                and t0.contact_mobile = #{search.contactMobile}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.cardno)">
                and t0.cardno = #{search.cardno}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.tradeNo)">
                and t0.trade_no = #{search.tradeNo}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.membershipTypeId)">
                and t0.membership_type_id = #{search.membershipTypeId}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.status)">
                <choose>
                    <when test="search.status == 'valid' ">
                        and t0.status = 'valid' and t0.timeto &gt;= NOW()
                    </when>
                    <when test="search.status == 'unvalid' ">
                        and (t0.status = 'unvalid' or (t0.status = 'valid' and t0.timeto &lt; NOW()))
                    </when>
                    <otherwise>
                        and t0.status = #{search.status}
                    </otherwise>
                </choose>
            </if>
        </where>
        order by t0.id desc
    </select>
</mapper>

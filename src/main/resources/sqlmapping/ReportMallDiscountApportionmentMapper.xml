<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.ReportMallDiscountApportionmentMapper">

    <select id="getMallTradeNo" resultType="Map">
        select t0.trade_no as tradeNo,
        t0.company_id as companyId
        from oms_order_item t0
        where t0.pay_status like 'paid%'
        <choose>
            <when test="@org.apache.commons.lang3.StringUtils@isNotBlank(tradeNo)">
                and t0.trade_no = #{tradeNo}
            </when>
            <otherwise>
                and t0.updatetime &gt;= #{startTime}
                and t0.updatetime &lt; #{endTime}
            </otherwise>
        </choose>
    </select>

    <select id="getMallRefundSerialNo" resultType="Map">
        select t0.refund_serial_no as serialNo,
        t0.company_id as companyId
        from order_refund_detail t0
        left join order_refund t1 on t1.serial_no = t0.refund_serial_no
        where t1.order_type = 'ticket'
        and t1.origin_status like 'paid%'
        and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success', 'refund_audited', 'refund_running')
        <choose>
            <when test="@org.apache.commons.lang3.StringUtils@isNotBlank(serialNo)">
                and t0.refund_serial_no = #{serialNo}
            </when>
            <otherwise>
                and t0.updatetime &gt;= #{startTime}
                and t0.updatetime &lt; #{endTime}
            </otherwise>
        </choose>
    </select>
</mapper>

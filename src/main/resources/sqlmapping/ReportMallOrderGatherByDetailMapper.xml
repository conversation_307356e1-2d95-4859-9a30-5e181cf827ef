<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llwh.dcenter.mapper.ReportMallOrderGatherByDetailMapper">

    <select id="getMallCount" resultType="com.llwh.dcenter.vo.report.ReportMallOrderGatherVo">
        select order_type,
        detail_product_id,
        product_name,
        sku_name,
        sku_code,
        detail_sku_id,
        unit_price,
        agency_id,
        sum(quantity)          as saleCount,
        sum(amount) as amount,
        sum(discount)     as discount,
        sum(paid_amount)  as paidAmount,
        sum(point_value)  as pointValue
        from report_mall_order_detail
        <where>
            and company_id = #{search.companyId}
            and order_type = #{search.orderType}
            and pay_type = 'pay'
            <if test="search.orderTimeFrom != null">
                and order_time &gt;= #{search.orderTimeFrom}
            </if>
            <if test="search.orderTimeTo != null">
                and order_time &lt; #{search.orderTimeTo}
            </if>
            <if test="search.agencyId != null">
                and agency_id = #{search.agencyId}
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.detailProductIdList)">
                and detail_product_id in
                <foreach collection="search.detailProductIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.saleUserGroupIdList)">
                and sale_user_group_id in
                <foreach collection="search.saleUserGroupIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.agencyIdList)">
                and agency_id in
                <foreach collection="search.agencyIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="search.detailProductId != null">
                and detail_product_id = #{search.detailProductId}
            </if>
            <if test="search.detailSkuId != null">
                and detail_sku_id = #{search.detailSkuId}
            </if>
        </where>
        group by order_type, detail_product_id, product_name, sku_name, sku_code, detail_sku_id, unit_price, agency_id
    </select>

    <select id="getMallRefundCount" resultType="com.llwh.dcenter.vo.report.ReportMallOrderGatherVo">
        select order_type,
        detail_product_id,
        product_name,
        sku_name,
        sku_code,
        detail_sku_id,
        agency_id,
        -unit_price as unitPrice,
        sum(quantity)          as refundCount,
        sum(amount) as refundAmount,
        sum(discount)     as refundDiscount,
        sum(paid_amount)  as refundPaidAmount,
        sum(point_value)  as refundPointValue
        from report_mall_order_detail
        <where>
            and company_id = #{search.companyId}
            and order_type = #{search.orderType}
            and pay_type = 'refund'
            <if test="search.orderTimeFrom != null">
                and order_time &gt;= #{search.orderTimeFrom}
            </if>
            <if test="search.orderTimeTo != null">
                and order_time &lt; #{search.orderTimeTo}
            </if>
            <if test="search.agencyId != null">
                and agency_id = #{search.agencyId}
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.detailProductIdList)">
                and detail_product_id in
                <foreach collection="search.detailProductIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.saleUserGroupIdList)">
                and sale_user_group_id in
                <foreach collection="search.saleUserGroupIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.agencyIdList)">
                and agency_id in
                <foreach collection="search.agencyIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="search.detailProductId != null">
                and detail_product_id = #{search.detailProductId}
            </if>
            <if test="search.detailSkuId != null">
                and detail_sku_id = #{search.detailSkuId}
            </if>
        </where>
        group by order_type, detail_product_id, product_name, sku_name, sku_code, detail_sku_id, unit_price, agency_id
    </select>
</mapper>

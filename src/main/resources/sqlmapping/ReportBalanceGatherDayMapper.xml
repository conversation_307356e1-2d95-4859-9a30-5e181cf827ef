<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llwh.dcenter.mapper.ReportBalanceGatherDayMapper">

    <select id="getBalanceUpdateDate" resultType="date">
        select distinct date(updatetime) as tradeDate
        from membership_charge_record
        where status = 'Y'
          and updatetime &gt;= #{startTime}
          and updatetime &lt; #{endTime}
        union
        select distinct date(updatetime) as tradeDate
        from membership_expend_record
        where updatetime &gt;= #{startTime}
          and updatetime &lt; #{endTime}
    </select>


    <select id="getChargeCount" resultType="com.llwh.dcenter.model.ReportBalanceGatherDay">
        select date(mcr.updatetime) as tradeDate,
        mt.id as membershipTypeId,
        mt.name as membershipTypeName,
        mcr.company_id as companyId,
        sum(amount) as chargeTotal
        from membership_charge_record mcr
        left join balance_card m on m.cardno = mcr.cardno
        left join balance_card_type mt on mt.id = m.card_type_id
        where mcr.status = 'Y'
        and mcr.updatetime &gt;= #{startTime}
        and mcr.updatetime &lt; #{endTime}
        group by 1,2,3,4
    </select>

    <select id="getExpendCount" resultType="com.llwh.dcenter.model.ReportBalanceGatherDay">
        select date(mcr.updatetime) as tradeDate,
        mt.id as membershipTypeId,
        mt.name as membershipTypeName,
        mcr.company_id as companyId,
        -sum(amount) as spendTotal
        from membership_expend_record mcr
        left join balance_card m on m.cardno = mcr.cardno
        left join balance_card_type mt on mt.id = m.card_type_id
        where mcr.updatetime &gt;= #{startTime}
          and mcr.updatetime &lt; #{endTime}
        group by 1,2,3,4
    </select>

    <select id="getTotals" resultType="com.llwh.dcenter.model.ReportBalanceGatherDay">
        select sum(charge_total) as chargeTotal,
        sum(spend_total) as spendTotal
        from report_balance_gather_day
        <where>
            <if test="search.tradeDateFrom != null">
                and trade_date &gt;= #{search.tradeDateFrom}
            </if>
            <if test="search.tradeDateTo != null">
                and trade_date &lt;= #{search.tradeDateTo}
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.membershipTypeIdList)">
                and membership_type_id in
                <foreach collection="search.membershipTypeIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            and company_id = #{search.companyId}
        </where>
    </select>
</mapper>

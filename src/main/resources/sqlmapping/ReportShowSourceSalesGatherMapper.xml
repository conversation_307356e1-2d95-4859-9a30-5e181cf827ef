<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.ReportShowSourceSalesGatherMapper">

    <sql id="commonWhere4DataAuthority">
        <choose>
            <when test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityProgramIds)">
                and (program_id in
                <foreach collection="search.authorityProgramIds" item="programId" separator="," open="(" close=")">
                    #{programId}
                </foreach>
                <trim prefix="or (" prefixOverrides="and" suffix=")">
                    <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                        and stadium_id in
                        <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                            #{stadiumId}
                        </foreach>
                    </if>
                    <if test="search.authorityUserGroupId != null">
                        and user_group_id = #{search.authorityUserGroupId}
                    </if>
                    <if test="search.authorityUserId != null">
                        and sale_user_id = #{search.authorityUserId}
                    </if>
                </trim>
                )
            </when>
            <otherwise>
                <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                    and stadium_id in
                    <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                        #{stadiumId}
                    </foreach>
                </if>
                <if test="search.authorityUserGroupId != null">
                    and user_group_id = #{search.authorityUserGroupId}
                </if>
                <if test="search.authorityUserId != null">
                    and sale_user_id = #{search.authorityUserId}
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="commonWhere">
        where company_id = #{companyId}
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.showIdList)">
            and show_id in
            <foreach collection="search.showIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search.ticketDateFrom != null">
            and tickettime &gt;= #{search.ticketDateFrom}
        </if>
        <if test="search.ticketDateTo != null">
            and tickettime &lt;= #{search.ticketDateTo}
        </if>
        <include refid="commonWhere4DataAuthority"></include>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.platform)">
            and platform = #{search.platform}
        </if>
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.addUserIdList4Source)">
            and sale_user_id in
            <foreach collection="search.addUserIdList4Source" item="addUserIdSource" separator="," open="(" close=")">
                #{addUserIdSource}
            </foreach>
        </if>
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.userGroupIdList4Source)">
            and user_group_id in
            <foreach collection="search.userGroupIdList4Source" item="userGroupIdSource" separator="," open="(" close=")">
                #{userGroupIdSource}
            </foreach>
        </if>
        <if test="search.userGroupId != null">
            and user_group_id = #{search.userGroupId}
        </if>
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.originList)">
            and origin in
            <foreach collection="search.originList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="getCounts" resultType="java.util.Map">
        select
        user_group_id as userGroupId,
        sale_user_id as saleUserId,
        platform,
        sell_type as sellType,
        ticket_type_id as ticketTypeId,
        ifnull(sum(quantity), 0) as quantity,
        ifnull(sum(amount), 0.00) as amount,
        sum(real_pay) as paidAmount,
        convert(sum(settlement_amount), DECIMAL(15, 2)) as settlementAmount,
        ifnull(sum(if(pay_type = 'refund', quantity, 0)), 0) as refundQuantity,
        sum(if(pay_type = 'refund', real_pay, 0)) as refundPaidAmount
        from report_stand_check_detail
        <include refid="commonWhere"></include>
        group by 1,2,3,4,5
    </select>

    <select id="getProgramCounts" resultType="java.util.Map">
        select
        user_group_id as userGroupId,
        sale_user_id as saleUserId,
        platform,
        program_name as programName,
        play_time as showName,
        ticket_type_id as ticketTypeId,
        ifnull(sum(quantity), 0) as quantity,
        ifnull(sum(amount), 0.00) as amount,
        sum(real_pay) as paidAmount,
        convert(sum(settlement_amount), DECIMAL(15, 2)) as settlementAmount,
        ifnull(sum(if(pay_type = 'refund', quantity, 0)), 0) as refundQuantity,
        sum(if(pay_type = 'refund', real_pay, 0)) as refundPaidAmount
        from report_stand_check_detail
        <include refid="commonWhere"></include>
        group by 1,2,3,4,5,6
    </select>

    <select id="getSourceProgramWithoutShowCounts" resultType="java.util.Map">
        select
        user_group_id as userGroupId,
        sale_user_id as saleUserId,
        platform,
        program_name as programName,
        ticket_type_id as ticketTypeId,
        ifnull(sum(quantity), 0) as quantity,
        ifnull(sum(amount), 0.00) as amount,
        sum(real_pay) as paidAmount,
        convert(sum(settlement_amount), DECIMAL(15, 2)) as settlementAmount,
        ifnull(sum(if(pay_type = 'refund', quantity, 0)), 0) as refundQuantity,
        sum(if(pay_type = 'refund', real_pay, 0)) as refundPaidAmount
        from report_stand_check_detail
        <include refid="commonWhere"></include>
        group by 1,2,3,4,5
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llwh.dcenter.mapper.ReportSeatSaleGatherDayMapper">

    <select id="getStandUpdateDate" resultType="date">
        select distinct date(tickettime) as ticketdate
        from ticket_order
        where pay_status like 'paid%'
        <choose>
            <when test="@org.apache.commons.lang3.StringUtils@isNotBlank(tradeNo)">
                and trade_no = #{tradeNo}
            </when>
            <otherwise>
                and updatetime &gt;= #{startTime}
                and updatetime &lt; #{endTime}
            </otherwise>
        </choose>
        and tickettime is not NULL
        union
        select distinct date(ord.tickettime) as ticketdate
        from order_refund_detail ord
        left join order_refund o on ord.refund_serial_no = o.serial_no
        where o.order_type = 'ticket'
        and o.origin_status like 'paid%'
        <choose>
            <when test="@org.apache.commons.lang3.StringUtils@isNotBlank(tradeNo)">
                and ord.trade_no = #{tradeNo}
            </when>
            <otherwise>
                and ord.updatetime &gt;= #{startTime}
                and ord.updatetime &lt; #{endTime}
            </otherwise>
        </choose>
        and ord.refund_status in ('ticket_success', 'refund_apply', 'refund_success', 'refund_audited',
        'refund_running')
        and ord.tickettime is not NULL
    </select>

    <select id="getSeatSaleCount" resultType="com.llwh.dcenter.model.ReportSeatSaleGatherDay">
        select t0.stadium_id        as stadiumId,
        t2.venue_id          as venueId,
        t1.user_group_id     as userGroupId,
        t1.program_id        as programId,
        t0.ticket_price_id   as ticketPriceId,
        t0.ticket_price      as ticketPrice,
        t1.schedule_id       as showId,
        t0.company_id        as companyId,
        t8.city_code         as cityCode,
        t1.sell_type         as sellType,
        t1.platform          as platform,
        t1.add_user_id       as addUserId,
        date(t1.tickettime)  as ticketDate,
        count(1)             as saleCount,
        sum(t0.ticket_price) as amount,
        sum(t0.discount)     as discount,
        sum(t0.paid_amount)  as paidAmount,
        sum(if(t0.gateway_code like 'free%',0,t0.paid_amount))  as settlementAmount
        from seat_detail t0
        left join ticket_order t1 on t1.trade_no = t0.trade_no
        left join program t2 on t2.id = t1.program_id
        left join stadium t3 on t3.id = t1.stadium_id
        left join venue t4 on t4.id = t2.venue_id
        left join schedule t5 on t5.id = t1.schedule_id
        left join user_group t7 on t7.id = t1.user_group_id
        left join city t8 on t8.city_code = t3.city_code
        where t1.tickettime &gt;= #{startTime}
        and t1.tickettime &lt; #{endTime}
        <if test="scheduleId != null">
            and t1.schedule_id = #{scheduleId}
        </if>
        and t0.pay_status like 'paid%'
        GROUP BY t0.stadium_id,t2.venue_id,t1.user_group_id,t1.program_id,t0.ticket_price_id, t0.ticket_price,t1.schedule_id,
                 t0.company_id,t8.city_code, date(t1.tickettime),t1.sell_type,t1.add_user_id,t1.platform
    </select>

    <select id="getSeatRefundCount" resultType="com.llwh.dcenter.model.ReportSeatSaleGatherDay">
        select t1.stadium_id         as stadiumId,
        t4.venue_id           as venueId,
        t3.user_group_id      as userGroupId,
        t3.program_id         as programId,
        t0.ticket_price_id    as ticketPriceId,
        t0.ticket_price       as ticketPrice,
        t3.schedule_id        as showId,
        t0.company_id         as companyId,
        t10.city_code         as cityCode,
        t3.sell_type         as sellType,
        t3.platform          as platform,
        t3.add_user_id       as addUserId,
        date(t0.tickettime)   as ticketDate,
        -count(1)             as refundCount,
        -sum(t0.ticket_price) as refundAmount,
        -sum(t2.discount)     as refundDiscount,
        -sum(t0.real_pay)     as refundPaidAmount,
        -sum(if(t2.gateway_code like 'free%',0,t2.paid_amount))  as refundSettlementAmount
        from order_refund_detail t0
        left join order_refund t1 on t1.serial_no = t0.refund_serial_no
        left join seat_detail t2 on t2.uuid = t0.uuid
        left join ticket_order t3 on t3.trade_no = t1.trade_no
        left join program t4 on t4.id = t3.program_id
        left join stadium t5 on t5.id = t1.stadium_id
        left join venue t6 on t6.id = t4.venue_id
        left join schedule t7 on t7.id = t3.schedule_id
        left join user_group t9 on t9.id = t3.user_group_id
        left join city t10 on t10.city_code = t5.city_code
        where t1.order_type = 'ticket'
        and t1.origin_status like 'paid%'
        and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success', 'refund_audited', 'refund_running')
        and t0.tickettime &gt;= #{startTime}
        and t0.tickettime &lt; #{endTime}
        <if test="scheduleId != null">
            and t3.schedule_id = #{scheduleId}
        </if>
        group by t1.stadium_id, t4.venue_id, t3.user_group_id, t3.program_id,
        t0.ticket_price_id, t0.ticket_price,t3.schedule_id,t0.company_id,t10.city_code,date(t0.tickettime),t3.sell_type,t3.add_user_id,t3.platform
    </select>

    <select id="getTotalCount" resultType="com.llwh.dcenter.model.ReportSeatSaleGatherDay">
        select * from report_seat_sale_gather_day
        <where>
        <if test="search.datefrom != null">
            and ticket_date &gt;= #{search.datefrom}
        </if>
        <if test="search.dateto != null">
            and ticket_date &lt; #{search.dateto}
        </if>
        <if test="search.playStartTime != null">
            and play_time &gt;= #{search.playStartTime}
        </if>
        <if test="search.playEndTime != null">
            and play_time &lt; #{search.playEndTime}
        </if>
            <if test="search.stadiumId != null">
                and stadium_id = #{search.stadiumId}
            </if>
            <if test="search.venueId != null">
                and venue_id = #{search.venueId}
            </if>
        <if test="search.programId != null">
            and program_id = #{search.programId}
        </if>
        <if test="search.showNameId != null">
            and show_id = #{search.showNameId}
        </if>
        <if test="search.userGroupId != null">
            and user_group_id = #{search.userGroupId}
        </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.userGroupIdList)">
                and user_group_id in
                <foreach collection="search.userGroupIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programCode)">
            and program_code like #{search.programCode}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.cityName)">
            and city_name like #{search.cityName}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.stadiumName)">
            and stadium_name like #{search.stadiumName}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.venueName)">
            and venue_name like #{search.venueName}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programName)">
            and program_name like #{search.programName}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.category)">
            and category like #{search.category}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.smallCategory)">
            and small_category like #{search.smallCategory}
        </if>
        and company_id = #{search.companyId}
        </where>
        order by user_group_id,ticket_date,ticket_price desc,id
    </select>

    <select id="getTotals" resultType="com.llwh.dcenter.model.ReportSeatSaleGatherDay">
        select sum(sale_count) as saleCount,
        sum(paid_amount) as paidAmount,
        sum(refund_count) as refundCount,
        sum(refund_paid_amount) as refundPaidAmount,
        sum(total_quantity) as totalQuantity,
        sum(total_paid_amount) as totalPaidAmount,
        sum(total_settlement_amount) as settlementAmount
        from report_seat_sale_gather_day
        <where>
            <if test="search.datefrom != null">
                and ticket_date &gt;= #{search.datefrom}
            </if>
            <if test="search.dateto != null">
                and ticket_date &lt; #{search.dateto}
            </if>
            <if test="search.playStartTime != null">
                and play_time &gt;= #{search.playStartTime}
            </if>
            <if test="search.playEndTime != null">
                and play_time &lt; #{search.playEndTime}
            </if>
            <if test="search.stadiumId != null">
                and stadium_id = #{search.stadiumId}
            </if>
            <if test="search.venueId != null">
                and venue_id = #{search.venueId}
            </if>
            <if test="search.programId != null">
                and program_id = #{search.programId}
            </if>
            <if test="search.showNameId != null">
                and show_id = #{search.showNameId}
            </if>
            <if test="search.ticketTypeId != null">
                and ticket_type_id = #{search.ticketTypeId}
            </if>
            <if test="search.userGroupId != null">
                and user_group_id = #{search.userGroupId}
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.userGroupIdList)">
                and user_group_id in
                <foreach collection="search.userGroupIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programCode)">
                and program_code like #{search.programCode}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.cityName)">
                and city_name like #{search.cityName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.stadiumName)">
                and stadium_name like #{search.stadiumName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.venueName)">
                and venue_name like #{search.venueName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programName)">
                and program_name like #{search.programName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.category)">
                and category like #{search.category}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.smallCategory)">
                and small_category like #{search.smallCategory}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.sellType)">
                and sell_type = #{search.sellType}
            </if>

            <choose>
                <when test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityProgramIds)">
                    and (program_id in
                    <foreach collection="search.authorityProgramIds" item="programId" separator="," open="(" close=")">
                        #{programId}
                    </foreach>
                    <trim prefix="or (" prefixOverrides="and" suffix=")">
                        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                            and stadium_id in
                            <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                                #{stadiumId}
                            </foreach>
                        </if>
                        <if test="search.authorityUserGroupId != null">
                            and user_group_id = #{search.authorityUserGroupId}
                        </if>
                    </trim>
                    )
                </when>
                <otherwise>
                    <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                        and stadium_id in
                        <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                            #{stadiumId}
                        </foreach>
                    </if>
                    <if test="search.authorityUserGroupId != null">
                        and user_group_id = #{search.authorityUserGroupId}
                    </if>
                </otherwise>
            </choose>
            and company_id = #{search.companyId}
        </where>
    </select>


    <select id="getRevenueTotalCount" resultType="com.llwh.dcenter.model.ReportSeatSaleGatherDay">
        select ticket_date,
        city_name,
        program_id,
        program_code,
        program_name,
        stadium_name,
        venue_name,
        source,
        source_channel,
        sum(sale_count) as saleCount,
        sum(paid_amount) as paidAmount,
        sum(settlement_amount) as settlementAmount,
        sum(refund_count) as refundCount,
        sum(refund_paid_amount) as refundPaidAmount,
        sum(refund_settlement_amount) as refundSettlementAmount,
        sum(total_quantity) as totalQuantity,
        sum(total_paid_amount) as totalPaidAmount,
        sum(total_settlement_amount) as totalSettlementAmount
        from report_seat_sale_gather_day
        <where>
            <if test="search.datefrom != null">
                and ticket_date &gt;= #{search.datefrom}
            </if>
            <if test="search.dateto != null">
                and ticket_date &lt;= #{search.dateto}
            </if>
            <if test="search.playStartTime != null">
                and play_time &gt;= #{search.playStartTime}
            </if>
            <if test="search.playEndTime != null">
                and play_time &lt;= #{search.playEndTime}
            </if>
            <if test="search.stadiumId != null">
                and stadium_id = #{search.stadiumId}
            </if>
            <if test="search.venueId != null">
                and venue_id = #{search.venueId}
            </if>
            <if test="search.programId != null">
                and program_id = #{search.programId}
            </if>
            <if test="search.userGroupId != null">
                and user_group_id = #{search.userGroupId}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programCode)">
                and program_code like #{search.programCode}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.cityName)">
                and city_name like #{search.cityName}
            </if>
            <choose>
                <when test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityProgramIds)">
                    and (program_id in
                    <foreach collection="search.authorityProgramIds" item="programId" separator="," open="(" close=")">
                        #{programId}
                    </foreach>
                    <trim prefix="or (" prefixOverrides="and" suffix=")">
                        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                            and stadium_id in
                            <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="("
                                     close=")">
                                #{stadiumId}
                            </foreach>
                        </if>
                        <if test="search.authorityUserGroupId != null">
                            and user_group_id = #{search.authorityUserGroupId}
                        </if>
                    </trim>
                    )
                </when>
                <otherwise>
                    <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                        and stadium_id in
                        <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="("
                                 close=")">
                            #{stadiumId}
                        </foreach>
                    </if>
                    <if test="search.authorityUserGroupId != null">
                        and user_group_id = #{search.authorityUserGroupId}
                    </if>
                </otherwise>
            </choose>

            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.source)">
                and source = #{search.source}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.sonOfChannel)">
                and source_channel = #{search.sonOfChannel}
            </if>
            and company_id = #{search.companyId}
        </where>
        group by ticket_date,city_name,program_id,program_code,program_name,stadium_name,venue_name,source,source_channel
        order by ticket_date,max(id)
    </select>

    <select id="getRevenueTotals" resultType="com.llwh.dcenter.model.ReportSeatSaleGatherDay">
        select sum(sale_count) as saleCount,
        sum(paid_amount) as paidAmount,
        sum(refund_count) as refundCount,
        sum(refund_paid_amount) as refundPaidAmount,
        sum(total_quantity) as totalQuantity,
        sum(total_paid_amount) as totalPaidAmount
        from report_seat_sale_gather_day
        <where>
            <if test="search.datefrom != null">
                and ticket_date &gt;= #{search.datefrom}
            </if>
            <if test="search.dateto != null">
                and ticket_date &lt; #{search.dateto}
            </if>
            <if test="search.playStartTime != null">
                and play_time &gt;= #{search.playStartTime}
            </if>
            <if test="search.playEndTime != null">
                and play_time &lt; #{search.playEndTime}
            </if>
            <if test="search.stadiumId != null">
                and stadium_id = #{search.stadiumId}
            </if>
            <if test="search.venueId != null">
                and venue_id = #{search.venueId}
            </if>
            <if test="search.programId != null">
                and program_id = #{search.programId}
            </if>
            <if test="search.userGroupId != null">
                and user_group_id = #{search.userGroupId}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programCode)">
                and program_code like #{search.programCode}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.cityName)">
                and city_name like #{search.cityName}
            </if>
            and company_id = #{search.companyId}
        </where>
    </select>
</mapper>

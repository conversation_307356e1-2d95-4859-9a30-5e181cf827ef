<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.ReportShowOrderMapper">


    <select id="searchShowOrder" resultType="com.llwh.dcenter.vo.report.ReportShowOrderVo">
        select s.id as id,
        s.trade_no as tradeNo,
        s.out_trade_no as outTradeNo,
        s.payseqno as payseqno,
        os.play_time as playTime,
        os.cn_name as playName,
        s.addtime as addtime,
        s.contact_mobile as contactMobile,
        s.member_name as memberName,
        s.mobile as mobile,
        p.cn_name as programName,
        s.quantity as quantity,
        s.seat_amount as seatAmount,
        s.paid_amount as paidAmount,
        s.fee as fee,
        s.discount as discount,
        s.refund_amount as refundAmount,
        s.paid_amount - s.refund_amount as remainAmount,
        s.gateway_code as gatewayCode,
        s.merchant_code        as merchantCode,
        s.paidtime as paidtime,
        s.origin as origin,
        s.otherfee as otherfee,
        u.group_name as userGroupName,
        tu.realname as tbsUserName,
        s.dynamic_value as dynamicValue,
        s.transport as transport,
        s.express_address as expressAddress,
        s.contact_name as contactName,
        s.platform as platform,
        s.lock_no as lockNo,
        ka.unit_name as unitName,
        s.addition_info as additionInfo,
        s.pay_status as payStatus,
        group_concat(od.category) as category,
        group_concat(od.discount_amount) as discountAmount
        from show_order s
        left join program p on (s.program_id = p.id)
        left join open_show os on (s.show_id = os.id)
        left join user_group u on (s.user_group_id = u.id)
        left join tbs_user tu on (tu.id = s.add_user_id)
        left join key_account ka on (ka.id = s.customer_id)
        left join order_discount od on (s.trade_no = od.trade_no)
        <where>
            s.trade_no is not null
            <if test="search.companyId !=null">
                and s.company_id = #{search.companyId}
            </if>
            <if test="search.memberId != null">
                and s.member_id= #{search.memberId}
            </if>
            <if test="search.tradeNos != null">
                and s.trade_no in
                <foreach collection="search.tradeNos" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.platform)">
                and s.platform = #{search.platform}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.unitName)">
                and ka.unit_name like #{search.unitName}
            </if>
            <if test="search.outTradeNos != null">
                and s.out_trade_no in
                <foreach collection="search.outTradeNos" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.payseqno)">
                and s.payseqno = #{search.payseqno}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.origin)">
                and s.origin = #{search.origin}
            </if>
            <if test="search.payStatus =='paidNoTickets'">
                and s.pay_status like 'paid%'
                and s.pay_status not
                in('paid_success','paid_return','paid_return_cert','paid_return_succ','paid_return_fail')
            </if>
            <if test="search.payStatus =='paidSuccess'">
                and s.pay_status = 'paid_success'
            </if>
            <if test="search.payStatus =='paidRefund'">
                and s.pay_status like 'paid_return%'
            </if>
            <if test="search.payStatus =='unPaid'">
                and (s.pay_status like 'cancel%' or s.pay_status like 'new%' )
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.contactName)">
                and s.contact_name = #{search.contactName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.contactMobile)">
                and s.contact_mobile = #{search.contactMobile}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.mobile)">
                and s.mobile = #{search.mobile}
            </if>
            <if test="search.stadiumId != null">
                and s.stadium_id = #{search.stadiumId}
            </if>
            <if test="search.venueId != null">
                and os.venue_id = #{search.venueId}
            </if>
            <if test="search.showId != null">
                and s.show_id = #{search.showId}
            </if>
            <if test="search.playStartTime != null">
                and s.play_time &gt; #{search.playStartTime}
            </if>
            <if test="search.playEndTime != null">
                and s.play_time &lt; #{search.playEndTime}
            </if>
            <if test="search.addtimeFrom != null">
                and s.addtime &gt; #{search.addtimeFrom}
            </if>
            <if test="search.addtimeTo != null">
                and s.addtime &lt; #{search.addtimeTo}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.gatewayCode)">
                and s.gateway_code = #{search.gatewayCode}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.gatewayCode)">
                and s.merchant_code = #{search.merchantCode}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.lockNo)">
                and s.lock_no = #{search.lockNo}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.barcode)">
                and s.trade_no in (select distinct trade_no from show_order_detail where barcode = #{search.barcode})
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.uuid)">
                and s.trade_no = (select distinct trade_no from show_order_detail where uuid = #{search.uuid})
            </if>
            <if test="search.programId != null">
                and s.program_id = #{search.programId}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.transport)">
                and s.transport = #{search.transport}
            </if>
            <if test='search.refundApply == "Y"'>
                and exists ( SELECT 1 FROM order_refund rf where rf.order_type='show' and rf.refund_status !='reject'
                and s.trade_no=rf.trade_no)
            </if>
            <if test='search.refundApply == "N"'>
                and not exists ( SELECT 1 FROM order_refund rf where rf.order_Type='show' and rf.refund_status
                !='reject' and s.trade_no=rf.trade_no)
            </if>
            <choose>
                <when test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityProgramIds)">
                    and (s.program_id in
                    <foreach collection="search.authorityProgramIds" item="programId" separator="," open="(" close=")">
                        #{programId}
                    </foreach>
                    <trim prefix="or (" prefixOverrides="and" suffix=")">
                        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                            and s.stadium_id in
                            <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                                #{stadiumId}
                            </foreach>
                        </if>
                        <if test="search.authorityUserGroupId != null">
                            and s.user_group_id = #{search.authorityUserGroupId}
                        </if>
                        <if test="search.authorityUserId != null">
                            and s.sale_user_id = #{search.authorityUserId}
                        </if>
                    </trim>
                    )
                </when>
                <otherwise>
                    <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.authorityStadiumIds)">
                        and s.stadium_id in
                        <foreach collection="search.authorityStadiumIds" item="stadiumId" separator="," open="(" close=")">
                            #{stadiumId}
                        </foreach>
                    </if>
                    <if test="search.authorityUserGroupId != null">
                        and s.user_group_id = #{search.authorityUserGroupId}
                    </if>
                    <if test="search.authorityUserId != null">
                        and s.sale_user_id = #{search.authorityUserId}
                    </if>
                </otherwise>
            </choose>
        </where>
        GROUP BY s.id, s.trade_no, s.out_trade_no, s.payseqno, os.play_time, os.cn_name, s.addtime, s.contact_mobile,
        s.member_name, s.mobile, p.cn_name, s.quantity, s.seat_amount, s.paid_amount, s.fee,
        s.discount, s.refund_amount, s.gateway_code, s.paidtime, s.origin, s.otherfee,
        u.group_name, tu.realname, s.dynamic_value, s.transport, s.express_address, s.contact_name, s.platform,
        s.lock_no, ka.unit_name, s.addition_info, s.pay_status, s.merchant_code
        order by s.addtime desc
    </select>

    <select id="getProgram" resultType="Map">
        select dynamic_status as dynamicStatus
        from program
        where id = #{programId}
    </select>
</mapper>

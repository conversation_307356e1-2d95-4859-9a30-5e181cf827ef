<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llwh.dcenter.mapper.AuthorityStandSaleCheckGatherMapper">

    <select id="getStandUpdateDate" resultType="date">
        select distinct date(tickettime) as ticketdate
        from show_order
        where pay_status like 'paid%'
        and updatetime &gt;= #{startTime}
        and updatetime &lt; #{endTime}
        and tickettime is not NULL
        union
        select distinct date(ord.tickettime) as ticketdate
        from order_refund_detail ord
        left join order_refund o on ord.refund_serial_no = o.serial_no
        where o.order_type = 'show'
        and o.origin_status like 'paid%'
        and ord.updatetime &gt;= #{startTime}
        and ord.updatetime &lt; #{endTime}
        and ord.refund_status in ('ticket_success', 'refund_apply', 'refund_success', 'refund_audited', 'refund_running')
        and ord.tickettime is not NULL
    </select>


    <select id="getStandCount" resultType="com.llwh.dcenter.model.ReportStandSaleCheckGather">
        select t0.stadium_id        as stadiumId,
        t2.venue_id          as venueId,
        t1.user_group_id     as userGroupId,
        t1.program_id        as programId,
        t0.ticket_type_id    as ticketTypeId,
        t1.show_id           as showId,
        t0.company_id        as companyId,
        t8.city_code         as cityCode,
        t1.sell_type         as sellType,
        date(t1.tickettime)  as ticketDate,
        count(1)             as saleCount,
        sum(t0.ticket_price) as amount,
        sum(t0.discount)     as discount,
        sum(t0.paid_amount)  as paidAmount,
        sum(if(t0.gateway_code like 'free%',0,t0.paid_amount))  as settlementAmount,
        u.id as addUserId,
        u.username as userName,
        u.realname as realName,
        t0.gateway_code gatewayCode
        from show_order_detail t0
        left join show_order t1 on t0.trade_no = t1.trade_no
        left join program t2 on t2.id = t1.program_id
        left join stadium t3 on t3.id = t1.stadium_id
        left join venue t4 on t4.id = t2.venue_id
        left join open_show t5 on t5.id = t1.show_id
        left join ticket_type t6 on t6.id = t0.ticket_type_id
        left join user_group t7 on t7.id = t1.user_group_id
        left join city t8 on t8.city_code = t3.city_code
        left join tbs_user u on t1.add_user_id=u.id
        where t0.pay_status like 'paid%'
        and t1.tickettime &gt;= #{startTime}
        and t1.tickettime &lt; #{endTime}
        GROUP BY t0.stadium_id, t2.venue_id, t1.user_group_id, t1.program_id, t0.ticket_type_id,
        t1.show_id, date(t1.tickettime), t0.company_id,t8.city_code,t1.sell_type,u.id,u.username,u.realname,t0.gateway_code
    </select>

    <select id="getStandRefundCount" resultType="com.llwh.dcenter.model.ReportStandSaleCheckGather">
        select t1.stadium_id         as stadiumId,
        t4.venue_id           as venueId,
        t3.user_group_id      as userGroupId,
        t3.program_id         as programId,
        t2.ticket_type_id     as ticketTypeId,
        t3.show_id            as showId,
        date(t0.tickettime)   as ticketDate,
        t0.company_id         as companyId,
        t10.city_code         as cityCode,
        t3.sell_type         as sellType,
        -count(1)             as refundCount,
        -sum(t0.ticket_price) as refundAmount,
        -sum(t2.discount)     as refundDiscount,
        -sum(t0.real_pay)     as refundPaidAmount,
        -sum(if(t2.gateway_code like 'free%',0,t2.paid_amount))  as settlementAmount,
        u.id as addUserId,
        u.username as userName,
        u.realname as realName,
        t0.gateway_code gatewayCode
        from order_refund_detail t0
        left join order_refund t1 on t1.serial_no = t0.refund_serial_no
        left join show_order_detail t2 on t2.uuid = t0.uuid
        left join show_order t3 on t3.trade_no = t1.trade_no
        left join program t4 on t4.id = t3.program_id
        left join stadium t5 on t5.id = t1.stadium_id
        left join venue t6 on t6.id = t4.venue_id
        left join open_show t7 on t7.id = t3.show_id
        left join ticket_type t8 on t8.id = t2.ticket_type_id
        left join user_group t9 on t9.id = t3.user_group_id
        left join city t10 on t10.city_code = t5.city_code
        left join tbs_user u on t1.add_user_id=u.id
        where t1.order_type = 'show'
        and t1.origin_status like 'paid%'
        and t0.refund_status in ('ticket_success', 'refund_apply', 'refund_success', 'refund_audited', 'refund_running')
        and t0.tickettime &gt;= #{startTime}
        and t0.tickettime &lt; #{endTime}
        group by t1.stadium_id, t4.venue_id, t3.user_group_id, t3.program_id,t2.ticket_type_id, t3.show_id,
                 date(t0.tickettime), t0.company_id,t10.city_code,t3.sell_type,u.id,u.username,u.realname,t0.gateway_code
    </select>

    <select id="getTotalCount" resultType="com.llwh.dcenter.model.ReportStandSaleCheckGather">
        select stadium_name as stadiumName,
        venue_name as venueName,
        user_group_name as userGroupName,
        program_id as programId,
        program_name as programName,
        category as category,
        small_category as smallCategory,
        show_name as showName,
        play_time as playTime,
        program_code as programCode,
        city_name as cityName,
        ticket_type_name as ticketTypeName,
        ticket_price as ticketPrice,
        sum(sale_count) as saleCount,
        sum(amount) as amount,
        sum(discount) as discount,
        sum(paid_amount) as paid_amount,
        sum(refund_count) as refundCount,
        sum(refund_amount) as refundAmount,
        sum(refund_discount) as refundDiscount,
        sum(refund_paid_amount) as refundPaidAmount,
        sum(total_quantity) as totalQuantity,
        sum(total_amount) as totalAmount,
        sum(total_discount) as totalDiscount,
        sum(total_paid_amount) as totalPaidAmount
        from report_stand_sale_check_gather
        <where>
            and company_id = #{search.companyId}
            <if test="search.stadiumId != null">
                and stadium_id = #{search.stadiumId}
            </if>
            <if test="search.venueId != null">
                and venue_id = #{search.venueId}
            </if>
            <if test="search.programId != null">
                and program_id = #{search.programId}
            </if>
            <if test="search.showNameId != null">
                and show_id = #{search.showNameId}
            </if>
            <if test="search.ticketTypeId != null">
                and ticket_type_id = #{search.ticketTypeId}
            </if>
            <if test="search.userGroupId != null">
                and user_group_id = #{search.userGroupId}
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.userGroupIdList)">
                and user_group_id in
                <foreach collection="search.userGroupIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programCode)">
                and program_code like #{search.programCode}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.cityName)">
                and city_name like #{search.cityName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.venueName)">
                and venue_name like #{search.venueName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.stadiumName)">
                and stadium_name like #{search.stadiumName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programName)">
                and program_name like #{search.programName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.category)">
                and category like #{search.category}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.smallCategory)">
                and small_category like #{search.smallCategory}
            </if>
            <if test="search.datefrom != null">
                and ticket_date &gt;= #{search.datefrom}
            </if>
            <if test="search.dateto != null">
                and ticket_date &lt; #{search.dateto}
            </if>
            <if test="search.playStartTime != null">
                and play_time &gt;= #{search.playStartTime}
            </if>
            <if test="search.playEndTime != null">
                and play_time &lt; #{search.playEndTime}
            </if>
        </where>
        group by stadium_name,venue_name,user_group_name,program_id,program_name,show_name,play_time,ticket_type_name,ticket_price,program_code,
        city_name,category,small_category,gateway_code
        order by user_group_name,play_time,ticket_price desc,max(id)
    </select>

    <select id="getTicketPriceCount" resultType="java.lang.Double">
        select price as ticketPrice from ticket_type where id = #{ticketTypeId}
    </select>

    <select id="getCounts" resultType="com.llwh.dcenter.model.ReportStandSaleCheckGather">
        (select stadium_name as stadiumName,
               venue_name as venueName,
               user_group_name as userGroupName,
               program_name as programName,
               category as category,
               small_category as smallCategory,
               program_id as programId,
               show_name as showName,
               play_time as playTime,
               program_code as programCode,
               city_name as cityName,
               ticket_type_id as ticketTypeId,
               ticket_type_name as ticketTypeName,
               ticket_price as ticketPrice,
               sell_type as sellType,
               add_user_id as addUserId,
               gateway_code as gatewayCode,
               sum(sale_count) as saleCount,
               sum(amount) as amount,
               sum(discount) as discount,
               sum(paid_amount) as paid_amount,
               sum(refund_count) as refundCount,
               sum(refund_discount) as refundDiscount,
               sum(refund_amount) as refundAmount,
               sum(refund_paid_amount) as refundPaidAmount,
               sum(total_quantity) as totalQuantity,
               sum(total_amount) as totalAmount,
               sum(total_discount) as totalDiscount,
               sum(total_paid_amount) as totalPaidAmount,
               sum(settlement_amount) as settlementAmount
               from report_stand_sale_check_gather
               where
                company_id = #{search.companyId}
                   <if test="search.stadiumId != null">
                       and stadium_id = #{search.stadiumId}
                   </if>
                   <if test="search.venueId != null">
                       and venue_id = #{search.venueId}
                   </if>
               <if test="search.programId != null">
               and program_id = #{search.programId}
               </if>
               <if test="search.showNameId != null">
               and show_id = #{search.showNameId}
               </if>
               <if test="search.ticketTypeId != null">
               and ticket_type_id = #{search.ticketTypeId}
               </if>
               <if test="search.userGroupId != null">
                   and user_group_id = #{search.userGroupId}
               </if>
                   <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.userGroupIdList)">
                       and user_group_id in
                       <foreach collection="search.userGroupIdList" open="(" close=")" item="id" separator=",">
                           #{id}
                       </foreach>
                   </if>
               <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programCode)">
                   and program_code like #{search.programCode}
               </if>
               <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.cityName)">
                   and city_name like #{search.cityName}
               </if>
                   <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.venueName)">
                       and venue_name like #{search.venueName}
                   </if>
                   <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.stadiumName)">
                       and stadium_name like #{search.stadiumName}
                   </if>
                   <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programName)">
                       and program_name like #{search.programName}
                   </if>
                   <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.category)">
                       and category like #{search.category}
                   </if>
                   <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.smallCategory)">
                       and small_category like #{search.smallCategory}
                   </if>
                   <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.sellType)">
                       and sell_type = #{search.sellType}
                   </if>
                   <if test="search.datefrom != null">
                       and ticket_date &gt;= #{search.datefrom}
                   </if>
                   <if test="search.dateto != null">
                       and ticket_date &lt; #{search.dateto}
                   </if>
                   <if test="search.playStartTime != null">
                       and play_time &gt;= #{search.playStartTime}
                   </if>
                   <if test="search.playEndTime != null">
                       and play_time &lt; #{search.playEndTime}
                   </if>
                   <if test="search.addUserId != null">
                       and add_user_id = #{search.addUserId}
                   </if>
                   <if test="search.userGroupId != null">
                       and user_group_id = #{search.userGroupId}
                   </if>
        group by stadium_name,venue_name,user_group_name,program_name,program_id,show_name,play_time,ticket_type_id,ticket_type_name,ticket_price,program_code,
        city_name,category,small_category,sell_type,add_user_id,gateway_code
        order by user_group_name,play_time,ticket_price desc,max(id))
                   <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(programIds)">
                       union
                       (select stadium_name as stadiumName,
                       venue_name as venueName,
                       user_group_name as userGroupName,
                       program_name as programName,
                       category as category,
                       small_category as smallCategory,
                       program_id as programId,
                       show_name as showName,
                       play_time as playTime,
                       program_code as programCode,
                       city_name as cityName,
                       ticket_type_id as ticketTypeId,
                       ticket_type_name as ticketTypeName,
                       ticket_price as ticketPrice,
                       sell_type as sellType,
                       add_user_id as addUserId,
                       gateway_code as gatewayCode,
                       sum(sale_count) as saleCount,
                       sum(amount) as amount,
                       sum(discount) as discount,
                       sum(paid_amount) as paid_amount,
                       sum(refund_count) as refundCount,
                       sum(refund_discount) as refundDiscount,
                       sum(refund_amount) as refundAmount,
                       sum(refund_paid_amount) as refundPaidAmount,
                       sum(total_quantity) as totalQuantity,
                       sum(total_amount) as totalAmount,
                       sum(total_discount) as totalDiscount,
                       sum(total_paid_amount) as totalPaidAmount,
                       sum(settlement_amount) as settlementAmount
                       from report_stand_sale_check_gather
                       where company_id = #{search.companyId}
                       <if test="search.stadiumId != null">
                           and stadium_id = #{search.stadiumId}
                       </if>
                       <if test="search.venueId != null">
                           and venue_id = #{search.venueId}
                       </if>
                       <if test="search.programId != null">
                           and program_id = #{search.programId}
                       </if>
                       <if test="search.showNameId != null">
                           and show_id = #{search.showNameId}
                       </if>
                       <if test="search.ticketTypeId != null">
                           and ticket_type_id = #{search.ticketTypeId}
                       </if>
                       <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programCode)">
                           and program_code like #{search.programCode}
                       </if>
                       <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.cityName)">
                           and city_name like #{search.cityName}
                       </if>
                       <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.venueName)">
                           and venue_name like #{search.venueName}
                       </if>
                       <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.stadiumName)">
                           and stadium_name like #{search.stadiumName}
                       </if>
                       <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programName)">
                           and program_name like #{search.programName}
                       </if>
                       <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.category)">
                           and category like #{search.category}
                       </if>
                       <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.smallCategory)">
                           and small_category like #{search.smallCategory}
                       </if>
                       <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.sellType)">
                           and sell_type = #{search.sellType}
                       </if>
                       <if test="search.datefrom != null">
                           and ticket_date &gt;= #{search.datefrom}
                       </if>
                       <if test="search.dateto != null">
                           and ticket_date &lt; #{search.dateto}
                       </if>
                       <if test="search.playStartTime != null">
                           and play_time &gt;= #{search.playStartTime}
                       </if>
                       <if test="search.playEndTime != null">
                           and play_time &lt; #{search.playEndTime}
                       </if>
                       and program_id in
                       <foreach collection="programIds" open="(" close=")" item="id" separator=",">
                           #{id}
                       </foreach>
                       group by stadium_name,venue_name,user_group_name,program_name,program_id,show_name,play_time,ticket_type_id,ticket_type_name,ticket_price,program_code,
                       city_name,category,small_category,sell_type,add_user_id,gateway_code
                       order by user_group_name,play_time,ticket_price desc,max(id))
                   </if>
    </select>

    <select id="getTotals" resultType="com.llwh.dcenter.model.ReportStandSaleCheckGather">
        select
        sum(sale_count) as saleCount,
        sum(amount) as amount,
        sum(discount) as discount,
        sum(paid_amount) as paid_amount,
        sum(refund_count) as refundCount,
        sum(refund_amount) as refundAmount,
        sum(refund_discount) as refundDiscount,
        sum(refund_paid_amount) as refundPaidAmount,
        sum(total_quantity) as totalQuantity,
        sum(total_amount) as totalAmount,
        sum(total_discount) as totalDiscount,
        sum(total_paid_amount) as totalPaidAmount,
        sum(settlement_amount) as settlementAmount
        from report_stand_sale_check_gather
        <where>
            and company_id = #{search.companyId}
            <if test="search.stadiumId != null">
                and stadium_id = #{search.stadiumId}
            </if>
            <if test="search.venueId != null">
                and venue_id = #{search.venueId}
            </if>
            <if test="search.programId != null">
                and program_id = #{search.programId}
            </if>
            <if test="search.showNameId != null">
                and show_id = #{search.showNameId}
            </if>
            <if test="search.ticketTypeId != null">
                and ticket_type_id = #{search.ticketTypeId}
            </if>
            <if test="search.userGroupId != null">
                and user_group_id = #{search.userGroupId}
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.userGroupIdList)">
                and user_group_id in
                <foreach collection="search.userGroupIdList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programCode)">
                and program_code like #{search.programCode}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.cityName)">
                and city_name like #{search.cityName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.venueName)">
                and venue_name like #{search.venueName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.stadiumName)">
                and stadium_name like #{search.stadiumName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programName)">
                and program_name like #{search.programName}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.category)">
                and category like #{search.category}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.smallCategory)">
                and small_category like #{search.smallCategory}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.sellType)">
                and sell_type = #{search.sellType}
            </if>
            <if test="search.datefrom != null">
                and ticket_date &gt;= #{search.datefrom}
            </if>
            <if test="search.dateto != null">
                and ticket_date &lt; #{search.dateto}
            </if>
            <if test="search.playStartTime != null">
                and play_time &gt;= #{search.playStartTime}
            </if>
            <if test="search.playEndTime != null">
                and play_time &lt; #{search.playEndTime}
            </if>
        </where>
    </select>
</mapper>

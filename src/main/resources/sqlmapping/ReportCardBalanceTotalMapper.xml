<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.ReportCardBalanceTotalMapper">



    <select id="getCardTotals" resultType="com.llwh.dcenter.vo.report.ReportBalanceDetailTotalVo">
        select cardno,
        membership_type_id,
        membership_type_name,
        mobile,
        sum(if(tag = 'charge', amount,0)) as chargeTotal,
        sum(if(tag = 'spend', amount,0)) as spendTotal
        from report_balance_detail
        <where>
            <if test="search.tradetimeFrom != null">
                and tradetime &gt;= #{search.tradetimeFrom}
            </if>
            <if test="search.tradetimeTo != null">
                and tradetime &lt;= #{search.tradetimeTo}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.cardno)">
                and cardno = #{search.cardno}
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.membershipTypeIdList)">
                and membership_type_id in
            <foreach collection="search.membershipTypeIdList" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.mobile)">
                and mobile = #{search.mobile}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.tag)">
                and tag = #{search.tag}
            </if>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.typeList)">
                and type in
                <foreach collection="search.typeList" open="(" close=")" item="type" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.originTradeNo)">
                and origin_trade_no = #{search.originTradeNo}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.serialNo)">
                and serial_no = #{search.serialNo}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.description)">
                and description like #{search.description}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.remark)">
                and remark like #{search.remark}
            </if>

            and company_id = #{search.companyId}
        </where>
        group by cardno,membership_type_id,membership_type_name,mobile
    </select>

    <select id="getCardBalances" resultType="com.llwh.dcenter.vo.report.ReportBalanceDetailTotalVo">
        select cardno,
            balance
        from membership
        <where>
            <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(cardnos)">
                and cardno in
                <foreach collection="cardnos" open="(" close=")" item="cardno" separator=",">
                    #{cardno}
                </foreach>
            </if>
        </where>
    </select>

</mapper>

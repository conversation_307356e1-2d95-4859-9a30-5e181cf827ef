<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.ReportShowOverAllOperateMapper">
    <resultMap id="getTotalTicketCountMap" type="com.llwh.dcenter.vo.overalloperate.OverallCountScheduleVo">
        <result column="scheduleId" jdbcType="VARCHAR" property="scheduleId"/>
        <result column="programId" jdbcType="VARCHAR" property="programId"/>
        <result column="programName" jdbcType="VARCHAR" property="programName"/>
        <result column="playTime" jdbcType="VARCHAR" property="schedulePlayTime"/>
        <collection property="priceCountList" resultMap="overallCountMap" columnPrefix="over_"/>
    </resultMap>
    <resultMap id="overallCountMap" type="com.llwh.dcenter.vo.overalloperate.OverallPriceCountVo">
        <result column="ticketLevel" jdbcType="VARCHAR" property="ticketLevel"/>
        <result column="totalAmount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="totalNum" jdbcType="INTEGER" property="totalNum"/>
        <result column="reserveNum" jdbcType="INTEGER" property="reserveNum"/>
        <result column="reserveAmount" jdbcType="DECIMAL" property="reserveAmount"/>
        <result column="saleTotalNum" jdbcType="INTEGER" property="saleTotalNum"/>
        <result column="saleTotalAmount" jdbcType="DECIMAL" property="saleTotalAmount"/>
        <result column="uuid" jdbcType="VARCHAR" property="uuid"/>
    </resultMap>
    <select id="getTotalTicketCount" resultMap="getTotalTicketCountMap">
        SELECT
        CONCAT( tp.program_id, st.show_id, tp.id, tp.price ) over_uuid,
        st.show_id over_scheduleId,
        CONCAT( IFNULL(tp.cn_name,''), '(', tp.price, ')' ) over_ticketLevel,
        sum( st.maxnum ) AS over_totalNum,
        sum( tp.price * st.maxnum ) AS over_totalAmount,
        sum( st.reserve_num ) AS over_reserveNum,
        sum( tp.price * st.reserve_num ) AS over_reserveAmount,
        sum( st.sell_num ) AS over_saleTotalNum,
        sum( tp.price * st.sell_num ) AS over_saleTotalAmount,
        tp.program_id over_programId,
        tp.program_id programId,
        st.show_id scheduleId,
        p.cn_name AS programName,
        s.play_time AS playTime
        FROM
        show_ticket_type st
        INNER JOIN ticket_type tp ON st.ticket_type_id = tp.id
        INNER JOIN open_show s ON st.show_id = s.id
        INNER JOIN program p ON tp.program_id = p.id
        WHERE
        tp.company_id = #{search.companyId}
        and st.maxnum > 0
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.scheduleIdList)">
            and st.show_id in
            <foreach collection="search.scheduleIdList" item="scheduleId" open="(" separator="," close=")">
                #{scheduleId}
            </foreach>
        </if>
        GROUP BY
        tp.program_id,
        st.show_id,
        st.ticket_type_id,
        tp.price
        order by tp.price ,tp.addtime desc;
    </select>

    <select id="getSaleTotalTicketCount" resultType="com.llwh.dcenter.vo.overalloperate.OverallPriceCountVo">
        SELECT
        CONCAT(tp.program_id,s.show_id,tp.id,tp.price) uuid,
        IFNULL(SUM(s.ticket_price),0) saleAmount,
        IFNULL(sum(s.discount),0) saleDiscountAmount,
        sum(if(s.gateway_code like 'free%',0,s.paid_amount))  as settlementAmount,
        count(*) saleNum,
        CONCAT(IFNULL(tp.remark,''),'(',tp.price,')') ticketLevel
        FROM
        show_order_detail s
        left join order_refund_detail rd on s.uuid = rd.uuid
        INNER JOIN ticket_type tp ON s.ticket_type_id = tp.id
        WHERE
        s.company_id = #{search.companyId}
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.scheduleIdList)">
            and s.show_id in
            <foreach collection="search.scheduleIdList" item="scheduleId" open="(" separator="," close=")">
                #{scheduleId}
            </foreach>
        </if>
        and s.pay_status in('paid_uncheck','paid_failure','paid_certain','paid_success')
        and (rd.id is null or rd.refund_status not in ('ticket_success', 'refund_apply', 'refund_success'))
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.platform)">
            and s.platform = #{search.platform}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.sellType)">
            and s.sell_type = #{search.sellType}
        </if>
        <if test="search.addtimeFrom != null">
            and s.addtime &gt;= #{search.addtimeFrom}
        </if>
        <if test="search.addtimeTo != null">
            and s.addtime &lt;= #{search.addtimeTo}
        </if>
        <if test="search.paidtimeFrom != null">
            and s.paidtime &gt;= #{search.paidtimeFrom}
        </if>
        <if test="search.paidtimeTo != null">
            and s.paidtime &lt;= #{search.paidtimeTo}
        </if>
        GROUP BY tp.program_id,s.show_id,tp.id,tp.price;
    </select>

    <select id="getSellTypeTotalCount" resultType="com.llwh.dcenter.vo.overalloperate.SellTypeCountVo">
        SELECT
        CONCAT(tp.program_id,s.show_id,tp.id,tp.price) uuid,
        sell_type as sellType,
        count(*) as ticketNum,
        tp.price as price
        FROM
        show_order_detail s
        left join order_refund_detail rd on s.uuid = rd.uuid
        INNER JOIN ticket_type tp ON s.ticket_type_id = tp.id
        WHERE
        s.company_id = #{search.companyId}
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(search.scheduleIdList)">
            and s.show_id in
            <foreach collection="search.scheduleIdList" item="scheduleId" open="(" separator="," close=")">
                #{scheduleId}
            </foreach>
        </if>
        and s.pay_status in('paid_uncheck','paid_failure','paid_certain','paid_success')
        and (rd.id is null or rd.refund_status not in ('ticket_success', 'refund_apply', 'refund_success'))
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.platform)">
            and s.platform = #{search.platform}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.sellType)">
            and s.sell_type = #{search.sellType}
        </if>
        <if test="search.addtimeFrom != null">
            and s.addtime &gt;= #{search.addtimeFrom}
        </if>
        <if test="search.addtimeTo != null">
            and s.addtime &lt;= #{search.addtimeTo}
        </if>
        <if test="search.paidtimeFrom != null">
            and s.paidtime &gt;= #{search.paidtimeFrom}
        </if>
        <if test="search.paidtimeTo != null">
            and s.paidtime &lt;= #{search.paidtimeTo}
        </if>
        GROUP BY tp.program_id,s.show_id,tp.id,tp.price,s.sell_type;
    </select>

    <select id="getScheduleIdsBySaleType" resultType="Long">
        SELECT
        o.id
        FROM
        `open_show` o
        WHERE
        o.company_id = #{companyId}
        AND o.`status` = 'Y'
        <if test="@org.apache.commons.collections4.CollectionUtils@isNotEmpty(ids)">
            and o.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@equals(saleType, 'sale')">
            and o.external_time &lt;=#{date}
            and o.external_end_time &gt;=#{date}
            and o.program_id in(
            SELECT DISTINCT p.program_id from program_calendar p WHERE date( p.play_date )>= date(now())
            )
        </if>
    </select>

</mapper>

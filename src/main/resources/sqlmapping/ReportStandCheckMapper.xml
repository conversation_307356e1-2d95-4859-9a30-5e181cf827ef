<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.llwh.dcenter.mapper.ReportStandCheckMapper">

    <select id="getPageRecordCounts" resultType="com.llwh.dcenter.model.ReportStandCheck">
        select id,
               uuid,
               device_id,
               check_date,
               check_time,
               company_id,
               tbs_user_id,
               check_user_group_id
        from check_record
        where
              company_id=#{companyId}
          and reserve_type='show_check'
          and updatetime &gt;= #{startTime}
          and updatetime &lt; #{endTime}
          limit #{offset},#{pageSize}
    </select>

    <select id="getRecordByUuids" resultType="com.llwh.dcenter.model.ReportStandCheck">
        select id,
               uuid,
               device_id,
               check_date,
               check_time,
               company_id,
               tbs_user_id,
               check_user_group_id
        from check_record
        where
            company_id=#{companyId}
          and uuid in
        <foreach collection="uuids" item="uuid" open="(" close=")" separator=",">
            #{uuid}
        </foreach>
    </select>



    <select id="getShowInfos" resultType="com.llwh.dcenter.model.ReportStandCheck">
        SELECT
            sod.uuid,
            sod.trade_no tradeNo,
            sod.gateway_code as gatewayCode,
            so.merchant_code as merchantCode,
            p.cn_name programName,
            p.id programId,
            s.cn_name stadiumName,
            s.id stadiumId,
            v.cn_name venueName,
            v.id venueId,
            os.cn_name showName,
            os.id showId,
            os.play_time playTime,
            tt.cn_name ticketName,
            tt.id ticketTypeId,
            tt.price ticketPrice,
            saleg.group_name saleGroupName,
            saleg.id as saleUserGroupId
        FROM show_order_detail sod
            left join show_order so on so.trade_no = sod.trade_no
            left join program p on sod.program_id=p.id
            left join open_show os on sod.show_id=os.id
            left join stadium s on sod.stadium_id=s.id
            left join venue v on p.venue_id=v.id
            left join user_group g on sod.check_user_group_id = g.id
            left join tbs_user saleu on sale_user_id=saleu.id
            left join user_group saleg on  saleu.group_id=saleg.id
            left join ticket_type tt on sod.ticket_type_id=tt.id
        where sod.uuid in
        <foreach item="uuid" collection="list" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </select>



    <select id="getTotalCount" resultType="com.llwh.dcenter.model.ReportCheckRecord">
        select * from report_check_record
        where company_id = #{search.companyId}
        <if test="search.checkTimeFrom != null">
            and check_date &gt;= #{search.checkTimeFrom}
        </if>
        <if test="search.checkTimeTo != null">
            and check_date &lt; #{search.checkTimeTo}
        </if>
        <if test="search.playStartTime != null">
            and play_time &gt;= #{search.playStartTime}
        </if>
        <if test="search.playEndTime != null">
            and play_time &lt; #{search.playEndTime}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.venueName)">
            and venue_name like #{search.venueName}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.stadiumName)">
            and stadium_name like #{search.stadiumName}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programName)">
            and program_name like #{search.programName}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programCode)">
            and program_code like #{search.programCode}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.reserveType)">
            and reserve_type = #{search.reserveType}
        </if>
        <if test="search.programId != null">
            and program_id = #{search.programId}
        </if>
        <if test="search.stadiumId != null">
            and stadium_id = #{search.stadiumId}
        </if>
        <if test="search.venueId != null">
            and venue_id = #{search.venueId}
        </if>
        <if test="search.ticketPrice != null">
            and ticket_price = #{search.ticketPrice}
        </if>
        <if test="search.tbsUserId != null">
            and tbs_user_id = #{search.tbsUserId}
        </if>
        <if test="search.checkUserGroupId != null">
            and check_user_group_id = #{search.checkUserGroupId}
        </if>
        order by check_time,updatetime,id
    </select>

    <select id="getTotals" resultType="com.llwh.dcenter.vo.report.CheckRecordVo">
        select sum(check_count) as checkCount,
        sum(1) as count
        from report_check_record
        where company_id = #{search.companyId}
        <if test="search.checkTimeFrom != null">
            and check_time &gt;= #{search.checkTimeFrom}
        </if>
        <if test="search.checkTimeTo != null">
            and check_time &lt; #{search.checkTimeTo}
        </if>
        <if test="search.playStartTime != null">
            and play_time &gt;= #{search.playStartTime}
        </if>
        <if test="search.playEndTime != null">
            and play_time &lt; #{search.playEndTime}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.venueName)">
            and venue_name like #{search.venueName}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.stadiumName)">
            and stadium_name like #{search.stadiumName}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programName)">
            and program_name like #{search.programName}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.programCode)">
            and program_code like #{search.programCode}
        </if>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(search.reserveType)">
            and reserve_type = #{search.reserveType}
        </if>
        <if test="search.stadiumId != null">
            and stadium_id = #{search.stadiumId}
        </if>
        <if test="search.venueId != null">
            and venue_id = #{search.venueId}
        </if>
        <if test="search.programId != null">
            and program_id = #{search.programId}
        </if>
        <if test="search.ticketPrice != null">
            and ticket_price = #{search.ticketPrice}
        </if>
        <if test="search.tbsUserId != null">
            and tbs_user_id = #{search.tbsUserId}
        </if>
        <if test="search.checkUserGroupId != null">
            and check_user_group_id = #{search.checkUserGroupId}
        </if>
    </select>
</mapper>

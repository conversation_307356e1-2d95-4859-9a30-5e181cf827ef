<?xml version="1.0" encoding="UTF-8"?>
<configuration status="off" monitorInterval="1800">
	<Properties>
		<Property name="log.path" value="/opt/lamp/weblog/sadcenter/" />
		<Property name="log.pattern" value="[#%level[%date{yyyy-MM-dd HH:mm:ss}][%thread] %c{1}.%M(%line) | %msg#]%n" />
	</Properties>
	<appenders>
		<Console name="console" target="SYSTEM_OUT">
			<ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
			<PatternLayout pattern="${log.pattern}"/>
		</Console>
		<RollingFile name="WebLogger" fileName="${log.path}weblog.log" filePattern="${log.path}weblog.log.%d{yyyy-MM-dd}">
			<ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY" />
			<PatternLayout pattern="${log.pattern}" />
			<Policies>
				<TimeBasedTriggeringPolicy />
			</Policies>
		</RollingFile>
		<RollingFile name="hbaseLogger" fileName="${log.path}hbase2Log.log" filePattern="${log.path}hbase2Log.log.%d{yyyy-MM-dd}">
			<ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY" />
			<PatternLayout pattern="${log.pattern}" />
			<Policies>
				<TimeBasedTriggeringPolicy />
			</Policies>
		</RollingFile>
	</appenders>
	<loggers>
		<logger name="org.springframework.core" level="warn" />
		<logger name="org.springframework.beans" level="warn" />
		<logger name="org.springframework.context" level="info" />
		<logger name="org.hibernate" level="warn" />
		<logger name="org.hibernate.ps" level="warn" />
		<logger name="org.hibernate.SQL" level="warn" />
		<logger name="org.jboss.netty" level="warn" />
		<logger name="org.jboss.netty" level="warn" />
		<logger name="cn.fancylab" level="warn" />
		<logger name="cn.fantasyworks" level="warn" />
		<logger name="com.aliyun.oss" level="warn" />
		<logger name="com.ctrip.framework.apollo" level="info" />
		<logger name="org.apache.dubbo" level="warn" />
		<logger name="com.alibaba.druid" level="error" />
		<logger name="RocketmqClient" level="error" />
		<logger name="hbase2Log" level="info" additivity="false">
			<appender-ref ref="hbaseLogger" />
		</logger>
		<!-- Root Logger -->
		<root level="INFO" includeLocation="true">
			<appender-ref ref="WebLogger" />
		</root>
	</loggers>
</configuration>

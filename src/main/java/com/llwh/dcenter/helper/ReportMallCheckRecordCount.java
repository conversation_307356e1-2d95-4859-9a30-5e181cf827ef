package com.llwh.dcenter.helper;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonFormat;

import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @since 2024/4/8 19:58
 */
public class ReportMallCheckRecordCount implements Serializable {
	private static final long serialVersionUID = 3340041488829909529L;
	/**
	 * 订单类型，线上商品：mallcultural、积分商品：mallpoint
	 * 参考OrderType.TYPE_MALL_CULTURAL、OrderType.TYPE_MALL_POINT、OrderType.TYPE_MALL_RETAIL
	 */
	private String orderType;
	/**
	 * 验票时间开始
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Timestamp checkTimeFrom;
	/**
	 * 验票时间结束
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Timestamp checkTimeTo;
	/**
	 * 订单号
	 */
	private String tradeNo;
	/**
	 * 商品id
	 */
	private Long productId;
	/**
	 * 商品名称
	 */
	private String productName;
	/**
	 * 具体商品id，线上商品pms_cultural_creation_product的id、积分商品pms_point_product的id
	 */
	private Long detailProductId;
	/**
	 * 商品上架名称
	 */
	private String shelfName;
	/**
	 * 商品分类id
	 */
	private Long productCategoryId;
	/**
	 * 商品分类父id
	 */
	private Long productCategoryParentId;
	/**
	 * 发货方式
	 */
	private String deliveryChannel;
	/**
	 * 自提地址id
	 */
	private Long addressId;
	/**
	 * 省份
	 */
	private String provinceName;
	/**
	 * 地级市
	 */
	private String cityName;
	/**
	 * 区县
	 */
	private String countyName;
	/**
	 * 街道详细地址
	 */
	private String address;
	/**
	 * 核销用户ID
	 */
	private Long tbsUserId;
	/**
	 * 核销用户组ID
	 */
	private Long checkUserGroupId;
	/**
	 * 会员手机号
	 */
	private String mobile;

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public Timestamp getCheckTimeFrom() {
		return checkTimeFrom;
	}

	public void setCheckTimeFrom(Timestamp checkTimeFrom) {
		this.checkTimeFrom = checkTimeFrom;
	}

	public Timestamp getCheckTimeTo() {
		return checkTimeTo;
	}

	public void setCheckTimeTo(Timestamp checkTimeTo) {
		this.checkTimeTo = checkTimeTo;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Long getDetailProductId() {
		return detailProductId;
	}

	public void setDetailProductId(Long detailProductId) {
		this.detailProductId = detailProductId;
	}

	public String getShelfName() {
		return shelfName;
	}

	public void setShelfName(String shelfName) {
		this.shelfName = shelfName;
	}

	public Long getProductCategoryId() {
		return productCategoryId;
	}

	public void setProductCategoryId(Long productCategoryId) {
		this.productCategoryId = productCategoryId;
	}

	public Long getProductCategoryParentId() {
		return productCategoryParentId;
	}

	public void setProductCategoryParentId(Long productCategoryParentId) {
		this.productCategoryParentId = productCategoryParentId;
	}

	public String getDeliveryChannel() {
		return deliveryChannel;
	}

	public void setDeliveryChannel(String deliveryChannel) {
		this.deliveryChannel = deliveryChannel;
	}

	public Long getAddressId() {
		return addressId;
	}

	public void setAddressId(Long addressId) {
		this.addressId = addressId;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getCountyName() {
		return countyName;
	}

	public void setCountyName(String countyName) {
		this.countyName = countyName;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Long getTbsUserId() {
		return tbsUserId;
	}

	public void setTbsUserId(Long tbsUserId) {
		this.tbsUserId = tbsUserId;
	}

	public Long getCheckUserGroupId() {
		return checkUserGroupId;
	}

	public void setCheckUserGroupId(Long checkUserGroupId) {
		this.checkUserGroupId = checkUserGroupId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
}

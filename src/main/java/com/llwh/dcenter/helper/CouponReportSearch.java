package com.llwh.dcenter.helper;

import java.sql.Timestamp;

import cn.fancylab.sso.vo.DesensitizeDecodeVo;

public class CouponReportSearch extends DesensitizeDecodeVo {

	private static final long serialVersionUID = -833828324208661L;

	/**
	 * 注册手机号
	 */
	private String mobile;
	/**
	 * 卡号
	 */
	private String cardno;

	/**
	 * 注册手机号
	 */
	private String title;

	private Timestamp addtimeFrom;
	private Timestamp addtimeTo;

	private String status;

	private Timestamp locktimeFrom;
	private Timestamp locktimeTo;

	private String openid;

	private Long companyId;
	private Long checkUserId; // 核销业务用户ID
	private Long checkUserGroupId; // 核销用户组ID
	private Long checkMerchantId; // 核销商户用户ID
	private Long activityId; // 优惠券活动ID
	private Long memberId; // 会员ID
	/**
	 * 订单号
	 */
	private String lockno;
	/**
	 * 优惠券类型
	 */
	private String cardtype;

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getCardno() {
		return cardno;
	}

	public void setCardno(String cardno) {
		this.cardno = cardno;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Timestamp getAddtimeFrom() {
		return addtimeFrom;
	}

	public void setAddtimeFrom(Timestamp addtimeFrom) {
		this.addtimeFrom = addtimeFrom;
	}

	public Timestamp getAddtimeTo() {
		return addtimeTo;
	}

	public void setAddtimeTo(Timestamp addtimeTo) {
		this.addtimeTo = addtimeTo;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Timestamp getLocktimeFrom() {
		return locktimeFrom;
	}

	public void setLocktimeFrom(Timestamp locktimeFrom) {
		this.locktimeFrom = locktimeFrom;
	}

	public Timestamp getLocktimeTo() {
		return locktimeTo;
	}

	public void setLocktimeTo(Timestamp locktimeTo) {
		this.locktimeTo = locktimeTo;
	}

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getCheckUserId() {
		return checkUserId;
	}

	public void setCheckUserId(Long checkUserId) {
		this.checkUserId = checkUserId;
	}

	public Long getCheckUserGroupId() {
		return checkUserGroupId;
	}

	public void setCheckUserGroupId(Long checkUserGroupId) {
		this.checkUserGroupId = checkUserGroupId;
	}

	public Long getCheckMerchantId() {
		return checkMerchantId;
	}

	public void setCheckMerchantId(Long checkMerchantId) {
		this.checkMerchantId = checkMerchantId;
	}

	public Long getActivityId() {
		return activityId;
	}

	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}

	public String getLockno() {
		return lockno;
	}

	public void setLockno(String lockno) {
		this.lockno = lockno;
	}

	public String getCardtype() {
		return cardtype;
	}

	public void setCardtype(String cardtype) {
		this.cardtype = cardtype;
	}
}

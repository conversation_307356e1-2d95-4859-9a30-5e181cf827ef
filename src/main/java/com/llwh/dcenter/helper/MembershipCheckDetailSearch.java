package com.llwh.dcenter.helper;

import java.sql.Timestamp;
import java.util.List;

import cn.fancylab.sso.vo.DesensitizeDecodeVo;

public class MembershipCheckDetailSearch extends DesensitizeDecodeVo {
	private static final long serialVersionUID = 8295805699245606531L;
	private Timestamp datefrom;
	private Timestamp dateto;
	private String memberCardTypeId;
	private String mobile;

	/**
	 * 卡种名称
	 */
	private String membershipCardType;
	/**
	 * 卡号
	 */
	private String cardNo;
	/**
	 * 订单号
	 */
	private String tradeNo;
	private Long memberId;
	/**
	 * 核销用户、用户组
	 */
	private Long tbsUserId;
	private Long checkUserGroupId;

	/**
	 * 用户权限下卡种ID
	 */
	private List<String> membershipCardIdList;

	public List<String> getMembershipCardIdList() {
		return membershipCardIdList;
	}

	public void setMembershipCardIdList(List<String> membershipCardIdList) {
		this.membershipCardIdList = membershipCardIdList;
	}

	public Timestamp getDatefrom() {
		return datefrom;
	}

	public void setDatefrom(Timestamp datefrom) {
		this.datefrom = datefrom;
	}

	public Timestamp getDateto() {
		return dateto;
	}

	public void setDateto(Timestamp dateto) {
		this.dateto = dateto;
	}

	public String getMemberCardTypeId() {
		return memberCardTypeId;
	}

	public void setMemberCardTypeId(String memberCardTypeId) {
		this.memberCardTypeId = memberCardTypeId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getMembershipCardType() {
		return membershipCardType;
	}

	public void setMembershipCardType(String membershipCardType) {
		this.membershipCardType = membershipCardType;
	}

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}

	public Long getTbsUserId() {
		return tbsUserId;
	}

	public void setTbsUserId(Long tbsUserId) {
		this.tbsUserId = tbsUserId;
	}

	public Long getCheckUserGroupId() {
		return checkUserGroupId;
	}

	public void setCheckUserGroupId(Long checkUserGroupId) {
		this.checkUserGroupId = checkUserGroupId;
	}
}

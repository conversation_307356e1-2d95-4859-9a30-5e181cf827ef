package com.llwh.dcenter.helper.wlj;

import java.util.concurrent.ConcurrentHashMap;

public class WljApiHelperRegistry {

	private static final ConcurrentHashMap<String, AbstractWljApiHelper> registry = new ConcurrentHashMap<>();

	public static AbstractWljApiHelper get(String companyCode) {
		return registry.get(companyCode);
	}

	public static void put(String companyCode, AbstractWljApiHelper helper) {
		registry.put(companyCode, helper);
	}
}

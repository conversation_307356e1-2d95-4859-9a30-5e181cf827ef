package com.llwh.dcenter.helper.wlj;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import cn.fancylab.sso.SsoServiceClient;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.support.SysParam;
import cn.fancylab.util.BytesWrapper;
import cn.fancylab.util.JsonUtils;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.http.BodyRequest;
import cn.fancylab.util.http.HttpRequestBuilder;
import cn.fancylab.util.http.HttpResult;
import cn.fancylab.util.http.HttpUtils;
import cn.fancylab.util.http.UploadRequestBuilder;

import com.llwh.dcenter.vo.wlj.WljProgramVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

/*
 * 功能描述: <br>
 * @Author: zhangbiaoyan
 * @Date: 2023/4/12 16:05
 */
public abstract class AbstractWljApiHelper implements IWljApiHelper {
	protected static final Logger dbLogger = LoggerUtils.getLogger(AbstractWljApiHelper.class);

//	public abstract String getProgramUrl();
//
//	public abstract String getScheduleUrl();
//
//	public abstract String getTokenUrl();
//
//	public abstract String getShowCodeIdUrl();
//
//	public abstract String getAppId();
//
//	public abstract String getSecret();
//
//	public abstract String getCompanyCode();

	@Autowired
	private SsoServiceClient ssoServiceClient;
	public static final SysParam PROGRAM_URL_PREFIX = SysParam.get("programUrlPrefix", "项目预览").setModule("sadcenter-base");

	/**
	 * 上报项目信息
	 *
	 * @param program
	 * @return
	 */
	public ResultCode reportProgram(WljProgramVo program) {
		UploadRequestBuilder urb = new UploadRequestBuilder();
		// horizontalPoster > verticalPoster > extraPoster
		if (!StringUtils.isAllEmpty(program.getHorizontalPoster(), program.getVerticalPoster(), program.getExtraPoster())) {
			String img = StringUtils.isNotBlank(program.getHorizontalPoster()) ? program.getHorizontalPoster() : Objects.toString(program.getVerticalPoster(), program.getExtraPoster());
			String type = "horizontalPoster";
			if (StringUtils.isBlank(program.getHorizontalPoster())) {
				type = "verticalPoster";
			}
			if (StringUtils.isBlank(program.getVerticalPoster())) {
				type = "extraPoster";
			}
			BytesWrapper picResult = HttpUtils.getUrlAsBytes(img, new HttpRequestBuilder());
			String fileExt = picResult.resHeader.get("content-type").split("\\/")[1];
			urb.setInputName("files", type + "." + fileExt).setByteBody(picResult.getByteArray());
		}
		String showIdCode = program.getShowIdCode();
		if (StringUtils.isBlank(showIdCode)) {
			dbLogger.error("文旅局获取项目识别为空：program {}", JsonUtils.writeObjectToJson(program));
			return ResultCodeHelper.CODE11_DATA_NOT_EXISTS("文旅局获取项目识别为空");
		}
		Map param = new HashMap<>();
		param.put("showIdCode", showIdCode);
		Html2TextHelper html2TextHelper = Html2TextHelper.getInstance();
		String introduction = StringUtils.isBlank(program.getProgramInfo()) ? "" : html2TextHelper.getContent(program.getProgramInfo());
		param.put("introduction", introduction);
		String artist = StringUtils.isBlank(program.getCastList()) ? "" : html2TextHelper.getContent(program.getCastList());
		param.put("artist", artist);
		if (StringUtils.isNotBlank(program.getShowType())) {
			param.put("showType", program.getShowType());
		}
		ResultCode<List<Map>> response = ssoServiceClient.getListParam(program.getCompanyId(), PROGRAM_URL_PREFIX, "THEATRE");
		if (response.isSuccess()) {
			List<Map> data = response.getData();
			if (CollectionUtils.isNotEmpty(data)) {
				for (Map datum : data) {
					String supportSeat = MapUtils.getString(datum, "supportSeat");
					if ("app".equals(MapUtils.getString(datum, "terminalType")) && supportSeat.equals(program.getSupportSeat())) {
						String urlPrefix = MapUtils.getString(datum, "urlPrefix");
						param.put("jumpUrl", urlPrefix + "?programId=" + program.getId());
					}
				}
			}
		}
		urb.addParam("inParam", JsonUtils.writeMapToJson(param));
		dbLogger.warn("推送文旅局项目请求参数：{}|MultiPart={}", JsonUtils.writeObjectToJson(param), urb.getInputName());
		HttpResult httpResult = HttpUtils.uploadFile(getProgramUrl(), urb);
		dbLogger.warn("推送文旅局项目请求返回：{}", JsonUtils.writeObjectToJson(httpResult));
		return ResultCode.getSuccessReturn(httpResult.getResponse());
	}

	/**
	 * 上报场次销售信息
	 *
	 * @param param
	 * @return
	 */
	public ResultCode reportScheduleSaleInfo(Map param) {
		BodyRequest rb = new BodyRequest(JsonUtils.writeObjectToJson(param));
		rb.addHeader("content-type", "application/json;charset=UTF-8");
		rb.addHeader("version", "1.0.0");
		rb.addHeader("accept-language", "zh-CN");
		dbLogger.info("上报场次销售统计信息请求：{}", JsonUtils.writeObjectToJson(param, false));
		HttpResult httpResult = HttpUtils.postBodyAsString(getScheduleUrl(), rb);
		dbLogger.info("上报场次销售统计信息返回：{}", JsonUtils.writeObjectToJson(httpResult, false));
		return ResultCode.getSuccessReturn(httpResult.getResponse());
	}

	/**
	 * 获取token
	 *
	 * @return
	 */
	public String getToken() {
		Map param = new HashMap();
		param.put("appId", getAppId());
		param.put("secret", getSecret());
		HttpRequestBuilder rb = getHttpRequestBuilder(param);
		dbLogger.info("文旅局获取token请求：{}", JsonUtils.writeObjectToJson(param, false));
		HttpResult httpResult = HttpUtils.postUrlAsString(getTokenUrl(), rb);
		dbLogger.info("文旅局获取token返回：{}", JsonUtils.writeObjectToJson(httpResult, false));
		String response = httpResult.getResponse();
		Map map = JsonUtils.readJsonToMap(response);
		String code = MapUtils.getString(map, "code");
		if ("0".equals(code)) {
			Map dataMap = (Map) map.get("data");
			return MapUtils.getString(dataMap, "accessToken");
		}
		dbLogger.error("文旅局获取token失败:{}", response);
		return null;
	}

	/**
	 * 获取项目识别码
	 *
	 * @param approveNum
	 * @return
	 */
	public String getShowIdCode(String token, String approveNum) {
		Map param = new HashMap();
		param.put("appId", getAppId());
		param.put("accessToken", token);
		param.put("approvalNumber", approveNum);
		HttpRequestBuilder rb = getHttpRequestBuilder(param);
		dbLogger.info("文旅局获取ShowCodeId请求：{}", JsonUtils.writeObjectToJson(param, false));
		HttpResult httpResult = HttpUtils.postUrlAsString(getShowCodeIdUrl(), rb);
		dbLogger.info("文旅局获取ShowCodeId返回：{}", JsonUtils.writeObjectToJson(httpResult, false));
		String response = httpResult.getResponse();
		Map map = JsonUtils.readJsonToMap(response);
		String code = MapUtils.getString(map, "code");
		if ("0".equals(code)) {
			Map dataMap = (Map) map.get("data");
			return MapUtils.getString(dataMap, "showIdCode");
		}
		dbLogger.error("文旅局获取项目识别码失败:{}", response);
		return null;
	}

	private static HttpRequestBuilder getHttpRequestBuilder(Map param) {
		HttpRequestBuilder rb = new HttpRequestBuilder();
		rb.addParams(param);
		return rb;
	}

}

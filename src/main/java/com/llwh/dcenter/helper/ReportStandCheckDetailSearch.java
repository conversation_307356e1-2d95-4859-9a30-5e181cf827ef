package com.llwh.dcenter.helper;

import java.sql.Timestamp;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 门票销售明细表
 * <AUTHOR>
 */
public class ReportStandCheckDetailSearch extends DataLevelSearch {
	private static final long serialVersionUID = -8408114385405940540L;

	private Long companyId;
	/**
	 * 场次id拼接串，多个场次以英文逗号（,）拼接
 	 */
	private String showIds;
	private List<Long> showIdList;
	/**
	 * 出票、退票时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp ticketTimeFrom;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp ticketTimeTo;
	/**
	 * 付款、退款时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp paidtimeStart;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp paidtimeEnd;
	/**
	 * 订单号
	 */
	private String tradeNo;
	/**
	 * 来源
	 */
	private String source;
	/**
	 * 根据来源source获取填充的用户组、用户
	 */
	private List<Long> userGroupIdList4Source;
	private List<Long> addUserIdList4Source;
	/**
	 * 渠道
	 */
	private String platform;
	/**
	 * 销售用户组、用户
	 */
	private Long userGroupId;
	private Long saleUserId;
	/**
	 * 第三方（外部）订单号
	 */
	private String outTradeNo;
	/**
	 * 出票类型
	 */
	private String sellType;
	/**
	 * 是否已核销（Y/N）
	 */
	private String checked;
	/**
	 * 仅查询实际销售，Y
	 */
	private String onlySale;
	private String origin;
	/**
	 * 查询条件分销码备注对应的分销码拼接串，多个时以英文逗号拼接
	 */
	private String origins;
	private List<String> originList;
	/**
	 * 订单明细-票状态，N无效、Y有效、T退票、U已使用
	 */
	private String status;
	private List<String> statusList;
	private String orderStatus;
	private String payType;
	/**
	 * 核销时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp checkTimeFrom;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp checkTimeTo;
	/**
	 * 微信优惠券标识
	 */
	private String wxPromotion;
	/**
	 * 微信优惠券批次号
	 */
	private String promotionStockIds;

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getShowIds() {
		return showIds;
	}

	public void setShowIds(String showIds) {
		this.showIds = showIds;
	}

	public List<Long> getShowIdList() {
		return showIdList;
	}

	public void setShowIdList(List<Long> showIdList) {
		this.showIdList = showIdList;
	}

	public Timestamp getTicketTimeFrom() {
		return ticketTimeFrom;
	}

	public void setTicketTimeFrom(Timestamp ticketTimeFrom) {
		this.ticketTimeFrom = ticketTimeFrom;
	}

	public Timestamp getTicketTimeTo() {
		return ticketTimeTo;
	}

	public void setTicketTimeTo(Timestamp ticketTimeTo) {
		this.ticketTimeTo = ticketTimeTo;
	}

	public Timestamp getPaidtimeStart() {
		return paidtimeStart;
	}

	public void setPaidtimeStart(Timestamp paidtimeStart) {
		this.paidtimeStart = paidtimeStart;
	}

	public Timestamp getPaidtimeEnd() {
		return paidtimeEnd;
	}

	public void setPaidtimeEnd(Timestamp paidtimeEnd) {
		this.paidtimeEnd = paidtimeEnd;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public List<Long> getUserGroupIdList4Source() {
		return userGroupIdList4Source;
	}

	public void setUserGroupIdList4Source(List<Long> userGroupIdList4Source) {
		this.userGroupIdList4Source = userGroupIdList4Source;
	}

	public List<Long> getAddUserIdList4Source() {
		return addUserIdList4Source;
	}

	public void setAddUserIdList4Source(List<Long> addUserIdList4Source) {
		this.addUserIdList4Source = addUserIdList4Source;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public Long getSaleUserId() {
		return saleUserId;
	}

	public void setSaleUserId(Long saleUserId) {
		this.saleUserId = saleUserId;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	public String getSellType() {
		return sellType;
	}

	public void setSellType(String sellType) {
		this.sellType = sellType;
	}

	public String getChecked() {
		return checked;
	}

	public void setChecked(String checked) {
		this.checked = checked;
	}

	public String getOnlySale() {
		return onlySale;
	}

	public void setOnlySale(String onlySale) {
		this.onlySale = onlySale;
	}

	public String getOrigin() {
		return origin;
	}

	public void setOrigin(String origin) {
		this.origin = origin;
	}

	public String getOrigins() {
		return origins;
	}

	public void setOrigins(String origins) {
		this.origins = origins;
	}

	public List<String> getOriginList() {
		return originList;
	}

	public void setOriginList(List<String> originList) {
		this.originList = originList;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public List<String> getStatusList() {
		return statusList;
	}

	public void setStatusList(List<String> statusList) {
		this.statusList = statusList;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public Timestamp getCheckTimeFrom() {
		return checkTimeFrom;
	}

	public void setCheckTimeFrom(Timestamp checkTimeFrom) {
		this.checkTimeFrom = checkTimeFrom;
	}

	public Timestamp getCheckTimeTo() {
		return checkTimeTo;
	}

	public void setCheckTimeTo(Timestamp checkTimeTo) {
		this.checkTimeTo = checkTimeTo;
	}

	public String getWxPromotion() {
		return wxPromotion;
	}

	public void setWxPromotion(String wxPromotion) {
		this.wxPromotion = wxPromotion;
	}

	public String getPromotionStockIds() {
		return promotionStockIds;
	}

	public void setPromotionStockIds(String promotionStockIds) {
		this.promotionStockIds = promotionStockIds;
	}
}

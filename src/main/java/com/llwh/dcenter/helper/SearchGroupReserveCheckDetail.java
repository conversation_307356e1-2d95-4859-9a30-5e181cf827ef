package com.llwh.dcenter.helper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class SearchGroupReserveCheckDetail {
	private Date datefrom;
	private Date dateto;
	private Date reserveDateFrom;
	private Date reserveDateTo;
	private Date checkDateFrom;
	private Date checkDateTo;
	private Long programId;
	private Long showId;
	private String reserveNo;
	private Long distributorId;
	private String distributorName;
	private Long ticketTypeId;
	private Long userGroupId;
	private String status;
	private String statuses;
	private List<String> statusList;
	private String programIds;
	private List<Long> programIdList;
	private String showIds;
	private List<Long> showIdList;
	private String ticketTypeIds;
	private List<Long> ticketTypeList;

	/**
	 * 分销商类型
	 */
	private String distributorTypes;

	public String getTicketTypeIds() {
		return ticketTypeIds;
	}

	public void setTicketTypeIds(String ticketTypeIds) {
		this.ticketTypeIds = ticketTypeIds;
	}

	public List<Long> getTicketTypeList() {
		return ticketTypeList;
	}

	public void setTicketTypeList(List<Long> ticketTypeList) {
		this.ticketTypeList = ticketTypeList;
	}

	public String getDistributorName() {
		return distributorName;
	}

	public void setDistributorName(String distributorName) {
		this.distributorName = distributorName;
	}

	public Date getDatefrom() {
		return datefrom;
	}

	public void setDatefrom(Date datefrom) {
		this.datefrom = datefrom;
	}

	public Date getDateto() {
		return dateto;
	}

	public void setDateto(Date dateto) {
		this.dateto = dateto;
	}

	public Date getReserveDateFrom() {
		return reserveDateFrom;
	}

	public void setReserveDateFrom(Date reserveDateFrom) {
		this.reserveDateFrom = reserveDateFrom;
	}

	public Date getReserveDateTo() {
		return reserveDateTo;
	}

	public void setReserveDateTo(Date reserveDateTo) {
		this.reserveDateTo = reserveDateTo;
	}

	public Date getCheckDateFrom() {
		return checkDateFrom;
	}

	public void setCheckDateFrom(Date checkDateFrom) {
		this.checkDateFrom = checkDateFrom;
	}

	public Date getCheckDateTo() {
		return checkDateTo;
	}

	public void setCheckDateTo(Date checkDateTo) {
		this.checkDateTo = checkDateTo;
	}

	public Long getProgramId() {
		return programId;
	}

	public void setProgramId(Long programId) {
		this.programId = programId;
	}

	public Long getShowId() {
		return showId;
	}

	public void setShowId(Long showId) {
		this.showId = showId;
	}

	public String getReserveNo() {
		return reserveNo;
	}

	public void setReserveNo(String reserveNo) {
		this.reserveNo = reserveNo;
	}

	public Long getDistributorId() {
		return distributorId;
	}

	public void setDistributorId(Long distributorId) {
		this.distributorId = distributorId;
	}

	public Long getTicketTypeId() {
		return ticketTypeId;
	}

	public void setTicketTypeId(Long ticketTypeId) {
		this.ticketTypeId = ticketTypeId;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getStatuses() {
		return statuses;
	}


	public void setStatuses(String statuses) {
		this.statuses = statuses;
		if (StringUtils.isNotBlank(this.statuses)) {
			if (CollectionUtils.isEmpty(statusList)) {
				this.statusList = new ArrayList<>();
			}
			this.statusList.addAll(Arrays.asList(StringUtils.split(statuses, ",")));
		}
	}

	public String getShowIds() {
		return showIds;
	}

	public void setShowIds(String showIds) {
		this.showIds = showIds;
	}

	public List<Long> getShowIdList() {
		return showIdList;
	}

	public void setShowIdList(List<Long> showIdList) {
		this.showIdList = showIdList;
	}

	public String getProgramIds() {
		return programIds;
	}

	public void setProgramIds(String programIds) {
		this.programIds = programIds;
	}

	public List<String> getStatusList() {
		return statusList;
	}

	public void setStatusList(List<String> statusList) {
		this.statusList = statusList;
	}

	public List<Long> getProgramIdList() {
		return programIdList;
	}

	public void setProgramIdList(List<Long> programIdList) {
		this.programIdList = programIdList;
	}

	public String getDistributorTypes() {
		return distributorTypes;
	}

	public void setDistributorTypes(String distributorTypes) {
		this.distributorTypes = distributorTypes;
	}
}

package com.llwh.dcenter.helper;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 站票票种销售统计查询对象
 * <AUTHOR>
 * @since 2024/5/30 15:22
 */
public class StandTicketTypeCountSearch extends DataLevelSearch implements Serializable {
	private static final long serialVersionUID = -8789644982718973379L;
	/**
	 * 场次ID
	 */
	private String showIds;
	private List<Long> showIdList;
	/**
	 * 出票/退票时间
	 */
	private Timestamp ticketTimeFrom;
	private Timestamp ticketTimeTo;

	public String getShowIds() {
		return showIds;
	}

	public void setShowIds(String showIds) {
		this.showIds = showIds;
	}

	public List<Long> getShowIdList() {
		return showIdList;
	}

	public void setShowIdList(List<Long> showIdList) {
		this.showIdList = showIdList;
	}

	public Timestamp getTicketTimeFrom() {
		return ticketTimeFrom;
	}

	public void setTicketTimeFrom(Timestamp ticketTimeFrom) {
		this.ticketTimeFrom = ticketTimeFrom;
	}

	public Timestamp getTicketTimeTo() {
		return ticketTimeTo;
	}

	public void setTicketTimeTo(Timestamp ticketTimeTo) {
		this.ticketTimeTo = ticketTimeTo;
	}
}

package com.llwh.dcenter.helper;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
public class ReportMemberDashboardSearch {
	private Long companyId;
	private Timestamp addtimeFrom;
	private Timestamp addtimeTo;
	/**
	 * monthly、daily
	 */
	private String incrementSearchBy;
	private Timestamp paidtimeFrom;
	private Timestamp paidtimeTo;


	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Timestamp getAddtimeFrom() {
		return addtimeFrom;
	}

	public void setAddtimeFrom(Timestamp addtimeFrom) {
		this.addtimeFrom = addtimeFrom;
	}

	public Timestamp getAddtimeTo() {
		return addtimeTo;
	}

	public void setAddtimeTo(Timestamp addtimeTo) {
		this.addtimeTo = addtimeTo;
	}

	public String getIncrementSearchBy() {
		return incrementSearchBy;
	}

	public void setIncrementSearchBy(String incrementSearchBy) {
		this.incrementSearchBy = incrementSearchBy;
	}

	public Timestamp getPaidtimeFrom() {
		return paidtimeFrom;
	}

	public void setPaidtimeFrom(Timestamp paidtimeFrom) {
		this.paidtimeFrom = paidtimeFrom;
	}

	public Timestamp getPaidtimeTo() {
		return paidtimeTo;
	}

	public void setPaidtimeTo(Timestamp paidtimeTo) {
		this.paidtimeTo = paidtimeTo;
	}
}

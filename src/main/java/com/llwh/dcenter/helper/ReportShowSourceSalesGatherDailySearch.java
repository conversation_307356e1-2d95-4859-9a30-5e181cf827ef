package com.llwh.dcenter.helper;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 门票渠道销售日报表-明细-查询对象
 * <AUTHOR>
 */
public class ReportShowSourceSalesGatherDailySearch extends DataLevelSearch implements Serializable {
	private static final long serialVersionUID = 2877771722952274975L;
	/**
	 * 场次ID
	 */
	private String showIds;

	private List<Long> showIdList;

	private Timestamp ticketDateFrom;
	private Timestamp ticketDateTo;
	private String source; // 来源
	private String platform; // 渠道
	private Long userGroupId;
	/**
	 * 对应的查询条件
	 */
	private List<Long> addUserIdList4Source;
	private List<Long> userGroupIdList4Source;


	public String getShowIds() {
		return showIds;
	}

	public void setShowIds(String showIds) {
		this.showIds = showIds;
	}

	public List<Long> getShowIdList() {
		return showIdList;
	}

	public void setShowIdList(List<Long> showIdList) {
		this.showIdList = showIdList;
	}

	public Timestamp getTicketDateFrom() {
		return ticketDateFrom;
	}

	public void setTicketDateFrom(Timestamp ticketDateFrom) {
		this.ticketDateFrom = ticketDateFrom;
	}

	public Timestamp getTicketDateTo() {
		return ticketDateTo;
	}

	public void setTicketDateTo(Timestamp ticketDateTo) {
		this.ticketDateTo = ticketDateTo;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public List<Long> getAddUserIdList4Source() {
		return addUserIdList4Source;
	}

	public void setAddUserIdList4Source(List<Long> addUserIdList4Source) {
		this.addUserIdList4Source = addUserIdList4Source;
	}

	public List<Long> getUserGroupIdList4Source() {
		return userGroupIdList4Source;
	}

	public void setUserGroupIdList4Source(List<Long> userGroupIdList4Source) {
		this.userGroupIdList4Source = userGroupIdList4Source;
	}
}

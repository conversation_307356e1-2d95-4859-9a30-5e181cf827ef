package com.llwh.dcenter.helper;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

public class SeatStandProgramStatisticsSearch extends DataLevelSearch implements Serializable {

	private static final long serialVersionUID = -138986674341461L;
	/**
	 * 场次ID
	 */
	private String scheduleIds;

	private List<Long> scheduleIdList;

	private Timestamp ticketDateFrom;
	private Timestamp ticketDateTo;
	private Timestamp paidtimeStart;
	private Timestamp paidtimeEnd;
	/**
	 * 销售用户、用户组
	 */
	private Long saleUserId;
	private Long userGroupId;
	/**
	 * 来源
	 */
	private String source;
	/**
	 * 渠道
	 */
	private String platform;
	private List<Long> addUserId4Source;
	private List<Long> userGroupIdList4Source;
	/**
	 * 出票类型
	 */
	private String sellType;
	/**
	 * 支付方式名称
	 */
	private String paymethod;
	/**
	 * 支付方式
	 */
	private String gatewayCode;
	/**
	 * 订单状态（票类型）
	 */
	private String orderStatus;
	/**
	 * 支付类型（出票/退票）
	 */
	private String payType;
	/**
	 * 订单明细-票状态，N无效、Y有效、T退票、U已使用
	 */
	private String status;
	private List<String> statusList;
	/**
	 * 全部渠道/指定渠道
	 */
	private String type;
	private String excludeGroupIds;
	private List<Long> excludeGroupIdList;

	public String getScheduleIds() {
		return scheduleIds;
	}

	public void setScheduleIds(String scheduleIds) {
		this.scheduleIds = scheduleIds;
	}

	public List<Long> getScheduleIdList() {
		return scheduleIdList;
	}

	public void setScheduleIdList(List<Long> scheduleIdList) {
		this.scheduleIdList = scheduleIdList;
	}

	public Timestamp getTicketDateFrom() {
		return ticketDateFrom;
	}

	public void setTicketDateFrom(Timestamp ticketDateFrom) {
		this.ticketDateFrom = ticketDateFrom;
	}

	public Timestamp getTicketDateTo() {
		return ticketDateTo;
	}

	public void setTicketDateTo(Timestamp ticketDateTo) {
		this.ticketDateTo = ticketDateTo;
	}

	public Timestamp getPaidtimeStart() {
		return paidtimeStart;
	}

	public void setPaidtimeStart(Timestamp paidtimeStart) {
		this.paidtimeStart = paidtimeStart;
	}

	public Timestamp getPaidtimeEnd() {
		return paidtimeEnd;
	}

	public void setPaidtimeEnd(Timestamp paidtimeEnd) {
		this.paidtimeEnd = paidtimeEnd;
	}

	public Long getSaleUserId() {
		return saleUserId;
	}

	public void setSaleUserId(Long saleUserId) {
		this.saleUserId = saleUserId;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public List<Long> getAddUserId4Source() {
		return addUserId4Source;
	}

	public void setAddUserId4Source(List<Long> addUserId4Source) {
		this.addUserId4Source = addUserId4Source;
	}

	public List<Long> getUserGroupIdList4Source() {
		return userGroupIdList4Source;
	}

	public void setUserGroupIdList4Source(List<Long> userGroupIdList4Source) {
		this.userGroupIdList4Source = userGroupIdList4Source;
	}

	public String getSellType() {
		return sellType;
	}

	public void setSellType(String sellType) {
		this.sellType = sellType;
	}

	public String getPaymethod() {
		return paymethod;
	}

	public void setPaymethod(String paymethod) {
		this.paymethod = paymethod;
	}

	public String getGatewayCode() {
		return gatewayCode;
	}

	public void setGatewayCode(String gatewayCode) {
		this.gatewayCode = gatewayCode;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public List<String> getStatusList() {
		return statusList;
	}

	public void setStatusList(List<String> statusList) {
		this.statusList = statusList;
	}

	public List<Long> getExcludeGroupIdList() {
		return excludeGroupIdList;
	}

	public void setExcludeGroupIdList(List<Long> excludeGroupIdList) {
		this.excludeGroupIdList = excludeGroupIdList;
	}

	public String getExcludeGroupIds() {
		return excludeGroupIds;
	}

	public void setExcludeGroupIds(String excludeGroupIds) {
		this.excludeGroupIds = excludeGroupIds;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}

package com.llwh.dcenter.helper;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 用户销售统计日报查询对象
 * <AUTHOR>
 * @since 2024/6/1 18:03
 */
public class ReportUserSalesGatherDailySearch extends DataLevelSearch implements Serializable {
	private static final long serialVersionUID = 6904685070962839826L;

	/**
	 * 场次ID
	 */
	private String scheduleIds;

	private List<Long> scheduleIdList;

	private Timestamp ticketDateFrom;
	private Timestamp ticketDateTo;

	private Long userGroupId;
	private Long addUserId;

	public String getScheduleIds() {
		return scheduleIds;
	}

	public void setScheduleIds(String scheduleIds) {
		this.scheduleIds = scheduleIds;
	}

	public List<Long> getScheduleIdList() {
		return scheduleIdList;
	}

	public void setScheduleIdList(List<Long> scheduleIdList) {
		this.scheduleIdList = scheduleIdList;
	}

	public Timestamp getTicketDateFrom() {
		return ticketDateFrom;
	}

	public void setTicketDateFrom(Timestamp ticketDateFrom) {
		this.ticketDateFrom = ticketDateFrom;
	}

	public Timestamp getTicketDateTo() {
		return ticketDateTo;
	}

	public void setTicketDateTo(Timestamp ticketDateTo) {
		this.ticketDateTo = ticketDateTo;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public Long getAddUserId() {
		return addUserId;
	}

	public void setAddUserId(Long addUserId) {
		this.addUserId = addUserId;
	}
}

package com.llwh.dcenter.helper;

import java.sql.Timestamp;
import java.util.List;

public class SearReportMulticardDetail extends DataLevelSearch {
	private static final long serialVersionUID = -18451450813631092L;
	private Timestamp tickettimeFrom;
	private Timestamp tickettimeTo;
	/**
	 * 会员卡号
	 */
	private String membershipCardNo;
	/**
	 * 会员卡种ID
	 */
	private String membershipTypeIds;
	/**
	 * 会员卡种名称
	 */
	private List<String> membershipTypeIdList;
	/**
	 * 多次券号码
	 */
	private String couponCardNo;
	/**
	 * 手机号
	 */
	private String mobile;

	/**
	 * 订单号
	 */
	private String tradeNo;
	/**
	 * 项目ID
	 */
	private Long programId;

	/**
	 * 企业ID
	 */
	private Long companyId;

	public Timestamp getTickettimeFrom() {
		return tickettimeFrom;
	}

	public void setTickettimeFrom(Timestamp tickettimeFrom) {
		this.tickettimeFrom = tickettimeFrom;
	}

	public Timestamp getTickettimeTo() {
		return tickettimeTo;
	}

	public void setTickettimeTo(Timestamp tickettimeTo) {
		this.tickettimeTo = tickettimeTo;
	}

	public String getMembershipCardNo() {
		return membershipCardNo;
	}

	public void setMembershipCardNo(String membershipCardNo) {
		this.membershipCardNo = membershipCardNo;
	}

	public String getMembershipTypeIds() {
		return membershipTypeIds;
	}

	public void setMembershipTypeIds(String membershipTypeIds) {
		this.membershipTypeIds = membershipTypeIds;
	}

	public List<String> getMembershipTypeIdList() {
		return membershipTypeIdList;
	}

	public void setMembershipTypeIdList(List<String> membershipTypeIdList) {
		this.membershipTypeIdList = membershipTypeIdList;
	}

	public String getCouponCardNo() {
		return couponCardNo;
	}

	public void setCouponCardNo(String couponCardNo) {
		this.couponCardNo = couponCardNo;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public Long getProgramId() {
		return programId;
	}

	public void setProgramId(Long programId) {
		this.programId = programId;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}
}

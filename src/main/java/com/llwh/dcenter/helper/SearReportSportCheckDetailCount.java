package com.llwh.dcenter.helper;

import java.sql.Timestamp;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

public class SearReportSportCheckDetailCount {
	/**
	 * 来源
	 */
	private String platform;
	/**
	 * 下单时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp startTime;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp endTime;
	/**
	 * 运动场馆
	 */
	private Long stadiumId;
	/**
	 * 运动场地
	 */
	private Long courtId;
	private Long programId;
	/**
	 * 运动区域
	 */
	private Long pieceId;
	private String paymethod;
	private String tradeNo;
	private String payseqno;
	private String outTradeNo;
	private String mobile;
	private String contactMobile;
	/**
	 * 预定日期
	 */
	private Date playDateStart;
	private Date playDateEnd;
	/**
	 * 场次时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp playStartTime;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Timestamp playEndTime;
	/**
	 * 销售用户、用户组
	 */
	private Long saleUserId;
	private Long userGroupId;
	private String orderStatus;
	private Long companyId;

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public Timestamp getStartTime() {
		return startTime;
	}

	public void setStartTime(Timestamp startTime) {
		this.startTime = startTime;
	}

	public Timestamp getEndTime() {
		return endTime;
	}

	public void setEndTime(Timestamp endTime) {
		this.endTime = endTime;
	}

	public Long getStadiumId() {
		return stadiumId;
	}

	public void setStadiumId(Long stadiumId) {
		this.stadiumId = stadiumId;
	}

	public Long getCourtId() {
		return courtId;
	}

	public void setCourtId(Long courtId) {
		this.courtId = courtId;
	}

	public Long getProgramId() {
		return programId;
	}

	public void setProgramId(Long programId) {
		this.programId = programId;
	}

	public Long getPieceId() {
		return pieceId;
	}

	public void setPieceId(Long pieceId) {
		this.pieceId = pieceId;
	}

	public String getPaymethod() {
		return paymethod;
	}

	public void setPaymethod(String paymethod) {
		this.paymethod = paymethod;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getPayseqno() {
		return payseqno;
	}

	public void setPayseqno(String payseqno) {
		this.payseqno = payseqno;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	public String getContactMobile() {
		return contactMobile;
	}

	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}

	public Date getPlayDateStart() {
		return playDateStart;
	}

	public void setPlayDateStart(Date playDateStart) {
		this.playDateStart = playDateStart;
	}

	public Date getPlayDateEnd() {
		return playDateEnd;
	}

	public void setPlayDateEnd(Date playDateEnd) {
		this.playDateEnd = playDateEnd;
	}

	public Timestamp getPlayStartTime() {
		return playStartTime;
	}

	public void setPlayStartTime(Timestamp playStartTime) {
		this.playStartTime = playStartTime;
	}

	public Timestamp getPlayEndTime() {
		return playEndTime;
	}

	public void setPlayEndTime(Timestamp playEndTime) {
		this.playEndTime = playEndTime;
	}

	public Long getSaleUserId() {
		return saleUserId;
	}

	public void setSaleUserId(Long saleUserId) {
		this.saleUserId = saleUserId;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}
}

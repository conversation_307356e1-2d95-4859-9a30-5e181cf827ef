package com.llwh.dcenter.helper;


import java.sql.Timestamp;
import java.util.List;


public class MallOrderGatherByDetailSear {
	private Timestamp orderTimeFrom;
	private Timestamp orderTimeTo;
	private String detailProductIds;
	private List<Long> detailProductIdList;
	private String saleUserGroupIds;
	private List<Long> saleUserGroupIdList;
	private Long agencyId;
	private String agencyIds;
	private List<Long> agencyIdList;
	private Long companyId;
	private String orderType;
	private Long detailProductId;
	private Long detailSkuId;

	public Timestamp getOrderTimeFrom() {
		return orderTimeFrom;
	}

	public void setOrderTimeFrom(Timestamp orderTimeFrom) {
		this.orderTimeFrom = orderTimeFrom;
	}

	public Timestamp getOrderTimeTo() {
		return orderTimeTo;
	}

	public void setOrderTimeTo(Timestamp orderTimeTo) {
		this.orderTimeTo = orderTimeTo;
	}

	public String getDetailProductIds() {
		return detailProductIds;
	}

	public void setDetailProductIds(String detailProductIds) {
		this.detailProductIds = detailProductIds;
	}

	public List<Long> getDetailProductIdList() {
		return detailProductIdList;
	}

	public void setDetailProductIdList(List<Long> detailProductIdList) {
		this.detailProductIdList = detailProductIdList;
	}

	public String getSaleUserGroupIds() {
		return saleUserGroupIds;
	}

	public void setSaleUserGroupIds(String saleUserGroupIds) {
		this.saleUserGroupIds = saleUserGroupIds;
	}

	public List<Long> getSaleUserGroupIdList() {
		return saleUserGroupIdList;
	}

	public void setSaleUserGroupIdList(List<Long> saleUserGroupIdList) {
		this.saleUserGroupIdList = saleUserGroupIdList;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public Long getAgencyId() {
		return agencyId;
	}

	public void setAgencyId(Long agencyId) {
		this.agencyId = agencyId;
	}

	public String getAgencyIds() {
		return agencyIds;
	}

	public void setAgencyIds(String agencyIds) {
		this.agencyIds = agencyIds;
	}

	public List<Long> getAgencyIdList() {
		return agencyIdList;
	}

	public void setAgencyIdList(List<Long> agencyIdList) {
		this.agencyIdList = agencyIdList;
	}

	public Long getDetailProductId() {
		return detailProductId;
	}

	public void setDetailProductId(Long detailProductId) {
		this.detailProductId = detailProductId;
	}

	public Long getDetailSkuId() {
		return detailSkuId;
	}

	public void setDetailSkuId(Long detailSkuId) {
		this.detailSkuId = detailSkuId;
	}
}

package com.llwh.dcenter.untrans.wlj.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.fancylab.monitor.AlarmService;
import cn.fancylab.monitor.WarnMsg;
import cn.fancylab.service.DaoService;
import cn.fancylab.support.ResultCode;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;

import com.llwh.dcenter.helper.wlj.AbstractWljApiHelper;
import com.llwh.dcenter.model.ThirdMapping;
import com.llwh.dcenter.service.report.CompanyService;
import com.llwh.dcenter.service.wlj.WljSeatSaleGatherDayService;
import com.llwh.dcenter.untrans.report.BasePageUntransService;
import com.llwh.dcenter.untrans.wlj.WljApiUntransService;
import com.llwh.dcenter.untrans.wlj.WljShowIdCodeUntransService;
import com.llwh.dcenter.vo.common.CompanyVo;
import com.llwh.dcenter.vo.wlj.WljProgramSearch;
import com.llwh.dcenter.vo.wlj.WljProgramVo;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
 * 功能描述: 上报文旅局项目数据
 * @Author: zhangbiaoyan
 * @Date: 2023/4/12 12:50
 */
@Service
public class WljShowIdCodeUntransServiceImpl implements WljShowIdCodeUntransService, BasePageUntransService<WljProgramVo, WljProgramSearch> {
	@Autowired
	private WljSeatSaleGatherDayService wljSeatSaleGatherDayService;
	@Autowired
	private CompanyService companyService;
	@Autowired
	private AlarmService alarmService;
	@Autowired
	private DaoService daoService;
	@Autowired
	private WljApiUntransService wljApiUntransService;
	public static final String TYPE = "wlj_Program";

	@Override
	public List<WljProgramVo> selectList(Long companyId, WljProgramSearch search) {
		return wljSeatSaleGatherDayService.getPageByUpdatetime(companyId, (search.getPageNo() - 1) * search.getPageSize(), search.getPageSize(), search.getUpdatetime());
	}

	@Override
	public void processData(AbstractWljApiHelper helper, Long companyId, WljProgramSearch search, List<WljProgramVo> list) {
		List<String> programIds = new ArrayList<>();
		list.forEach(item -> {
			programIds.add(item.getId().toString());
		});
		DetachedCriteria query = DetachedCriteria.forClass(ThirdMapping.class);
		query.add(Restrictions.eq("type", TYPE));
		query.add(Restrictions.in("klsmId", programIds));
		query.add(Restrictions.eq("companyId", companyId));
		List<ThirdMapping> thirdMappings = daoService.findByCriteria(query);
		Map<String, ThirdMapping> thirdMappingMap = BeanUtil.beanListToMap(thirdMappings, "klsmId");
		for (WljProgramVo programVo : list) {
			ThirdMapping thirdMapping = thirdMappingMap.get(programVo.getId() + "");
			if (thirdMapping != null && StringUtils.isNotBlank(thirdMapping.getThirdId())
					&& StringUtils.equals(programVo.getApprovalNum(), thirdMapping.getApprovalNum())) {
				continue;
			}
			if (thirdMapping == null) {
				thirdMapping = new ThirdMapping(companyId);
				thirdMapping.setKlsmId(programVo.getId().toString());
				thirdMapping.setType(TYPE);
			}
			thirdMapping.setApprovalNum(programVo.getApprovalNum());
			String showCodeId = helper.getShowIdCode(wljApiUntransService.getToken(helper), programVo.getApprovalNum());
			if (StringUtils.isBlank(showCodeId)) {
				WarnMsg msg = new WarnMsg("文旅局批文号错误", "数据校验").setDetail("[" + programVo.getProgramName() + "]:" + programVo.getApprovalNum());
				alarmService.alarm(msg);
				continue;
			}
			thirdMapping.setThirdId(showCodeId);
			daoService.saveObject(thirdMapping);
		}
	}

	@Override
	public ResultCode updateShowIdCode(AbstractWljApiHelper helper, String companyCode) {
		CompanyVo companyVo = companyService.getCompanyByCode(companyCode);
		return updateShowIdCode(helper, companyVo.getId(), DateUtil.getCurrAddedDayTime(-1));
	}

	@Override
	public ResultCode updateShowIdCode(AbstractWljApiHelper helper, Long companyId, Timestamp startUpdatetime) {
		startUpdatetime = DateUtil.addMinute(startUpdatetime, 20);
		WljProgramSearch search = new WljProgramSearch();
		if (startUpdatetime == null) {
			search.setUpdatetime(DateUtil.getCurFullTimestamp());
		} else {
			search.setUpdatetime(startUpdatetime);
		}
		processWithPage(helper, companyId, search);
		return null;
	}
}

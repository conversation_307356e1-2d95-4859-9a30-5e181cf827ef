package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.ValueUtil;

import com.llwh.dcenter.helper.ReportBalanceCardInfoSearch;
import com.llwh.dcenter.service.report.ReportBalanceCardInfoService;
import com.llwh.dcenter.untrans.report.ReportBalanceCardInfoUntransService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ReportBalanceCardInfoUntransServiceImpl implements ReportBalanceCardInfoUntransService {
	@Autowired
	private ReportBalanceCardInfoService reportBalanceCardInfoService;

	@Override
	public ResultCode<Map> getCounts(AuthUser user, ReportBalanceCardInfoSearch search) {
		Timestamp updatetimeFrom = search.getUpdatetimeFrom();
		Timestamp updatetimeTo = search.getUpdatetimeTo();
		if (updatetimeFrom == null || updatetimeTo == null) {
			return ResultCodeHelper.CODE11_PARAMS_REQUIRED("请输入储值变更时间范围");
		}
		if (StringUtils.isNotBlank(search.getCardTypeName())) {
			search.setCardTypeName("%" + search.getCardTypeName() + "%");
		}
		Long companyId = user.getCompanyId();
		List<Map<String, Object>> counts = reportBalanceCardInfoService.getCounts(companyId, search);
//		double amount4chargeTotal = 0.0;
//		double amount4expendTotal = 0.0;
//		double balance4beginningTotal = 0.0;
//		double balance4endingTotal = 0.0;
		double balanceAndGiftBalanceTotal = 0.0;
		double amount4chargeTotal = 0.0;
		double giftAmount4chargeTotal = 0.0;
		double expendTotal = 0.0;
		if (CollectionUtils.isNotEmpty(counts)) {
			List<String> cardnoList = BeanUtil.getBeanPropertyList(counts, "cardno", true);
			List<Map<String, Object>> beginningBalanceList = reportBalanceCardInfoService.getBalanceOfTheTime(companyId, cardnoList, updatetimeFrom);
			List<Map<String, Object>> endingBalanceList = reportBalanceCardInfoService.getBalanceOfTheTime(companyId, cardnoList, updatetimeTo);
			List<Map<String, Object>> periodChargeList = reportBalanceCardInfoService.getPeriodChargeList(companyId, cardnoList, updatetimeFrom, updatetimeTo);
			List<Map<String, Object>> periodExpendList = reportBalanceCardInfoService.getPeriodExpendList(companyId, cardnoList, updatetimeFrom, updatetimeTo);
			Map<String, Map<String, Object>> beginningBalanceMap = BeanUtil.beanListToMap(beginningBalanceList, "cardno");
			Map<String, Map<String, Object>> endingBalanceMap = BeanUtil.beanListToMap(endingBalanceList, "cardno");
			Map<String, Map<String, Object>> periodChargeMap = BeanUtil.beanListToMap(periodChargeList, "cardno");
			Map<String, Map<String, Object>> periodExpendMap = BeanUtil.beanListToMap(periodExpendList, "cardno");
			for (Map<String, Object> count : counts) {
				String cardno = (String) count.get("cardno");

				Map<String, Object> beginningMap = (Map<String, Object>) MapUtils.getMap(beginningBalanceMap, cardno, new HashMap<>());
				double balance4beginning = MapUtils.getDoubleValue(beginningMap, "balance", 0.0);
				count.put("balance4beginning", balance4beginning);
				count.put("giftBalance4beginning", MapUtils.getDoubleValue(beginningMap, "giftBalance", 0.0));

				Map<String, Object> endingMap = (Map<String, Object>) MapUtils.getMap(endingBalanceMap, cardno, new HashMap<>());
				double balance4ending = MapUtils.getDoubleValue(endingMap, "balance", 0.0);
				count.put("balance4ending", balance4ending);
				count.put("giftBalance4ending", MapUtils.getDoubleValue(endingMap, "giftBalance", 0.0));

				Map<String, Object> chargeMap = (Map<String, Object>) MapUtils.getMap(periodChargeMap, cardno, new HashMap<>());
				double amount4charge = MapUtils.getDoubleValue(chargeMap, "amount", 0.0);
				double giftAmount4charge = MapUtils.getDoubleValue(chargeMap, "giftAmount", 0.0);
				count.put("amount4charge", amount4charge);
				count.put("giftAmount4charge", giftAmount4charge);

				Map<String, Object> expendMap = (Map<String, Object>) MapUtils.getMap(periodExpendMap, cardno, new HashMap<>());
				double amount4expend = MapUtils.getDoubleValue(expendMap, "amount", 0.0);
				double giftAmount4expend = MapUtils.getDoubleValue(expendMap, "giftAmount", 0.0);
				count.put("amount4expend", amount4expend);
				count.put("giftAmount4expend", giftAmount4expend);

//				amount4chargeTotal = ValueUtil.round(amount4chargeTotal + amount4charge);
//				amount4expendTotal = ValueUtil.round(amount4expendTotal + amount4expend);
//				balance4beginningTotal = ValueUtil.round(balance4beginningTotal + balance4beginning);
//				balance4endingTotal = ValueUtil.round(balance4endingTotal + balance4ending);
				double balance = MapUtils.getDoubleValue(count, "balance", 0.0);
				double giftBalance = MapUtils.getDoubleValue(count, "giftBalance", 0.0);
				balanceAndGiftBalanceTotal = ValueUtil.round(balanceAndGiftBalanceTotal + balance + giftBalance);
				amount4chargeTotal = ValueUtil.round(amount4chargeTotal + amount4charge);
				giftAmount4chargeTotal = ValueUtil.round(giftAmount4chargeTotal + giftAmount4charge);
				expendTotal = ValueUtil.round(expendTotal + amount4expend + giftAmount4expend);
			}
		}
		Map<String, Object> model = new HashMap(2);
		Map<String, Object> total = new HashMap<>(4);
//		total.put("amount4chargeTotal", amount4chargeTotal);
//		total.put("amount4expendTotal", amount4expendTotal);
//		total.put("balance4beginningTotal", balance4beginningTotal);
//		total.put("balance4endingTotal", balance4endingTotal);
		total.put("balanceAndGiftBalanceTotal", balanceAndGiftBalanceTotal);
		total.put("amount4chargeTotal", amount4chargeTotal);
		total.put("giftAmount4chargeTotal", giftAmount4chargeTotal);
		total.put("expendTotal", expendTotal);
		model.put("total", total);
		model.put("records", counts);
		return ResultCode.getSuccessReturn(model);
	}
}

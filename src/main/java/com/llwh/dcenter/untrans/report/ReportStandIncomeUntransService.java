package com.llwh.dcenter.untrans.report;

import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.llwh.dcenter.helper.SearReportStandIncome;
import com.llwh.dcenter.vo.report.ReportStandIncomeVo;


public interface ReportStandIncomeUntransService {


	ResultCode<List<ReportStandIncomeVo>> getCounts(AuthUser user, SearReportStandIncome search);

}

package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import cn.fancylab.support.AuthUser;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.vo.report.ReportMembershipCheckGather;

public interface ReportMembershipCheckGatherUntransService {

	IPage<ReportMembershipCheckGather> getCounts(Page<ReportMembershipCheckGather> page, Date datefrom,
	                                             Date dateto, String membershipCardTypeId, AuthUser user);
	IPage<ReportMembershipCheckGather> getMembershipCheckGatherByTime(Page<ReportMembershipCheckGather> page, Timestamp datefrom,
	                                                                  Timestamp dateto, String membershipCardTypeId, AuthUser user);
	List<List<Object>> getTotalCount(Date datefrom, Date dateto,
	                                 String membershipCardTypeId, AuthUser user,Page page);

}

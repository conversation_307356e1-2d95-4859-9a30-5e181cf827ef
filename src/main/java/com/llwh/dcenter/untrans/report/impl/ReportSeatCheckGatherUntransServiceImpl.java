package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.StringUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.mapper.common.CommonMapper;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportSeatCheckGather;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportSeatCheckGatherService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatCheckGatherUntransService;
import com.llwh.dcenter.vo.report.ReportMemberSaleDetailVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportSeatCheckGatherUntransServiceImpl implements ReportSeatCheckGatherUntransService {

	@Autowired
	private ReportSeatCheckGatherService seatCheckGatherService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	@Autowired
	private CommonUntransService commonUntransService;
	@Autowired
	private DataLevelUntransService dataLevelUntransService;
	private static final String UK = "ReportSeatCheckGather";
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());
	@Autowired
	private CommonMapper commonMapper;

	@Override
	public void updateSeatCheckGatherJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			startTime = DateUtil.addMinute(record.getLastUpdateTime(), ReportConstant.UPDATE_REPEAT_MINUTE);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = DateUtil.getCurTruncTimestamp();
		}
		try {
			updateSeatCheckGatherCount(startTime, endTime, null);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateSeatCheckGatherCount(Timestamp startTime, Timestamp endTime, String uuid) {
		List<ReportSeatCheckGather> seatCheckCounts = new ArrayList<>();
		if (StringUtils.isNotBlank(uuid)) {
			seatCheckCounts = getAndPackSeatCheckCountList(null, null, uuid);
		} else {
			seatCheckCounts = getAndPackSeatCheckCountListByTimeRange(startTime, endTime);
		}
		if (CollectionUtils.isEmpty(seatCheckCounts)) {
			return;
		}

//		List<Long> ticketPriceIds = BeanUtil.getBeanPropertyList(seatCheckCounts,"ticketPriceId",true);
//		if(CollectionUtils.isNotEmpty(ticketPriceIds)){
//			List<TicketTypeVo> ticketPriceVos = commonUntransService.getTicketPrices(ticketPriceIds);
//			for(TicketTypeVo vo : ticketPriceVos){
//				for(ReportSeatCheckGather name : seatCheckCounts){
//					if(name.getTicketPriceId().equals(vo.getId())){
//						name.setTicketPrice(vo.getTicketPrice());
//					}
//				}
//			}
//		}
		commonUntransService.combineCheckStandOrSeatNames(new ArrayList<>(seatCheckCounts), ReportConstant.CATEGORY_SEAT);
		seatCheckGatherService.saveOrUpdateBatch(seatCheckCounts);
		dbLogger.warn("刷新座票入场汇总表(按天)：{}", seatCheckCounts.size());
	}

	private List<ReportSeatCheckGather> getAndPackSeatCheckCountListByTimeRange(Timestamp startTime, Timestamp endTime) {
		List<Date> dateList = seatCheckGatherService.getCheckDate(startTime, endTime);
		if (CollectionUtils.isEmpty(dateList)) {
			return new ArrayList<>();
		}
//		List<ReportSeatCheckGather> seatSaleCounts = new ArrayList<>();
		List<ReportSeatCheckGather> seatCheckCounts = new ArrayList<>();
		for(Date date : dateList){
			Timestamp checkStartTime = DateUtil.getBeginTimestamp(date);
			Timestamp checkEndTime = DateUtil.addDay(checkStartTime,1);
			List<ReportSeatCheckGather> seatCheckCountList = getAndPackSeatCheckCountList(checkStartTime, checkEndTime, null);
			seatCheckCounts.addAll(seatCheckCountList);
			//逻辑保留，后续单独新增按场次统计未入场人数报表
//			seatSaleCounts = seatCheckGatherService.getSeatSaleCount(checkStartTime, checkEndTime);
//			seatSaleCounts.forEach(count ->{
//				count.setUpdatetime(DateUtil.getCurFullTimestamp());
//				count.setId(StringUtil.md5(count.getCheckDate() + "_" + count.getStadiumId() +
//						"_" + count.getVenueId() + "_" + count.getProgramId() + "_" + count.getShowId() + "_" +
//						count.getUserGroupId() + "_" + count.getCheckGroupId() +
//						"_" + count.getCompanyId(),35));
//			});
//			for(ReportSeatCheckGather check : seatCheckCounts){
//				for(ReportSeatCheckGather sale : seatSaleCounts){
//					if(check.getId().equals(sale.getId())){
//						sale.setCheckCount(check.getCheckCount());
//						sale.setUncheckCount(sale.getSaleCount() - check.getCheckCount());
//
//					}else{
//						sale.setCheckCount(0);
//						sale.setUncheckCount(sale.getSaleCount());
//					}
//					NumberFormat numberFormat = NumberFormat.getInstance();
//					numberFormat.setMaximumFractionDigits(2);
//					Integer total = sale.getSaleCount() == 0 ? 1 : sale.getSaleCount();
//					String result = numberFormat.format((float)sale.getCheckCount()/(float)total*100);
//					sale.setAdmissionRates(result+"%");
//				}
//			}
		}
		return seatCheckCounts;
	}

	private List<ReportSeatCheckGather> getAndPackSeatCheckCountList(Timestamp checkStartTime, Timestamp checkEndTime, String uuid) {
		List<ReportSeatCheckGather> seatCheckCounts = seatCheckGatherService.getSeatCheckCount(checkStartTime, checkEndTime, uuid);
		Timestamp curFullTimestamp = DateUtil.getCurFullTimestamp();
		seatCheckCounts.forEach(count ->{
			count.setUpdatetime(curFullTimestamp);
			count.setId(StringUtil.md5(count.getCheckDate() + "_" + count.getStadiumId() +
					"_" + count.getVenueId() + "_" + count.getProgramId() + "_" + count.getShowId() + "_" +
					count.getUserGroupId() + "_" + count.getCheckGroupId() + "_" + count.getTbsUserId() +
					"_" + count.getCompanyId() + "_" + count.getTicketPriceId() + "_" + count.getAddUserId(),35));
		});
		return seatCheckCounts;
	}

	@Override
	public ResultCode<Page<ReportSeatCheckGather>> getCounts(AuthUser user, Page<ReportSeatCheckGather> page, SearReportStandCheckDetailCount search) {
		if ((search.getCheckDateFrom() == null || search.getCheckDateTo() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不能为空");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, true, true);
		QueryWrapper<ReportSeatCheckGather> query = getQueryWrapper4ReportSeatCheckGather(user, search);
		Page<ReportSeatCheckGather> pageResult = seatCheckGatherService.page(page, query);
		setUserName(pageResult);
		return ResultCode.getSuccessReturn(pageResult);
	}

	private QueryWrapper<ReportSeatCheckGather> getQueryWrapper4ReportSeatCheckGather(AuthUser user, SearReportStandCheckDetailCount search) {
		QueryWrapper<ReportSeatCheckGather> query = new QueryWrapper<>();
		query.eq("company_id", user.getCompanyId());
		query.orderByDesc("check_date","id");
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			query.like("stadium_name","%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			query.like("venue_name","%" + search.getVenueName() + "%");
		}
		if (search.getStadiumId() != null) {
			query.eq("stadium_id", search.getStadiumId());
		}
		if (search.getVenueId() != null) {
			query.eq("venue_id", search.getVenueId());
		}
		if (search.getProgramId() != null) {
			query.eq("program_id", search.getProgramId());
		}
		if (search.getShowId() != null) {
			query.eq("show_id", search.getShowId());
		}
		if (search.getShowNameId() != null) {
			query.eq("show_id", search.getShowNameId());
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			query.like("program_code","%" + search.getProgramCode() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			query.like("program_name","%" + search.getProgramName() + "%");
		}
		if (ObjectUtils.isNotEmpty(search.getCheckDateFrom())) {
			query.ge("check_date", search.getCheckDateFrom());
		}
		if (ObjectUtils.isNotEmpty(search.getCheckDateTo())) {
			query.lt("check_date", DateUtil.addDay(search.getCheckDateTo(), 1));
		}
		if (ObjectUtils.isNotEmpty(search.getPlayStartTime())) {
			query.ge("play_time", search.getPlayStartTime());
		}
		if (ObjectUtils.isNotEmpty(search.getPlayEndTime())) {
			query.lt("play_time", search.getPlayEndTime());
		}
		if (search.getUserGroupId() != null) {
			query.eq("user_group_id", search.getUserGroupId());
		}
		if (search.getSaleUserId() != null) {
			query.eq("add_user_id", search.getSaleUserId());
		}
		if (search.getCheckUserGroupId() != null) {
			query.eq("check_group_id", search.getCheckUserGroupId());
		}
		if (search.getTbsUserId() != null) {
			query.eq("tbs_user_id", search.getTbsUserId());
		}
		boolean haveConsumer = CollectionUtils.isNotEmpty(search.getAuthorityStadiumIds()) || search.getAuthorityUserGroupId() != null;
		Consumer<QueryWrapper<ReportSeatCheckGather>> consumer = consumerWrapper -> {
			if (CollectionUtils.isNotEmpty(search.getAuthorityStadiumIds())) {
				consumerWrapper.in("stadium_id", search.getAuthorityStadiumIds());
			}
			if (search.getAuthorityUserGroupId() != null) {
				consumerWrapper.eq("user_group_id", search.getAuthorityUserGroupId());
			}
		};
		if (CollectionUtils.isNotEmpty(search.getAuthorityProgramIds())) {
			query.and(wr -> wr.in("program_id", search.getAuthorityProgramIds()).or(haveConsumer, consumer));
		} else {
			query.and(haveConsumer, consumer);
		}
		return query;
	}

	private void setUserName(Page<ReportSeatCheckGather> pageResult) {
		if(pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())){
			return;
		}
		List<ReportSeatCheckGather> records = pageResult.getRecords();
		Set<Long> userIdSet = new HashSet<>(records.size()*2);
		for (ReportSeatCheckGather record : records) {
			if(record.getTbsUserId() != null){
				userIdSet.add(record.getTbsUserId());
			}
			if(record.getAddUserId() != null) {
				userIdSet.add(record.getAddUserId());
			}
		}
		List<ReportMemberSaleDetailVo> userNamesVos = commonMapper.getUserNames(new ArrayList<>(userIdSet));
		Map<Long, ReportMemberSaleDetailVo> checkGroupMap = BeanUtil.beanListToMap(userNamesVos, "tbsUserId");
		for(ReportSeatCheckGather record : records){
			ReportMemberSaleDetailVo vo = checkGroupMap.get(record.getTbsUserId());
			if(vo != null){
				record.setCheckUserName(vo.getUserName());
			}
			ReportMemberSaleDetailVo vo2 = checkGroupMap.get(record.getAddUserId());
			if(vo2 != null){
				record.setAddUserName(vo2.getUserName());
			}
		}
	}

	@Override
	public List<List<Object>> getTotalCount(AuthUser user, SearReportStandCheckDetailCount search) {
		List<List<Object>> resultList = new ArrayList<>();
		if ((search.getCheckDateFrom() == null || search.getCheckDateTo() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return resultList;
		}
		search.setCompanyId(user.getCompanyId());
		search.setCheckDateTo(DateUtil.addDay(search.getCheckDateTo(), 1));
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		List<ReportSeatCheckGather> tempList = seatCheckGatherService.getTotalCount(search);
		for (ReportSeatCheckGather detail : tempList) {
			List<Object> objList = new ArrayList<>();
			objList.add(detail.getCheckDate());
			objList.add(detail.getStadiumName());
			objList.add(detail.getVenueName());
			objList.add(detail.getProgramId());
			objList.add(detail.getProgramCode());
			objList.add(detail.getProgramName());
			objList.add(detail.getShowName());
			objList.add(detail.getPlayTime());
			objList.add(detail.getTicketPrice());
			objList.add(detail.getCheckCount());
			objList.add(detail.getUserGroupName());
			objList.add(detail.getCheckGroupName());
			resultList.add(objList);
		}
		return resultList;
	}

	@Override
	public ResultCode<Map<String, ?>> getTotals(AuthUser user, SearReportStandCheckDetailCount search) {
		if ((search.getCheckDateFrom() == null || search.getCheckDateTo() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不能为空");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, true, true);
		QueryWrapper<ReportSeatCheckGather> query = getQueryWrapper4ReportSeatCheckGather(user, search);
		query.select("sum(check_count) as checkCountSum", "sum(ticket_count) as ticketCountSum");
		Map<String, Object> map = seatCheckGatherService.getMap(query);
		return ResultCode.getSuccessReturn(map);
	}
}

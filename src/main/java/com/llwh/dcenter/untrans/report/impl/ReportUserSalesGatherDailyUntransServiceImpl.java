package com.llwh.dcenter.untrans.report.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.support.Sort;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.ValueUtil;

import com.llwh.dcenter.helper.ReportUserSalesGatherDailySearch;
import com.llwh.dcenter.service.report.ReportUserSalesGatherDailyService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.untrans.report.ReportUserSalesGatherDailyUntransService;
import com.llwh.dcenter.vo.common.UserGroupVo;
import com.llwh.dcenter.vo.report.ReportMemberSaleDetailVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/6/1 18:13
 */
@Service
public class ReportUserSalesGatherDailyUntransServiceImpl implements ReportUserSalesGatherDailyUntransService {
	@Autowired
	private DataLevelUntransService dataLevelUntransService;
	@Autowired
	private ReportUserSalesGatherDailyService userSalesGatherDailyService;
	@Autowired
	private CommonUntransService commonUntransService;

	@Override
	public ResultCode<Map> getCounts(AuthUser user, ReportUserSalesGatherDailySearch search) {
		if(StringUtils.isBlank(search.getScheduleIds()) && search.getTicketDateFrom() == null && search.getTicketDateTo() == null){
			return ResultCodeHelper.CODE11_PARAMS_ERROR("请指定场次或者时间");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, true, true);
		if(StringUtils.isNotBlank(search.getScheduleIds())){
			String[] scheduleIds = StringUtils.split(search.getScheduleIds(), ",");
			List<Long> scheduleIdList = Arrays.stream(scheduleIds).map(s->Long.parseLong(s.trim())).collect(Collectors.toList());
			search.setScheduleIdList(scheduleIdList);
		}

		Map model = new HashMap<>(2);
		model.put("userGroupList", new ArrayList<>());
		Map totalMap = new HashMap();
		totalMap.put("quantity", 0);
		totalMap.put("amount", 0.0);
		totalMap.put("paidAmount", 0.0);
		totalMap.put("settlementAmount", 0.0);
		model.put("totalMap", totalMap);

		List<Map<String, Object>> counts = userSalesGatherDailyService.getCounts(user.getCompanyId(), search);
		if (CollectionUtils.isEmpty(counts)) {
			return ResultCode.getSuccessReturn(model);
		}

		Map<String, Object> total = userSalesGatherDailyService.getTotals(user.getCompanyId(), search);
		if (MapUtils.isNotEmpty(total)) {
			totalMap.put("quantity", total.get("quantity") != null ? total.get("quantity") : 0);
			totalMap.put("amount", total.get("amount") != null ? ValueUtil.roundDouble((Double) total.get("amount")) : 0.0);
			totalMap.put("paidAmount", total.get("paidAmount") != null ? ValueUtil.roundDouble((Double) total.get("paidAmount")) : 0.0);
			totalMap.put("settlementAmount", total.get("settlementAmount") != null ? ValueUtil.roundDouble((Double) total.get("settlementAmount")) : 0.0);
		}

		Map<Long, String> userGroupNameMap = getUserGroupMap(counts);
		Map<Long, String> userNameMap = getUserMap(counts);
		Map<Long, List<String>> userGroup2SellTypeListMap = getUserGroup2SellTypeListMap(counts);
		Map<String, String> sellTypeNameMap = commonUntransService.getAllSellType(user.getCompanyId());

		Map<String, List<Map<String, Object>>> ticketDate2sellTypeListMap = BeanUtil.groupBeanListByFun(counts,
				e -> e.get("userGroupId") + "#" + e.get("addUserId") + "#" + DateUtil.formatDate((Date) e.get("ticketDate")), "");
		List<Map<String, Object>> ticketDateList = new ArrayList<>(ticketDate2sellTypeListMap.size());
		for (Map.Entry<String, List<Map<String, Object>>> entry : ticketDate2sellTypeListMap.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> sellTypeMapList = entry.getValue();
			String[] split = key.split("#");
			Long userGroupId = Long.valueOf(split[0]);
			Long addUserId = Long.valueOf(split[1]);
			HashMap ticketDateMap = new HashMap<>();
			ticketDateMap.put("userGroupId", userGroupId);
			ticketDateMap.put("addUserId", addUserId);
			ticketDateMap.put("ticketDate", split[2]);

			Map<String, Map<String, Object>> sellTypeMap4ticketDate = BeanUtil.beanListToMap(sellTypeMapList, "sellType");
			List<String> userGroup2SellTypeList4user = userGroup2SellTypeListMap.get(userGroupId);
			LinkedHashMap<String, Integer> sellTypeMap = new LinkedHashMap<>(userGroup2SellTypeList4user.size() + 1);
			Integer sellTypeTotal = 0;
			Double totalAmount = 0.0;
			Double totalPaidAmount = 0.0;
			Double totalSettlementAmount = 0.0;
			for (String sellType : userGroup2SellTypeList4user) {
				String sellTypeName = MapUtils.getString(sellTypeNameMap, sellType, "");
				String sellTypeKey = sellType + "-" + sellTypeName;
				Map<String, Object> sellTypeMapSingle = sellTypeMap4ticketDate.get(sellType);
				if (MapUtils.isNotEmpty(sellTypeMapSingle)) {
					Integer quantity = MapUtils.getInteger(sellTypeMapSingle, "quantity", 0);
					sellTypeMap.put(sellTypeKey, quantity);
					sellTypeTotal += quantity;
					totalAmount = ValueUtil.roundDouble(totalAmount + MapUtils.getDouble(sellTypeMapSingle, "amount", 0.0));
					totalPaidAmount = ValueUtil.roundDouble(totalPaidAmount + MapUtils.getDouble(sellTypeMapSingle, "paidAmount", 0.0));
					totalSettlementAmount = ValueUtil.roundDouble(totalSettlementAmount + MapUtils.getDouble(sellTypeMapSingle, "settlementAmount", 0.0));
				} else {
					sellTypeMap.put(sellTypeKey, 0);
				}
			}
			sellTypeMap.put("总数", sellTypeTotal);

			ticketDateMap.put("sellTypeMap", sellTypeMap);
			ticketDateMap.put("totalAmount", totalAmount);
			ticketDateMap.put("totalPaidAmount", totalPaidAmount);
			ticketDateMap.put("totalSettlementAmount", totalSettlementAmount);
			ticketDateList.add(ticketDateMap);
		}

		Map<String, List<Map<String, Object>>> addUserId2ticketDateListMap = BeanUtil.groupBeanListByFun(ticketDateList,
				e -> e.get("userGroupId") + "#" + e.get("addUserId"), "");
		List<Map<String, Object>> userList = new ArrayList<>(addUserId2ticketDateListMap.size());
		for (Map.Entry<String, List<Map<String, Object>>> entry : addUserId2ticketDateListMap.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> value = entry.getValue();
			value.sort(Sort.asc("ticketDate"));

			String[] split = key.split("#");
			Long userGroupId = Long.valueOf(split[0]);
			Long addUserId = Long.valueOf(split[1]);

			HashMap userMap = new HashMap<>();
			userMap.put("userGroupId", userGroupId);
			userMap.put("addUserId", addUserId);
			userMap.put("addUserName", MapUtils.getString(userNameMap, addUserId, addUserId+""));
			userMap.put("ticketDateList", value);

			LinkedHashMap<String, Integer> sellTypeMap = new LinkedHashMap<>(userGroup2SellTypeListMap.get(userGroupId).size() + 1);
			Double totalAmount = 0.0;
			Double totalPaidAmount = 0.0;
			Double totalSettlementAmount = 0.0;
			for (Map<String, Object> map : value) {
				LinkedHashMap<String, Integer> sellTypeMap4ticketDate = (LinkedHashMap<String, Integer>) MapUtils.getMap(map, "sellTypeMap", new LinkedHashMap<>());
				for (Map.Entry<String, Integer> entry1 : sellTypeMap4ticketDate.entrySet()) {
					String key1 = entry1.getKey();
					Integer value1 = entry1.getValue();
					sellTypeMap.put(key1, value1 + MapUtils.getInteger(sellTypeMap, key1, 0));
				}
				totalAmount = ValueUtil.roundDouble(totalAmount + MapUtils.getDouble(map, "totalAmount", 0.0));
				totalPaidAmount = ValueUtil.roundDouble(totalPaidAmount + MapUtils.getDouble(map, "totalPaidAmount", 0.0));
				totalSettlementAmount = ValueUtil.roundDouble(totalSettlementAmount + MapUtils.getDouble(map, "totalSettlementAmount", 0.0));
			}
			Map<String, Object> totalRow4user = new HashMap<>();
			totalRow4user.put("ticketDate", "小计");
			totalRow4user.put("sellTypeMap", sellTypeMap);
			totalRow4user.put("totalAmount", totalAmount);
			totalRow4user.put("totalPaidAmount", totalPaidAmount);
			totalRow4user.put("totalSettlementAmount", totalSettlementAmount);

			userMap.put("total", totalRow4user);
			userList.add(userMap);
		}

		Map<String, List<Map<String, Object>>> userGroupId2userListMap = BeanUtil.groupBeanListByFun(userList, e -> e.get("userGroupId") + "", "");
		List<Map<String, Object>> userGroupList = new ArrayList<>(userGroupId2userListMap.size());
		for (Map.Entry<String, List<Map<String, Object>>> entry : userGroupId2userListMap.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> value = entry.getValue();

			Long userGroupId = Long.valueOf(key);
			HashMap userGroupMap = new HashMap<>();
			userGroupMap.put("userGroupId", userGroupId);
			userGroupMap.put("userGroupName", MapUtils.getString(userGroupNameMap, userGroupId, userGroupId+""));
			userGroupMap.put("userList", value);

			LinkedHashMap<String, Integer> sellTypeMap = new LinkedHashMap<>(userGroup2SellTypeListMap.get(userGroupId).size() + 1);
			Double totalAmount = 0.0;
			Double totalPaidAmount = 0.0;
			Double totalSettlementAmount = 0.0;
			for (Map<String, Object> userMap : value) {
				Map<String, Object> subtotalMap = (Map<String, Object>) userMap.get("total");
				LinkedHashMap<String, Integer> sellTypeMap4user = (LinkedHashMap<String, Integer>) MapUtils.getMap(subtotalMap, "sellTypeMap", new LinkedHashMap<>());
				for (Map.Entry<String, Integer> entry1 : sellTypeMap4user.entrySet()) {
					String key1 = entry1.getKey();
					Integer value1 = entry1.getValue();
					sellTypeMap.put(key1, value1 + MapUtils.getInteger(sellTypeMap, key1, 0));
				}
				totalAmount = ValueUtil.roundDouble(totalAmount + MapUtils.getDouble(subtotalMap, "totalAmount", 0.0));
				totalPaidAmount = ValueUtil.roundDouble(totalPaidAmount + MapUtils.getDouble(subtotalMap, "totalPaidAmount", 0.0));
				totalSettlementAmount = ValueUtil.roundDouble(totalSettlementAmount + MapUtils.getDouble(subtotalMap, "totalSettlementAmount", 0.0));
			}
			Map<String, Object> totalRow4userGroup = new HashMap<>();
			totalRow4userGroup.put("ticketDate", "合计");
			totalRow4userGroup.put("sellTypeMap", sellTypeMap);
			totalRow4userGroup.put("totalAmount", totalAmount);
			totalRow4userGroup.put("totalPaidAmount", totalPaidAmount);
			totalRow4userGroup.put("totalSettlementAmount", totalSettlementAmount);

			userGroupMap.put("total", totalRow4userGroup);
			userGroupList.add(userGroupMap);
		}
		model.put("userGroupList", userGroupList);
		return ResultCode.getSuccessReturn(model);
	}

	private Map<Long, List<String>> getUserGroup2SellTypeListMap(List<Map<String, Object>> counts) {
		Map<Long, List<Map<String, Object>>> userGroupListMap = BeanUtil.groupBeanList(counts, "userGroupId");
		Map<Long, List<String>> userGroupSellTypeListMap = new HashMap<>(userGroupListMap.size());
		for (Map.Entry<Long, List<Map<String, Object>>> entry : userGroupListMap.entrySet()) {
			Long userGroupId = entry.getKey();
			List<Map<String, Object>> mapList4part = entry.getValue();
			List<String> sellTypes = BeanUtil.getBeanPropertyList(mapList4part, "sellType", true);
			Collections.sort(sellTypes);
			userGroupSellTypeListMap.put(userGroupId, sellTypes);
		}
		return userGroupSellTypeListMap;
	}

	private Map<Long, String> getUserMap(List<Map<String, Object>> counts) {
		List<Long> addUserIdList = BeanUtil.getBeanPropertyList(counts, "addUserId", true);
		List<List<Long>> partition = BeanUtil.partition(addUserIdList, 500);
		List<ReportMemberSaleDetailVo> userList = new ArrayList<>(addUserIdList.size());
		for (List<Long> idsPart : partition) {
			userList.addAll(commonUntransService.getUserNames(idsPart));
		}
		return BeanUtil.beanListToMap(userList, "tbsUserId", "realName", true);
	}

	private Map<Long, String> getUserGroupMap(List<Map<String, Object>> counts) {
		List<Long> userGroupIdList = BeanUtil.getBeanPropertyList(counts, "userGroupId", true);
		List<List<Long>> partition = BeanUtil.partition(userGroupIdList, 500);
		List<UserGroupVo> userGroupVoList = new ArrayList<>(userGroupIdList.size());
		for (List<Long> idsPart : partition) {
			userGroupVoList.addAll(commonUntransService.getUserGroupByIds(idsPart));
		}
		return BeanUtil.beanListToMap(userGroupVoList, "id", "userGroupName", true);
	}
}

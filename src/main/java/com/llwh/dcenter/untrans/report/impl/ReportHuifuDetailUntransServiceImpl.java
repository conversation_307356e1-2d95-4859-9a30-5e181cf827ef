package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.ticket.Status;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.ValueUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.base.constant.DiscountCategory;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.SearHuifuDetail;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportHuifuDetail;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportHuifuDetailService;
import com.llwh.dcenter.untrans.report.ReportHuifuDetailUntransService;
import com.llwh.dcenter.vo.report.huifu.HuifuDiscountVo;
import com.llwh.dcenter.vo.report.huifu.HuifuOrderGroupVo;
import com.llwh.dcenter.vo.report.huifu.HuifuTotalVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportHuifuDetailUntransServiceImpl implements ReportHuifuDetailUntransService {

	@Autowired
	private ReportHuifuDetailService reportHuifuDetailService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	private static final String UK = "ReportHuifuDetail";
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());

	@Override
	public void updateHuifuDetailJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			startTime = DateUtil.addMinute(record.getLastUpdateTime(), ReportConstant.UPDATE_REPEAT_MINUTE);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = DateUtil.getCurTruncTimestamp();
		}
		try {
			updateHuifuDetailCount(startTime, endTime, null);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateHuifuDetailCount(Timestamp startTime, Timestamp endTime, String tradeNo) {
		List<ReportHuifuDetail> countList = new ArrayList<>();
		List<ReportHuifuDetail> huifuCount = reportHuifuDetailService.getHuifuCount(startTime, endTime, tradeNo);
		if (CollectionUtils.isNotEmpty(huifuCount)) {
			Map<String, ReportHuifuDetail> huifuDetailMap = BeanUtil.beanListToMap(huifuCount, "tradeNo");
			List<HuifuDiscountVo> huifuDiscountVos = reportHuifuDetailService.getHuifuDiscount(startTime, endTime, tradeNo);
			if (CollectionUtils.isNotEmpty(huifuDiscountVos)) {
				Map<String, List<HuifuDiscountVo>> discountMap = BeanUtil.groupBeanList(huifuDiscountVos, "tradeNo");
				for(Map.Entry<String, List<HuifuDiscountVo>> entry : discountMap.entrySet()){
					String disTradeNo = entry.getKey();
					List<HuifuDiscountVo> huifuDiscountVoList = entry.getValue();
					ReportHuifuDetail detail = huifuDetailMap.get(disTradeNo);
					if(detail == null){
						continue;
					}
					Double platformSubsidyAmount = 0D;
					for(HuifuDiscountVo huifuDiscountVo : huifuDiscountVoList){
						if(Status.isY(huifuDiscountVo.getPlatformSubsidy())){
							platformSubsidyAmount += huifuDiscountVo.getDiscountAmount();
						}
						if(DiscountCategory.isMemership(huifuDiscountVo.getCategory())){
							detail.setMembershipDiscount(ValueUtil.roundDouble(detail.getMembershipDiscount() + huifuDiscountVo.getDiscountAmount()));
						}
						if(DiscountCategory.isCoupon(huifuDiscountVo.getCategory())){
							detail.setCouponDiscount(ValueUtil.roundDouble(detail.getCouponDiscount() + huifuDiscountVo.getDiscountAmount()));
						}
						if(DiscountCategory.isPromotion(huifuDiscountVo.getCategory())){
							detail.setPromotionDiscount(ValueUtil.roundDouble(detail.getPromotionDiscount() + huifuDiscountVo.getDiscountAmount()));
						}
						if(DiscountCategory.isPoints(huifuDiscountVo.getCategory())){
							detail.setPointsDiscount(ValueUtil.roundDouble(detail.getPointsDiscount() + huifuDiscountVo.getDiscountAmount()));
						}
					}
					detail.setPlatformSubsidyAmount(ValueUtil.roundDouble(detail.getPlatformSubsidyAmount() + platformSubsidyAmount));
					detail.setOrderRevenue(ValueUtil.roundDouble(detail.getOrderRevenue() +  platformSubsidyAmount));
				}
			}
			countList.addAll(huifuCount);
		}


		List<ReportHuifuDetail> refundHuifuCount = reportHuifuDetailService.getRefundHuifuCount(startTime, endTime, tradeNo);
		if (CollectionUtils.isNotEmpty(refundHuifuCount)) {
			Map<String, ReportHuifuDetail> huifuRefundDetailMap = BeanUtil.beanListToMap(refundHuifuCount, "tradeNo");
			List<HuifuDiscountVo> huifuDiscountVos = reportHuifuDetailService.getHuifuRefundDiscount(startTime, endTime, tradeNo);
			if (CollectionUtils.isNotEmpty(huifuDiscountVos)) {
				Map<String, List<HuifuDiscountVo>> discountMap = BeanUtil.groupBeanList(huifuDiscountVos, "tradeNo");
				for(Map.Entry<String, List<HuifuDiscountVo>> entry : discountMap.entrySet()){
					String disTradeNo = entry.getKey();
					List<HuifuDiscountVo> huifuDiscountVoList = entry.getValue();
					ReportHuifuDetail detail = huifuRefundDetailMap.get(disTradeNo);
					if(detail == null){
						continue;
					}
					Double platformSubsidyAmount = 0D;
					for(HuifuDiscountVo huifuDiscountVo : huifuDiscountVoList){
						if(Status.isY(huifuDiscountVo.getPlatformSubsidy())){
							platformSubsidyAmount += huifuDiscountVo.getDiscountAmount();
						}
						if(DiscountCategory.isMemership(huifuDiscountVo.getCategory())){
							detail.setMembershipDiscount(ValueUtil.roundDouble(detail.getMembershipDiscount() + huifuDiscountVo.getDiscountAmount()));
						}
						if(DiscountCategory.isCoupon(huifuDiscountVo.getCategory())){
							detail.setCouponDiscount(ValueUtil.roundDouble(detail.getCouponDiscount() + huifuDiscountVo.getDiscountAmount()));
						}
						if(DiscountCategory.isPromotion(huifuDiscountVo.getCategory())){
							detail.setPromotionDiscount(ValueUtil.roundDouble(detail.getPromotionDiscount() + huifuDiscountVo.getDiscountAmount()));
						}
						if(DiscountCategory.isPoints(huifuDiscountVo.getCategory())){
							detail.setPointsDiscount(ValueUtil.roundDouble(detail.getPointsDiscount() + huifuDiscountVo.getDiscountAmount()));
						}
					}
					detail.setPlatformSubsidyAmount(ValueUtil.roundDouble(detail.getPlatformSubsidyAmount() + platformSubsidyAmount));
					detail.setOrderRevenue(ValueUtil.roundDouble(detail.getOrderRevenue() +  platformSubsidyAmount));
				}
			}
			countList.addAll(refundHuifuCount);
		}
		Timestamp now = DateUtil.getCurFullTimestamp();
		countList.forEach(item -> item.setUpdatetime(now));
		reportHuifuDetailService.saveOrUpdateBatch(countList);
		if (StringUtils.isBlank(tradeNo)) {
			dbLogger.warn("刷新惠付码销售明细表：{}~{} {}", startTime, endTime, countList.size());
		}
	}


	@Override
	public ResultCode<Map> getCounts(AuthUser user, SearHuifuDetail search, Page<ReportHuifuDetail> page) {
		if ((search.getOrdertimeFrom() == null || search.getOrdertimeTo() == null) &&
				(search.getPaidtimeFrom() == null || search.getPaidtimeTo() == null)) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		QueryWrapper<ReportHuifuDetail> wrapper = new QueryWrapper<>();
		wrapper.eq("company_id", user.getCompanyId());
		wrapper.orderByAsc("ordertime","updatetime","id");
		if (StringUtils.isNotBlank(search.getTradeNo())) {
			wrapper.eq("trade_no", search.getTradeNo());
		}
		if (ObjectUtils.isNotEmpty(search.getPaidtimeFrom())) {
			wrapper.ge("paidtime", search.getPaidtimeFrom());
		}
		if (ObjectUtils.isNotEmpty(search.getPaidtimeTo())) {
			wrapper.le("paidtime", search.getPaidtimeTo());
		}
		if (ObjectUtils.isNotEmpty(search.getOrdertimeFrom())) {
			wrapper.ge("ordertime", search.getOrdertimeFrom());
		}
		if (ObjectUtils.isNotEmpty(search.getOrdertimeTo())) {
			wrapper.le("ordertime", search.getOrdertimeTo());
		}
		if (ObjectUtils.isNotEmpty(search.getAgencyId())) {
			wrapper.eq("agency_id", search.getAgencyId());
		}
		if (StringUtils.isNotBlank(search.getAgencyName())) {
			search.setAgencyName("%" + search.getAgencyName() + "%");
			wrapper.like("agency_name", search.getAgencyName());
		}
		Page<ReportHuifuDetail> pageResult = reportHuifuDetailService.page(page, wrapper);

		HuifuTotalVo totalVo = reportHuifuDetailService.getDetailTotal(user.getCompanyId(), search);
		Map model = new HashMap<>(2);
		model.put("pageResult", pageResult);
		model.put("total", totalVo);
		return ResultCode.getSuccessReturn(model);
	}

	@Override
	public ResultCode<Map> getGroupHuifu(AuthUser user, SearHuifuDetail search) {
		if (search.getOrdertimeFrom() == null || search.getOrdertimeTo() == null) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("订单日期不存在");
		}
		if (StringUtils.isNotBlank(search.getAgencyName())) {
			search.setAgencyName("%" + search.getAgencyName() + "%");
		}
		List<ReportHuifuDetail> details = reportHuifuDetailService.getDetailForGroup(user.getCompanyId(), search);
		if(CollectionUtils.isEmpty(details)){
			return ResultCode.SUCCESS;
		}
		HuifuOrderGroupVo totalGroup = new HuifuOrderGroupVo();
		List<Long> agencyIds = BeanUtil.getBeanPropertyList(details, "agencyId", true);
		totalGroup.setAgencyCount(agencyIds.size());

		Map<String, List<ReportHuifuDetail>> groupMap = BeanUtil.groupBeanListByFun(details, count ->  {
			String orderDate = DateUtil.formatDate(count.getOrdertime());
			Long angecyId = count.getAgencyId();
			return angecyId + "_" + orderDate;
		}, "");

		List<HuifuOrderGroupVo> groupVos = new ArrayList<>(groupMap.size());

		for(Map.Entry<String, List<ReportHuifuDetail>> entry : groupMap.entrySet()){
			String[] keyArr = StringUtils.split(entry.getKey(), "_");
			Long agencyId = Long.parseLong(keyArr[0]);
			Date orderDate = DateUtil.parseDate(keyArr[1]);
			List<ReportHuifuDetail> detailList = entry.getValue();
			HuifuOrderGroupVo groupVo = new HuifuOrderGroupVo();
			groupVo.setAgencyId(agencyId);
			groupVo.setAgencyName(detailList.get(0).getAgencyName());
			groupVo.setOrderDate(orderDate);
			for(ReportHuifuDetail detail : detailList){
				if(StringUtils.equals(detail.getPayType(), "pay")){
					groupVo.setPaidCount(groupVo.getPaidCount() + 1);
					groupVo.setAmount(ValueUtil.roundDouble(groupVo.getAmount() + detail.getAmount()));
					groupVo.setPaidAmount(ValueUtil.roundDouble(groupVo.getPaidAmount() + detail.getPaidAmount()));
					groupVo.setDiscount(ValueUtil.roundDouble(groupVo.getDiscount() + detail.getDiscount()));
					groupVo.setMembershipDiscount(ValueUtil.roundDouble(groupVo.getMembershipDiscount() + detail.getMembershipDiscount()));
					groupVo.setCouponDiscount(ValueUtil.roundDouble(groupVo.getCouponDiscount() + detail.getCouponDiscount()));
					groupVo.setPromotionDiscount(ValueUtil.roundDouble(groupVo.getPromotionDiscount() + detail.getPromotionDiscount()));
					groupVo.setPointsDiscount(ValueUtil.roundDouble(groupVo.getPointsDiscount() + detail.getPointsDiscount()));
					groupVo.setPlatformSubsidyAmount(ValueUtil.roundDouble(groupVo.getPlatformSubsidyAmount() + detail.getPlatformSubsidyAmount()));
					groupVo.setOrderRevenue(ValueUtil.roundDouble(groupVo.getOrderRevenue() + detail.getOrderRevenue()));
				}else{
					groupVo.setRefundCount(groupVo.getRefundCount() - 1);
					groupVo.setAmountRefund(ValueUtil.roundDouble(groupVo.getAmountRefund() + detail.getAmount()));
					groupVo.setPaidAmountRefund(ValueUtil.roundDouble(groupVo.getPaidAmountRefund() + detail.getPaidAmount()));
					groupVo.setDiscountRefund(ValueUtil.roundDouble(groupVo.getDiscountRefund() + detail.getDiscount()));
					groupVo.setMembershipDiscountRefund(ValueUtil.roundDouble(groupVo.getMembershipDiscountRefund() + detail.getMembershipDiscount()));
					groupVo.setCouponDiscountRefund(ValueUtil.roundDouble(groupVo.getCouponDiscountRefund() + detail.getCouponDiscount()));
					groupVo.setPromotionDiscountRefund(ValueUtil.roundDouble(groupVo.getPromotionDiscountRefund() + detail.getPromotionDiscount()));
					groupVo.setPointsDiscountRefund(ValueUtil.roundDouble(groupVo.getPointsDiscountRefund() + detail.getPointsDiscount()));
					groupVo.setPlatformSubsidyAmountRefund(ValueUtil.roundDouble(groupVo.getPlatformSubsidyAmountRefund() + detail.getPlatformSubsidyAmount()));
					groupVo.setOrderRevenueRefund(ValueUtil.roundDouble(groupVo.getOrderRevenueRefund() + detail.getOrderRevenue()));
				}
				groupVo.setSumCount(groupVo.getPaidCount() + groupVo.getRefundCount());
				groupVo.setAmountSum(ValueUtil.roundDouble(groupVo.getAmountSum() + detail.getAmount()));
				groupVo.setPaidAmountSum(ValueUtil.roundDouble(groupVo.getPaidAmountSum() + detail.getPaidAmount()));
				groupVo.setDiscountSum(ValueUtil.roundDouble(groupVo.getDiscountSum() + detail.getDiscount()));
				groupVo.setMembershipDiscountSum(ValueUtil.roundDouble(groupVo.getMembershipDiscountSum() + detail.getMembershipDiscount()));
				groupVo.setCouponDiscountSum(ValueUtil.roundDouble(groupVo.getCouponDiscountSum() + detail.getCouponDiscount()));
				groupVo.setPromotionDiscountSum(ValueUtil.roundDouble(groupVo.getPromotionDiscountSum() + detail.getPromotionDiscount()));
				groupVo.setPointsDiscountSum(ValueUtil.roundDouble(groupVo.getPointsDiscountSum() + detail.getPointsDiscount()));
				groupVo.setPlatformSubsidyAmountSum(ValueUtil.roundDouble(groupVo.getPlatformSubsidyAmountSum() + detail.getPlatformSubsidyAmount()));
				groupVo.setOrderRevenueSum(ValueUtil.roundDouble(groupVo.getOrderRevenueSum() + detail.getOrderRevenue()));
			}
			groupVos.add(groupVo);

			totalGroup.setPaidCount(totalGroup.getPaidCount() + groupVo.getPaidCount());
			totalGroup.setAmount(ValueUtil.roundDouble(totalGroup.getAmount() + groupVo.getAmount()));
			totalGroup.setPaidAmount(ValueUtil.roundDouble(totalGroup.getPaidAmount() + groupVo.getPaidAmount()));
			totalGroup.setDiscount(ValueUtil.roundDouble(totalGroup.getDiscount() + groupVo.getDiscount()));
			totalGroup.setMembershipDiscount(ValueUtil.roundDouble(totalGroup.getMembershipDiscount() + groupVo.getMembershipDiscount()));
			totalGroup.setCouponDiscount(ValueUtil.roundDouble(totalGroup.getCouponDiscount() + groupVo.getCouponDiscount()));
			totalGroup.setPromotionDiscount(ValueUtil.roundDouble(totalGroup.getPromotionDiscount() + groupVo.getPromotionDiscount()));
			totalGroup.setPointsDiscount(ValueUtil.roundDouble(totalGroup.getPointsDiscount() + groupVo.getPointsDiscount()));
			totalGroup.setPlatformSubsidyAmount(ValueUtil.roundDouble(totalGroup.getPlatformSubsidyAmount() + groupVo.getPlatformSubsidyAmount()));
			totalGroup.setOrderRevenue(ValueUtil.roundDouble(totalGroup.getOrderRevenue() + groupVo.getOrderRevenue()));

			totalGroup.setRefundCount(totalGroup.getRefundCount() + groupVo.getRefundCount());
			totalGroup.setAmountRefund(ValueUtil.roundDouble(totalGroup.getAmountRefund() + groupVo.getAmountRefund()));
			totalGroup.setPaidAmountRefund(ValueUtil.roundDouble(totalGroup.getPaidAmountRefund() + groupVo.getPaidAmountRefund()));
			totalGroup.setDiscountRefund(ValueUtil.roundDouble(totalGroup.getDiscountRefund() + groupVo.getDiscountRefund()));
			totalGroup.setMembershipDiscountRefund(ValueUtil.roundDouble(totalGroup.getMembershipDiscountRefund() + groupVo.getMembershipDiscountRefund()));
			totalGroup.setCouponDiscountRefund(ValueUtil.roundDouble(totalGroup.getCouponDiscountRefund() + groupVo.getCouponDiscountRefund()));
			totalGroup.setPromotionDiscountRefund(ValueUtil.roundDouble(totalGroup.getPromotionDiscountRefund() + groupVo.getPromotionDiscountRefund()));
			totalGroup.setPointsDiscountRefund(ValueUtil.roundDouble(totalGroup.getPointsDiscountRefund() + groupVo.getPointsDiscountRefund()));
			totalGroup.setPlatformSubsidyAmountRefund(ValueUtil.roundDouble(totalGroup.getPlatformSubsidyAmountRefund() + groupVo.getPlatformSubsidyAmountRefund()));
			totalGroup.setOrderRevenueRefund(ValueUtil.roundDouble(totalGroup.getOrderRevenueRefund() + groupVo.getOrderRevenueRefund()));

			totalGroup.setSumCount(totalGroup.getSumCount() + groupVo.getSumCount());
			totalGroup.setAmountSum(ValueUtil.roundDouble(totalGroup.getAmountSum() + groupVo.getAmountSum()));
			totalGroup.setPaidAmountSum(ValueUtil.roundDouble(totalGroup.getPaidAmountSum() + groupVo.getPaidAmountSum()));
			totalGroup.setDiscountSum(ValueUtil.roundDouble(totalGroup.getDiscountSum() + groupVo.getDiscountSum()));
			totalGroup.setMembershipDiscountSum(ValueUtil.roundDouble(totalGroup.getMembershipDiscountSum() + groupVo.getMembershipDiscountSum()));
			totalGroup.setCouponDiscountSum(ValueUtil.roundDouble(totalGroup.getCouponDiscountSum() + groupVo.getCouponDiscountSum()));
			totalGroup.setPromotionDiscountSum(ValueUtil.roundDouble(totalGroup.getPromotionDiscountSum() + groupVo.getPromotionDiscountSum()));
			totalGroup.setPointsDiscountSum(ValueUtil.roundDouble(totalGroup.getPointsDiscountSum() + groupVo.getPointsDiscountSum()));
			totalGroup.setPlatformSubsidyAmountSum(ValueUtil.roundDouble(totalGroup.getPlatformSubsidyAmountSum() + groupVo.getPlatformSubsidyAmountSum()));
			totalGroup.setOrderRevenueSum(ValueUtil.roundDouble(totalGroup.getOrderRevenueSum() + groupVo.getOrderRevenueSum()));
		}

		Map<Long, List<HuifuOrderGroupVo>> angecyMap = BeanUtil.groupBeanList(groupVos, "agencyId");
		List<Map> agencyList = new ArrayList<>(angecyMap.size());
		for(Map.Entry<Long, List<HuifuOrderGroupVo>> entry : angecyMap.entrySet()){
			Long agencyId = entry.getKey();
			List<HuifuOrderGroupVo> agencys = entry.getValue();
			HuifuOrderGroupVo agencyTotal = new HuifuOrderGroupVo();
			for(HuifuOrderGroupVo vo : agencys){
				agencyTotal.setPaidCount(agencyTotal.getPaidCount() + vo.getPaidCount());
				agencyTotal.setAmount(ValueUtil.roundDouble(agencyTotal.getAmount() + vo.getAmount()));
				agencyTotal.setPaidAmount(ValueUtil.roundDouble(agencyTotal.getPaidAmount() + vo.getPaidAmount()));
				agencyTotal.setDiscount(ValueUtil.roundDouble(agencyTotal.getDiscount() + vo.getDiscount()));
				agencyTotal.setMembershipDiscount(ValueUtil.roundDouble(agencyTotal.getMembershipDiscount() + vo.getMembershipDiscount()));
				agencyTotal.setCouponDiscount(ValueUtil.roundDouble(agencyTotal.getCouponDiscount() + vo.getCouponDiscount()));
				agencyTotal.setPromotionDiscount(ValueUtil.roundDouble(agencyTotal.getPromotionDiscount() + vo.getPromotionDiscount()));
				agencyTotal.setPointsDiscount(ValueUtil.roundDouble(agencyTotal.getPointsDiscount() + vo.getPointsDiscount()));
				agencyTotal.setPlatformSubsidyAmount(ValueUtil.roundDouble(agencyTotal.getPlatformSubsidyAmount() + vo.getPlatformSubsidyAmount()));
				agencyTotal.setOrderRevenue(ValueUtil.roundDouble(agencyTotal.getOrderRevenue() + vo.getOrderRevenue()));

				agencyTotal.setRefundCount(agencyTotal.getRefundCount() + vo.getRefundCount());
				agencyTotal.setAmountRefund(ValueUtil.roundDouble(agencyTotal.getAmountRefund() + vo.getAmountRefund()));
				agencyTotal.setPaidAmountRefund(ValueUtil.roundDouble(agencyTotal.getPaidAmountRefund() + vo.getPaidAmountRefund()));
				agencyTotal.setDiscountRefund(ValueUtil.roundDouble(agencyTotal.getDiscountRefund() + vo.getDiscountRefund()));
				agencyTotal.setMembershipDiscountRefund(ValueUtil.roundDouble(agencyTotal.getMembershipDiscountRefund() + vo.getMembershipDiscountRefund()));
				agencyTotal.setCouponDiscountRefund(ValueUtil.roundDouble(agencyTotal.getCouponDiscountRefund() + vo.getCouponDiscountRefund()));
				agencyTotal.setPromotionDiscountRefund(ValueUtil.roundDouble(agencyTotal.getPromotionDiscountRefund() + vo.getPromotionDiscountRefund()));
				agencyTotal.setPointsDiscountRefund(ValueUtil.roundDouble(agencyTotal.getPointsDiscountRefund() + vo.getPointsDiscountRefund()));
				agencyTotal.setPlatformSubsidyAmountRefund(ValueUtil.roundDouble(agencyTotal.getPlatformSubsidyAmountRefund() + vo.getPlatformSubsidyAmountRefund()));
				agencyTotal.setOrderRevenueRefund(ValueUtil.roundDouble(agencyTotal.getOrderRevenueRefund() + vo.getOrderRevenueRefund()));

				agencyTotal.setSumCount(agencyTotal.getSumCount() + vo.getSumCount());
				agencyTotal.setAmountSum(ValueUtil.roundDouble(agencyTotal.getAmountSum() + vo.getAmountSum()));
				agencyTotal.setPaidAmountSum(ValueUtil.roundDouble(agencyTotal.getPaidAmountSum() + vo.getPaidAmountSum()));
				agencyTotal.setDiscountSum(ValueUtil.roundDouble(agencyTotal.getDiscountSum() + vo.getDiscountSum()));
				agencyTotal.setMembershipDiscountSum(ValueUtil.roundDouble(agencyTotal.getMembershipDiscountSum() + vo.getMembershipDiscountSum()));
				agencyTotal.setCouponDiscountSum(ValueUtil.roundDouble(agencyTotal.getCouponDiscountSum() + vo.getCouponDiscountSum()));
				agencyTotal.setPromotionDiscountSum(ValueUtil.roundDouble(agencyTotal.getPromotionDiscountSum() + vo.getPromotionDiscountSum()));
				agencyTotal.setPointsDiscountSum(ValueUtil.roundDouble(agencyTotal.getPointsDiscountSum() + vo.getPointsDiscountSum()));
				agencyTotal.setPlatformSubsidyAmountSum(ValueUtil.roundDouble(agencyTotal.getPlatformSubsidyAmountSum() + vo.getPlatformSubsidyAmountSum()));
				agencyTotal.setOrderRevenueSum(ValueUtil.roundDouble(agencyTotal.getOrderRevenueSum() + vo.getOrderRevenueSum()));
			}
			agencys.add(agencyTotal);
			Map agencyMap = new HashMap(3);
			agencyMap.put("agencyId", agencyId);
			agencyMap.put("agencyName", agencys.get(0).getAgencyName());
			agencyMap.put("agencys", agencys);
			agencyList.add(agencyMap);
		}
		Map model = new HashMap(2);
		model.put("total", totalGroup);
		model.put("agencyList", agencyList);

		return ResultCode.getSuccessReturn(model);
	}
}

package com.llwh.dcenter.untrans.report.impl;

import com.llwh.dcenter.service.report.ReportOrderDetailService;
import com.llwh.dcenter.untrans.report.ReportOrderDetailUntransService;
import com.llwh.dcenter.vo.common.MemberActionStatsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
 * 功能描述: <br>
 * @Author: zhangbiaoyan
 * @Date: 2023/3/6 15:28
 */
@Service
public class ReportOrderDetailUntransServiceImpl implements ReportOrderDetailUntransService {
    @Autowired
    private ReportOrderDetailService reportOrderDetailService;
    @Override
    public MemberActionStatsVo getShowMemberActionStatsByMemberId(Long companyId, Long memberId) {
        return reportOrderDetailService.getShowMemberActionStatsByMemberId(companyId,memberId);
    }

    @Override
    public MemberActionStatsVo getTicketMemberActionStatsByMemberId(Long companyId, Long memberId) {
        return reportOrderDetailService.getTicketMemberActionStatsByMemberId(companyId,memberId);
    }
}

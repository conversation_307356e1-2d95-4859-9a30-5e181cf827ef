package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.JsonUtils;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.StringUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.SearReportSeatSaleGatherVenueArea;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportSeatSaleGatherVenueArea;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportMemberInfoService;
import com.llwh.dcenter.service.report.ReportSeatSaleGatherVenueAreaService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherVenueAreaUntransService;
import com.llwh.dcenter.vo.common.PaymethodVo;
import com.llwh.dcenter.vo.common.ProgramVo;
import com.llwh.dcenter.vo.common.ScheduleVo;
import com.llwh.dcenter.vo.common.StadiumVo;
import com.llwh.dcenter.vo.common.TicketTypeVo;
import com.llwh.dcenter.vo.common.VenueAreaVo;
import com.llwh.dcenter.vo.common.VenueVo;
import com.llwh.dcenter.vo.report.ReportMemberLevelCardNoVo;
import com.llwh.dcenter.vo.report.ReportMemberSaleDetailVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportSeatSaleGatherVenueAreaUntransServiceImpl implements ReportSeatSaleGatherVenueAreaUntransService {

	@Autowired
	private ReportSeatSaleGatherVenueAreaService seatSaleGatherVenueAreaService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	private static final String UK = "ReportSeatSaleGatherVenueArea";
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());
	@Autowired
	private CommonUntransService commonUntransService;
	@Autowired
	private ReportMemberInfoService reportMemberInfoService;
	@Autowired
	private DataLevelUntransService dataLevelUntransService;

	@Override
	public void updateSeatSaleGatherVenueAreaJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			startTime = DateUtil.addMinute(record.getLastUpdateTime(), ReportConstant.UPDATE_REPEAT_MINUTE);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = DateUtil.getCurTruncTimestamp();
		}
		try {
			updateSeatSaleGatherVenueAreaCount(startTime, endTime);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateSeatSaleGatherVenueAreaCount(Timestamp startTime, Timestamp endTime) {
		List<ReportSeatSaleGatherVenueArea> totalList = new ArrayList<>();
		List<ReportSeatSaleGatherVenueArea> seatCount = seatSaleGatherVenueAreaService.getSeatCount(startTime, endTime);
		if (CollectionUtils.isNotEmpty(seatCount)) {
			totalList.addAll(seatCount);
		}
		List<ReportSeatSaleGatherVenueArea> refundSeatCount = seatSaleGatherVenueAreaService.getRefundSeatCount(startTime, endTime);
		if (CollectionUtils.isNotEmpty(refundSeatCount)) {
			totalList.addAll(refundSeatCount);
		}
		List<String> paymethods = BeanUtil.getBeanPropertyList(totalList, "paymethod", true);
		Map<String, PaymethodVo> paymethodVoMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(paymethods)) {
			List<PaymethodVo> paymethodVos = commonUntransService.getPayMethods(paymethods);
			paymethodVoMap = BeanUtil.beanListToMap(paymethodVos, "name");
		}
		List<Long> ticketPriceIds = BeanUtil.getBeanPropertyList(totalList, "ticketPriceId", true);
		Map<Long, TicketTypeVo> ticketPriceVosMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(ticketPriceIds)) {
			List<TicketTypeVo> ticketPriceVos = commonUntransService.getTicketPrices(ticketPriceIds);
			ticketPriceVosMap = BeanUtil.beanListToMap(ticketPriceVos, "id");
		}
		List<Long> venueAreaIds = BeanUtil.getBeanPropertyList(totalList, "venueAreaId", true);
		Map<Long, VenueAreaVo> venueAreaVosMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(venueAreaIds)) {
			List<VenueAreaVo> venueAreaVos = commonUntransService.getVenueArea(venueAreaIds);
			venueAreaVosMap = BeanUtil.beanListToMap(venueAreaVos, "venueAreaId");
		}
		List<Long> addUserIds = BeanUtil.getBeanPropertyList(totalList, "addUserId", true);
		Map<Long, ReportMemberSaleDetailVo> userNameVosMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(addUserIds)) {
			List<ReportMemberSaleDetailVo> userNamesVos = commonUntransService.getUserNames(addUserIds);
			userNameVosMap = BeanUtil.beanListToMap(userNamesVos, "tbsUserId");
		}
		List<Long> userGroupIds = BeanUtil.getBeanPropertyList(totalList, "userGroupId", true);
		Map<Long, ReportMemberSaleDetailVo> userGroupNamesVosMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(userGroupIds)) {
			List<ReportMemberSaleDetailVo> userGroupNamesVos = commonUntransService.getUserGroupNames(userGroupIds);
			userGroupNamesVosMap = BeanUtil.beanListToMap(userGroupNamesVos, "userGroupId");
		}
		List<Long> showIds = BeanUtil.getBeanPropertyList(totalList, "showId", true);
		Map<Long, ScheduleVo> scheduleVosMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(showIds)) {
			List<ScheduleVo> scheduleVos = commonUntransService.getSchedules(showIds);
			scheduleVosMap = BeanUtil.beanListToMap(scheduleVos, "id");
		}
		List<Long> venueIds = BeanUtil.getBeanPropertyList(totalList, "venueId", true);
		Map<Long, VenueVo> venueVosMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(showIds)) {
			List<VenueVo> venueVos = commonUntransService.getVenues(venueIds);
			venueVosMap = BeanUtil.beanListToMap(venueVos, "id");
		}
		List<Long> stadiumIds = BeanUtil.getBeanPropertyList(totalList, "stadiumId", true);
		Map<Long, StadiumVo> stadiumVosMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(stadiumIds)) {
			List<StadiumVo> stadiumVos = commonUntransService.getStadiums(stadiumIds);
			stadiumVosMap = BeanUtil.beanListToMap(stadiumVos, "id");
		}

		for (ReportSeatSaleGatherVenueArea count : totalList) {
			if (MapUtils.isNotEmpty(paymethodVoMap) && StringUtils.isNotBlank(count.getPaymethod())) {
				PaymethodVo paymethodVo = paymethodVoMap.get(count.getPaymethod());
				if (paymethodVo != null) {
					count.setPaymethod(paymethodVo.getCnName());
				}
			}
			if (MapUtils.isNotEmpty(ticketPriceVosMap) && count.getTicketPriceId() != null) {
				TicketTypeVo ticketTypeVo = ticketPriceVosMap.get(count.getTicketPriceId());
				if (ticketTypeVo != null) {
					count.setTicketPriceName(ticketTypeVo.getCnName());
				}
			}
			if (MapUtils.isNotEmpty(venueAreaVosMap) && count.getVenueAreaId() != null) {
				VenueAreaVo venueAreaVo = venueAreaVosMap.get(count.getVenueAreaId());
				if (venueAreaVo != null) {
					count.setVenueAreaName(venueAreaVo.getVenueAreaCnName());
				}
			}
			if (MapUtils.isNotEmpty(userNameVosMap) && count.getAddUserId() != null) {
				ReportMemberSaleDetailVo userNameVo = userNameVosMap.get(count.getAddUserId());
				if (userNameVo != null) {
					count.setUserName(userNameVo.getUserName());
				}
			}
			if (MapUtils.isNotEmpty(userGroupNamesVosMap) && count.getUserGroupId() != null) {
				ReportMemberSaleDetailVo userGroupNameVo = userGroupNamesVosMap.get(count.getUserGroupId());
				if (userGroupNameVo != null) {
					count.setUserGroupName(userGroupNameVo.getUserGroupName());
				}
			}
			if (MapUtils.isNotEmpty(scheduleVosMap) && count.getShowId() != null) {
				ScheduleVo scheduleVo = scheduleVosMap.get(count.getShowId());
				if (scheduleVo != null) {
					count.setShowName(scheduleVo.getShowName());
					count.setPlayTime(scheduleVo.getPlayTime());
				}
			}
			if (MapUtils.isNotEmpty(venueVosMap) && count.getVenueId() != null) {
				VenueVo venueVo = venueVosMap.get(count.getVenueId());
				if (venueVo != null) {
					count.setVenueName(venueVo.getVenueName());
				}
			}
			if (MapUtils.isNotEmpty(stadiumVosMap) && count.getStadiumId() != null) {
				StadiumVo stadiumVo = stadiumVosMap.get(count.getStadiumId());
				if (stadiumVo != null) {
					count.setStadiumName(stadiumVo.getStadiumName());
				}
			}
			if (StringUtils.isNotBlank(count.getAdditionInfo())) {
				java.util.Map<String, String> tempMap = JsonUtils.readJsonToMap(count.getAdditionInfo());
				if (StringUtils.isNotBlank(MapUtils.getString(tempMap, "layoutRemark"))) {
					count.setTicketMessage(MapUtils.getString(tempMap, "layoutRemark"));
				}
				if (StringUtils.isNotBlank(MapUtils.getString(tempMap, "relateInfo"))) {
					count.setOrderRelatedMessage(MapUtils.getString(tempMap, "relateInfo"));
				}
				if (StringUtils.isNotBlank(MapUtils.getString(tempMap, "remark"))) {
					count.setDescription(MapUtils.getString(tempMap, "remark"));
				}
			}
		}
		Timestamp now = DateUtil.getCurFullTimestamp();
		Map<String, List<ReportSeatSaleGatherVenueArea>> stringListMap = BeanUtil.groupBeanListByFun(totalList, count -> {
			Long venueAreaId = count.getVenueAreaId();
			Long companyId = count.getCompanyId();
			Long ticketPriceId = count.getTicketPriceId();
			String tradeNo = count.getTradeNo();
			return StringUtil.md5(tradeNo + "_" + venueAreaId + "_" + ticketPriceId + "_" + companyId, 35);
		}, "");
		List<ReportSeatSaleGatherVenueArea> resultList = new ArrayList<>();
		for (String id : stringListMap.keySet()) {
			List<ReportSeatSaleGatherVenueArea> countList = stringListMap.get(id);
			if (countList.size() == 1) {
				ReportSeatSaleGatherVenueArea count = countList.get(0);
				count.setId(id);
				Integer saleCount = count.getSaleCount();
				if (saleCount == null) {
					saleCount = 0;
					count.setSaleCount(saleCount);
				}
				Double amount = count.getAmount();
				if (amount == null) {
					amount = 0.0;
					count.setAmount(amount);
				}
				Double discount = count.getDiscount();
				if (discount == null) {
					discount = 0.0;
					count.setDiscount(discount);
				}
				Double paidAmount = count.getPaidAmount();
				if (paidAmount == null) {
					paidAmount = 0.0;
					count.setPaidAmount(paidAmount);
				}
				Integer refundCount = count.getRefundCount();
				if (refundCount == null) {
					refundCount = 0;
					count.setRefundCount(refundCount);
				}
				Double refundAmount = count.getRefundAmount();
				if (refundAmount == null) {
					refundAmount = 0.0;
					count.setRefundAmount(refundAmount);
				}
				Double refundDiscount = count.getRefundDiscount();
				if (refundDiscount == null) {
					refundDiscount = 0.0;
					count.setRefundDiscount(refundDiscount);
				}
				Double refundPaidAmount = count.getRefundPaidAmount();
				if (refundPaidAmount == null) {
					refundPaidAmount = 0.0;
					count.setRefundPaidAmount(refundPaidAmount);
				}
				if (StringUtils.isBlank(count.getRowNos())) {
					count.setRowNos("");
				}
				if (StringUtils.isBlank(count.getColNos())) {
					count.setColNos("");
				}
				if (StringUtils.isBlank(count.getRefundRowNos())) {
					count.setRefundRowNos("");
				}
				if (StringUtils.isBlank(count.getRefundColNos())) {
					count.setRefundColNos("");
				}

				count.setTotalQuantity(saleCount + refundCount);
				count.setTotalAmount(amount + refundAmount);
				count.setTotalDiscount(discount + refundDiscount);
				count.setTotalPaidAmount(paidAmount + refundPaidAmount);
				count.setUpdatetime(now);
				resultList.add(count);
			} else {
				Integer saleCount = 0;
				Double amount = 0.0;
				Double discount = 0.0;
				Double paidAmount = 0.0;

				Integer refundCount = 0;
				Double refundAmount = 0.0;
				Double refundDiscount = 0.0;
				Double refundPaidAmount = 0.0;

				Integer totalQuantity = 0;
				Double totalAmount = 0.0;
				Double totalDisCount = 0.0;
				Double totalPaidAmount = 0.0;
				Double settlementAmount = 0.0;
				String rowNos = "";
				String colNos = "";
				String refundRowNos = "";
				String refundColNos = "";
				ReportSeatSaleGatherVenueArea reportSeatSaleGatherVenueArea = countList.get(0);
				reportSeatSaleGatherVenueArea.setId(id);
				for (ReportSeatSaleGatherVenueArea temp : countList) {
					saleCount += temp.getSaleCount() == null ? 0 : temp.getSaleCount();
					amount += temp.getAmount() == null ? 0.0 : temp.getAmount();
					discount += temp.getDiscount() == null ? 0.0 : temp.getDiscount();
					paidAmount += temp.getPaidAmount() == null ? 0.0 : temp.getPaidAmount();

					refundCount += temp.getRefundCount() == null ? 0 : temp.getRefundCount();
					refundAmount += temp.getRefundAmount() == null ? 0.0 : temp.getRefundAmount();
					refundDiscount += temp.getRefundDiscount() == null ? 0.0 : temp.getRefundDiscount();
					refundPaidAmount += temp.getRefundPaidAmount() == null ? 0.0 : temp.getRefundPaidAmount();

					settlementAmount += temp.getSettlementAmount() == null ? 0.0 : temp.getSettlementAmount();
					totalQuantity = saleCount + refundCount;
					totalAmount = amount + refundAmount;
					totalDisCount = discount + refundDiscount;
					totalPaidAmount = paidAmount + refundPaidAmount;
					if (StringUtils.isNotBlank(temp.getRowNos())) {
						rowNos = StringUtils.isBlank(rowNos) ? temp.getRowNos() : rowNos + "," + temp.getRowNos();
					}
					if (StringUtils.isNotBlank(temp.getColNos())) {
						colNos = StringUtils.isBlank(colNos) ? temp.getColNos() : colNos + "," + temp.getColNos();
					}
					if (StringUtils.isNotBlank(temp.getRefundRowNos())) {
						refundRowNos = StringUtils.isBlank(refundRowNos) ? temp.getRefundRowNos() : refundRowNos + "," + temp.getRefundRowNos();
					}
					if (StringUtils.isNotBlank(temp.getRefundColNos())) {
						refundColNos = StringUtils.isBlank(refundColNos) ? temp.getRefundColNos() : refundColNos + "," + temp.getRefundColNos();
					}
				}
				reportSeatSaleGatherVenueArea.setRowNos(rowNos);
				reportSeatSaleGatherVenueArea.setColNos(colNos);
				reportSeatSaleGatherVenueArea.setRefundRowNos(refundRowNos);
				reportSeatSaleGatherVenueArea.setRefundColNos(refundColNos);
				reportSeatSaleGatherVenueArea.setUpdatetime(now);
				reportSeatSaleGatherVenueArea.setSaleCount(saleCount);
				reportSeatSaleGatherVenueArea.setAmount(amount);
				reportSeatSaleGatherVenueArea.setDiscount(discount);
				reportSeatSaleGatherVenueArea.setPaidAmount(paidAmount);
				reportSeatSaleGatherVenueArea.setRefundCount(refundCount);
				reportSeatSaleGatherVenueArea.setRefundAmount(refundAmount);
				reportSeatSaleGatherVenueArea.setRefundDiscount(refundDiscount);
				reportSeatSaleGatherVenueArea.setRefundPaidAmount(refundPaidAmount);
				reportSeatSaleGatherVenueArea.setSettlementAmount(settlementAmount);
				reportSeatSaleGatherVenueArea.setTotalQuantity(totalQuantity);
				reportSeatSaleGatherVenueArea.setTotalAmount(totalAmount);
				reportSeatSaleGatherVenueArea.setTotalDiscount(totalDisCount);
				reportSeatSaleGatherVenueArea.setTotalPaidAmount(totalPaidAmount);
				resultList.add(reportSeatSaleGatherVenueArea);
			}
		}

		fillMemberLevel(resultList, endTime);
		seatSaleGatherVenueAreaService.saveOrUpdateBatch(resultList);
		dbLogger.warn("选座订单明细报表（区域票种细分）：{}~{} {}", startTime, endTime, resultList.size());
	}

	private void fillMemberLevel(List<ReportSeatSaleGatherVenueArea> resultList, Timestamp endTime) {
			List<Long> memberIds = BeanUtil.getBeanPropertyList(resultList, "memberId", true);
			List<Long> companyIds = BeanUtil.getBeanPropertyList(resultList, "companyId", true);
			Map<String, ReportMemberLevelCardNoVo> cardNoMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(memberIds)) {
				List<ReportMemberLevelCardNoVo> memberCardNos = reportMemberInfoService.getMemberCardNos(memberIds, companyIds, endTime);
				cardNoMap = memberCardNos.stream().collect(Collectors.toMap(item -> item.getCompanyId() + "" + item.getMemberId(), item -> item, (oldObj, newObj) -> newObj));
			}
		for (ReportSeatSaleGatherVenueArea count : resultList) {
			ReportMemberLevelCardNoVo reportMemberLevelCardNoVo = cardNoMap.get(count.getCompanyId() + "" + count.getMemberId());
			if (reportMemberLevelCardNoVo != null) {
				count.setMemberId(reportMemberLevelCardNoVo.getMemberId());
				if (reportMemberLevelCardNoVo.getMembershipLevelId() != null && reportMemberLevelCardNoVo.getMembershipGrowthValue() != null
						&& reportMemberLevelCardNoVo.getGrowthValue() != null
						&& reportMemberLevelCardNoVo.getMembershipGrowthValue().compareTo(reportMemberLevelCardNoVo.getGrowthValue()) > 0) {
					count.setMemberLevelId(reportMemberLevelCardNoVo.getMembershipLevelId());
					count.setMemberLevelName(reportMemberLevelCardNoVo.getMembershipLevelName());
				} else {
					count.setMemberLevelId(reportMemberLevelCardNoVo.getMemberLevelId());
					count.setMemberLevelName(reportMemberLevelCardNoVo.getMemberLevelName());
				}
			}
		}
	}

	private String getSeatStr(String rowNos, String colNos, String refundRowNos, String refundColNos) {
		List<String> seatList = new ArrayList<>();
		List<String> refundSeatList = new ArrayList<>();
		if (StringUtils.isBlank(rowNos) || StringUtils.isBlank(colNos)) {
			return "";
		}
		String[] row = rowNos.split(",");
		String[] col = colNos.split(",");
		if (row.length != col.length) {
			return "";
		}
		if (row.length > 0) {
			for (int i = 0; i < row.length; i++) {
				seatList.add(row[i] + "排" + col[i] + "座");
			}
		}
		if (StringUtils.isNotBlank(refundRowNos) && StringUtils.isNotBlank(refundColNos)) {
			String[] refundRow = refundRowNos.split(",");
			String[] refundCol = refundColNos.split(",");
			if (refundRow.length == refundCol.length) {
				for (int i = 0; i < refundRow.length; i++) {
					refundSeatList.add(refundRow[i] + "排" + refundCol[i] + "座");
				}
			}
		}
		seatList.removeAll(refundSeatList);
		return StringUtils.join(seatList, ",");
	}


	@Override
	public ResultCode<Page<ReportSeatSaleGatherVenueArea>> getCounts(AuthUser user, SearReportSeatSaleGatherVenueArea search, Page<ReportSeatSaleGatherVenueArea> page) {
		if ((search.getStartTime() == null || search.getEndTime() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, false, true);
		QueryWrapper<ReportSeatSaleGatherVenueArea> wrapper = new QueryWrapper<>();
		wrapper.eq("company_id", user.getCompanyId());
		wrapper.ne("settlement_amount", 0.00);
		wrapper.orderByAsc("tickettime", "updatetime", "id");
		if (search.getStadiumId() != null) {
			wrapper.eq("stadium_id", search.getStadiumId());
		}
		if (search.getVenueId() != null) {
			wrapper.eq("venue_id", search.getVenueId());
		}
		if (search.getProgramId() != null) {
			wrapper.eq("program_id", search.getProgramId());
		}
		if (StringUtils.isNotBlank(search.getSellType())) {
			wrapper.eq("sell_type", search.getSellType());
		}
		if (StringUtils.isNotBlank(search.getPaymethod())) {
			wrapper.eq("paymethod", search.getPaymethod());
		}
		if (StringUtils.isNotBlank(search.getTradeNo())) {
			wrapper.eq("trade_no", search.getTradeNo());
		}
		if (StringUtils.isNotBlank(search.getOutTradeNo())) {
			wrapper.eq("out_trade_no", search.getOutTradeNo());
		}
		if (search.getUserGroupId() != null) {
			wrapper.eq("user_group_id", search.getUserGroupId());
		}
		if (StringUtils.isNotBlank(search.getContactMobile())) {
			wrapper.eq("contact_mobile", search.getContactMobile());
		}
		if (StringUtils.isNotBlank(search.getTicketUser())) {
			wrapper.eq("ticket_user", search.getTicketUser());
		}
		if (StringUtils.isNotBlank(search.getRealName())) {
			wrapper.like("real_name", "%" + search.getRealName() + "%");
		}
		if (ObjectUtils.isNotEmpty(search.getStartTime())) {
			wrapper.ge("tickettime", search.getStartTime());
		}
		if (ObjectUtils.isNotEmpty(search.getEndTime())) {
			wrapper.le("tickettime", search.getEndTime());
		}
		if (ObjectUtils.isNotEmpty(search.getPlayStartTime())) {
			wrapper.ge("play_time", search.getPlayStartTime());
		}
		if (ObjectUtils.isNotEmpty(search.getPlayEndTime())) {
			wrapper.le("play_time", search.getPlayEndTime());
		}
		if (StringUtils.isNotBlank(search.getUserName())) {
			wrapper.like("user_name", "%" + search.getUserName() + "%");
		}
		if (StringUtils.isNotBlank(search.getPlatform())) {
			wrapper.eq("platform", search.getPlatform());
		}
		if (StringUtils.isNotBlank(search.getShowIds())) {
			List<Long> showIdList = BeanUtil.getIdList(search.getShowIds(), ",");
			wrapper.in("show_id", showIdList);
		}
		if (ObjectUtils.isNotEmpty(search.getPaidtimeStart())) {
			wrapper.ge("paidtime", search.getPaidtimeStart());
		}
		if (ObjectUtils.isNotEmpty(search.getPaidtimeEnd())) {
			wrapper.le("paidtime", search.getPaidtimeEnd());
		}
		if (StringUtils.isNotBlank(search.getDeliverySn())) {
			wrapper.eq("delivery_sn", search.getDeliverySn());
		}
		if (StringUtils.isNotBlank(search.getDeliveryStatus())) {
			wrapper.eq("delivery_status", search.getDeliveryStatus());
		}
		if (StringUtils.isNotBlank(search.getDeliveryCompanyCode())) {
			wrapper.eq("delivery_company_code", search.getDeliveryCompanyCode());
		}

		boolean haveConsumer = CollectionUtils.isNotEmpty(search.getAuthorityStadiumIds()) || search.getAuthorityUserGroupId() != null || search.getAuthorityUserId() != null;
		Consumer<QueryWrapper<ReportSeatSaleGatherVenueArea>> consumer = consumerWrapper -> {
			if (CollectionUtils.isNotEmpty(search.getAuthorityStadiumIds())) {
				consumerWrapper.in("stadium_id", search.getAuthorityStadiumIds());
			}
			if (search.getAuthorityUserGroupId() != null) {
				consumerWrapper.eq("user_group_id", search.getAuthorityUserGroupId());
			}
			if (search.getAuthorityUserId() != null) {
				consumerWrapper.eq("add_user_id", search.getAuthorityUserId());
			}
		};
		if (CollectionUtils.isNotEmpty(search.getAuthorityProgramIds())) {
			wrapper.and(wr -> wr.in("program_id", search.getAuthorityProgramIds()).or(haveConsumer, consumer));
		} else {
			wrapper.and(haveConsumer, consumer);
		}
		Page<ReportSeatSaleGatherVenueArea> pageResult = seatSaleGatherVenueAreaService.page(page, wrapper);
		List<Long> programIds = BeanUtil.getBeanPropertyList(pageResult.getRecords(), "programId", true);
		List<ProgramVo> ProgramVos = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(programIds)) {
			ProgramVos = commonUntransService.getPrograms(programIds);
		}
		Map<Long, ProgramVo> ProgramMap = BeanUtil.beanListToMap(ProgramVos, "id");
		for (ReportSeatSaleGatherVenueArea detail : pageResult.getRecords()) {
			if (StringUtils.isNotBlank(detail.getSellType())) {
				detail.setSellType(MapUtils.getString(commonUntransService.getAllSellType(user.getCompanyId()), detail.getSellType()));
			}
			ProgramVo programVo = ProgramMap.get(detail.getProgramId());
			if (programVo != null) {
				detail.setProgramStartTime(programVo.getStartTime());
				detail.setProgramEndTime(programVo.getEndTime());
			}
			String seatStr = getSeatStr(detail.getRowNos(), detail.getColNos(), detail.getRefundRowNos(), detail.getRefundColNos());
			detail.setSeatStr(seatStr);
		}

		return ResultCode.getSuccessReturn(pageResult);
	}
}

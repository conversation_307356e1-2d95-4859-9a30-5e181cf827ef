package com.llwh.dcenter.untrans.report.impl;

import java.util.List;
import com.llwh.dcenter.service.report.ReportMallOrderItemService;
import com.llwh.dcenter.untrans.report.ReportMallOrderItemUntransService;
import com.llwh.dcenter.vo.common.MemberActionStatsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
 * 功能描述: <br>
 * @Author: zhangbiaoyan
 * @Date: 2023/3/6 15:28
 */
@Service
public class ReportMallOrderItemUntransServiceImpl implements ReportMallOrderItemUntransService {
    @Autowired
    private ReportMallOrderItemService reportMallOrderItemService;

    @Override
    public List<MemberActionStatsVo> getMemberActionStatsByMemberId(Long companyId, Long memberId) {
        return reportMallOrderItemService.getMemberActionStatsByMemberId(companyId,memberId);
    }

    @Override
    public MemberActionStatsVo getOfflineMemberActionStatsByMemberId(Long companyId, Long memberId) {
        return reportMallOrderItemService.getOfflineMemberActionStatsByMemberId(companyId,memberId);
    }
}

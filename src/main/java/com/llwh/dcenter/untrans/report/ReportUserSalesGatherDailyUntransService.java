package com.llwh.dcenter.untrans.report;

import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.llwh.dcenter.helper.ReportUserSalesGatherDailySearch;

/**
 * <AUTHOR>
 * @since 2024/6/1 18:11
 */
public interface ReportUserSalesGatherDailyUntransService {
	/**
	 * 用户销售统计日报
	 * @param user
	 * @param search
	 * @return
	 */
	ResultCode<Map> getCounts(AuthUser user, ReportUserSalesGatherDailySearch search);

}

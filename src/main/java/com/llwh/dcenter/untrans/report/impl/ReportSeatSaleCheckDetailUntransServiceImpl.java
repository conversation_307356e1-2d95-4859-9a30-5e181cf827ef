package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import cn.fancylab.api.theatre.common.api.StadiumApiService;
import cn.fancylab.api.thvendor.general.api.OriginChannelApiService;
import cn.fancylab.compute.Pair;
import cn.fancylab.support.AuthUser;
import cn.fancylab.support.BizRTException;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.ticket.Status;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.JsonUtils;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.ValueUtil;
import cn.fancylab.util.VoCopyUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.base.constant.GroupType;
import com.llwh.base.constant.Platform;
import com.llwh.dcenter.constant.IdCardConst;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportSeatSaleCheckDetail;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportKeyAccountService;
import com.llwh.dcenter.service.report.ReportMemberInfoService;
import com.llwh.dcenter.service.report.ReportSeatSaleCheckDetailService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleCheckDetailUntransService;
import com.llwh.dcenter.vo.ReportSeatSaleCheckDetailVo;
import com.llwh.dcenter.vo.ScheduleSeatSimpleVo;
import com.llwh.dcenter.vo.common.ApiUserVo;
import com.llwh.dcenter.vo.common.PaymethodVo;
import com.llwh.dcenter.vo.common.ProgramVo;
import com.llwh.dcenter.vo.common.UserGroupVo;
import com.llwh.dcenter.vo.report.ReportKeyAccountVo;
import com.llwh.dcenter.vo.report.ReportMemberLevelCardNoVo;
import com.llwh.dcenter.vo.report.ReportProgramTodaySaleVo;
import com.llwh.dcenter.vo.report.ReportSeatDetailTotalVo;
import com.llwh.dcenter.vo.report.ReportShowTodaySaleVo;
import com.llwh.dcenter.vo.report.ReportTodaySaleVo;
import com.llwh.dcenter.vo.report.ReportTotalTodaySaleVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportSeatSaleCheckDetailUntransServiceImpl implements ReportSeatSaleCheckDetailUntransService {

	@Autowired
	private ReportSeatSaleCheckDetailService seatSaleCheckDetailService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	private static final String UK = "ReportSeatSaleCheckDetail";
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());
	@Autowired
	private CommonUntransService commonUntransService;

	@Autowired
	private ReportMemberInfoService reportMemberInfoService;

	@Autowired
	private ReportKeyAccountService reportKeyAccountService;
	@Autowired
	private DataLevelUntransService dataLevelUntransService;

	@DubboReference(version = "1.0")
	private StadiumApiService stadiumApiService;

	@DubboReference(version = "1.0")
	private OriginChannelApiService originChannelApiService;

	private static final String CUSTOMERCATEGORY = "keyaccount";

	@Override
	public void updateSeatSaleCheckDetailJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			startTime = DateUtil.addMinute(record.getLastUpdateTime(), ReportConstant.UPDATE_REPEAT_MINUTE);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = DateUtil.getCurTruncTimestamp();
		}
		try {
			updateSeatSaleCheckDetailCount(startTime, endTime, null, null);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateSeatSaleCheckDetailCount(Timestamp startTime, Timestamp endTime, String tradeNo, String uuid) {
		List<ReportSeatSaleCheckDetail> countList = new ArrayList<>();
		List<ReportSeatSaleCheckDetail> seatCount = seatSaleCheckDetailService.getSeatCount(startTime, endTime, tradeNo, uuid);
		if (CollectionUtils.isNotEmpty(seatCount)) {
			countList.addAll(seatCount);
		}

		List<ReportSeatSaleCheckDetail> refundSeatCount = seatSaleCheckDetailService.getRefundSeatCount(startTime, endTime, tradeNo, uuid);
		if (CollectionUtils.isNotEmpty(refundSeatCount)) {
			countList.addAll(refundSeatCount);
		}
		List<Long> memberIds = BeanUtil.getBeanPropertyList(countList,"memberId",true);
		List<Long> companyIds = BeanUtil.getBeanPropertyList(countList,"companyId",true);
		Map<String, ReportMemberLevelCardNoVo>  cardNoMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(memberIds)) {
			List<ReportMemberLevelCardNoVo> memberCardNos = reportMemberInfoService.getMemberCardNos(memberIds, companyIds, DateUtil.getCurFullTimestamp());
			cardNoMap = memberCardNos.stream().collect(Collectors.toMap(item ->{
				return item.getCompanyId() + "" + item.getMemberId();
			} , item -> item, (oldObj, newObj) -> newObj));
		}

		List<Long> customerIds = countList.stream().filter(item-> StringUtils.equals(CUSTOMERCATEGORY,item.getCustomerCategory()))
				.map(ReportSeatSaleCheckDetail::getCustomerId)
				.collect(Collectors.toList());
		Map<String, ReportKeyAccountVo>  customerMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(customerIds)) {
			List<ReportKeyAccountVo> unitNames = reportKeyAccountService.getReportKeyAccountVoByIds(customerIds);
			customerMap = unitNames.stream().collect(Collectors.toMap(item ->{
				return item.getCompanyId() + CUSTOMERCATEGORY + item.getCustomerId();
			} , item -> item));
		}


		List<String> names = BeanUtil.getBeanPropertyList(countList,"paymethod",true);
		List<PaymethodVo> paymethodVos = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(names)){
			paymethodVos = commonUntransService.getPayMethods(names);
		}
		List<PaymethodVo> finalPaymethodVos = paymethodVos;

		Map<Long, Map<String, String>> lockTypesMap = countList.stream().map(ReportSeatSaleCheckDetail::getCompanyId).distinct().map(aLong -> {
			ResultCode<List<Map>> holdTypes = stadiumApiService.getLockTypeList(aLong);
			List<Map> data = holdTypes.getData();
			Pair<Long, Map<String, String>> pair = new Pair<>();
			pair.setKey(aLong);
			Map<String, String> tempMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(data)) {
				for (Map datum : data) {
					tempMap.put(MapUtils.getString(datum, "value"), MapUtils.getString(datum, "name"));
				}
			}
			pair.setValue(tempMap);
			return pair;
		}).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
		Map<Long, Map<String, String>> holdTypesMap = countList.stream().map(ReportSeatSaleCheckDetail::getCompanyId).distinct().map(aLong -> {
			ResultCode<List<Map>> holdTypes = stadiumApiService.getHoldTypes(aLong);
			List<Map> data = holdTypes.getData();
			Pair<Long, Map<String, String>> pair = new Pair<>();
			pair.setKey(aLong);
			Map<String, String> tempMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(data)) {
				for (Map datum : data) {
					tempMap.put(MapUtils.getString(datum, "value"), MapUtils.getString(datum, "name"));
				}
			}
			pair.setValue(tempMap);
			return pair;
		}).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
		Map<Long, Map<String, String>> reserveTypesMap = countList.stream().map(ReportSeatSaleCheckDetail::getCompanyId).distinct().map(aLong -> {
			ResultCode<List<Map>> holdTypes = stadiumApiService.getReserveTypeList(aLong);
			List<Map> data = holdTypes.getData();
			Pair<Long, Map<String, String>> pair = new Pair<>();
			pair.setKey(aLong);
			pair.setValue(BeanUtil.getKeyValuePairMap(data, "code", "name"));
			return pair;
		}).collect(Collectors.toMap(Pair::getKey, Pair::getValue));

		for (ReportSeatSaleCheckDetail count:countList){
			if(CollectionUtils.isNotEmpty(finalPaymethodVos)){
				finalPaymethodVos.forEach(vo -> {
					if(vo.getName().equals(count.getPaymethod())){
						count.setPaymethod(vo.getCnName());
					}
				});
			}
			if(StringUtils.isNotBlank(count.getAdditionInfo())){
				java.util.Map<String, String> tempMap = JsonUtils.readJsonToMap(count.getAdditionInfo());
				if(StringUtils.isNotBlank(MapUtils.getString(tempMap,"layoutRemark"))){
					count.setTicketMessage(MapUtils.getString(tempMap,"layoutRemark"));
				}
				if(StringUtils.isNotBlank(MapUtils.getString(tempMap,"relateInfo"))){
					count.setOrderRelatedMessage(MapUtils.getString(tempMap,"relateInfo"));
				}
				if(StringUtils.isNotBlank(MapUtils.getString(tempMap,"remark"))){
					count.setDescription(MapUtils.getString(tempMap,"remark"));
				}
			}
			ReportMemberLevelCardNoVo reportMemberLevelCardNoVo = cardNoMap.get(count.getCompanyId() + "" + count.getMemberId());
			if (reportMemberLevelCardNoVo != null) {
				count.setMemberId(reportMemberLevelCardNoVo.getMemberId());
				count.setCardNo(reportMemberLevelCardNoVo.getCardNo());

				if(reportMemberLevelCardNoVo.getMembershipLevelId() != null && reportMemberLevelCardNoVo.getMembershipGrowthValue() != null
					&& reportMemberLevelCardNoVo.getGrowthValue() != null
						&& reportMemberLevelCardNoVo.getMembershipGrowthValue().compareTo(reportMemberLevelCardNoVo.getGrowthValue()) > 0){
					count.setMemberLevelId(reportMemberLevelCardNoVo.getMembershipLevelId());
					count.setMemberLevelName(reportMemberLevelCardNoVo.getMembershipLevelName());
				}else{
					count.setMemberLevelId(reportMemberLevelCardNoVo.getMemberLevelId());
					count.setMemberLevelName(reportMemberLevelCardNoVo.getMemberLevelName());
				}


				count.setMemberStatus(reportMemberLevelCardNoVo.getStatus());
			}
			ReportKeyAccountVo reportKeyAccountVo = customerMap.get(count.getCompanyId() + CUSTOMERCATEGORY + count.getCustomerId());
			if (reportKeyAccountVo != null) {
				count.setCustomerCategory(CUSTOMERCATEGORY);
				count.setCustomerUnitName(reportKeyAccountVo.getCustomerUnitName());
			}
			count.setUpdatetime(DateUtil.getCurFullTimestamp());

			String reserveNoName = count.getReserveNoName();
			if (StringUtils.equals("预留", reserveNoName)) {
				Map<String, String> typeMap = reserveTypesMap.get(count.getCompanyId());
				count.setHoldTypeName(MapUtils.getString(typeMap, count.getHoldFlag()));
			} else if (StringUtils.equals("保留", reserveNoName)) {
				Map<String, String> typeMap = holdTypesMap.get(count.getCompanyId());
				count.setHoldTypeName(MapUtils.getString(typeMap, count.getHoldFlag()));
			} else if (StringUtils.equals("锁座", reserveNoName)) {
				Map<String, String> typeMap = lockTypesMap.get(count.getCompanyId());
				count.setHoldTypeName(MapUtils.getString(typeMap, count.getHoldFlag()));
			}

		}
		seatSaleCheckDetailService.saveOrUpdateBatch(countList);
		if (StringUtils.isBlank(tradeNo)) {
			dbLogger.warn("刷新座票销售及入场明细表：{}~{} {}", startTime, endTime, countList.size());
		}
	}


	@Override
	public ResultCode<Page<ReportSeatSaleCheckDetail>> getCounts(AuthUser user, SearReportStandCheckDetailCount search, Page<ReportSeatSaleCheckDetail> page) {
		if ((search.getStartTime() == null || search.getEndTime() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null) &&
				(search.getOrderTimeFrom() == null || search.getOrderTimeTo() == null)) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, false, true);
		QueryWrapper<ReportSeatSaleCheckDetail> wrapper = new QueryWrapper<>();
		wrapper.eq("company_id", user.getCompanyId());
		wrapper.orderByAsc("tickettime","updatetime","id");
		if (search.getStadiumId() != null) {
			wrapper.eq("stadium_id", search.getStadiumId());
		}
		if (search.getVenueId() != null) {
			wrapper.eq("venue_id", search.getVenueId());
		}
		if (search.getProgramId() != null) {
			wrapper.eq("program_id",search.getProgramId());
		}
		if (search.getShowNameId() != null) {
			wrapper.eq("show_id", search.getShowNameId());
		}
		if (search.getTicketTypeId() != null) {
			wrapper.eq("ticket_type_id",search.getTicketTypeId());
		}
		if (StringUtils.isNotBlank(search.getSellType())) {
			wrapper.eq("sell_type", search.getSellType());
		}
		if (StringUtils.isNotBlank(search.getPaymethod())) {
			wrapper.eq("paymethod", search.getPaymethod());
		}
		if (StringUtils.isNotBlank(search.getTradeNo())) {
			wrapper.eq("trade_no", search.getTradeNo());
		}
		if (StringUtils.isNotBlank(search.getOutTradeNo())) {
			wrapper.eq("out_trade_no", search.getOutTradeNo());
		}
		if (StringUtils.isNotBlank(search.getChannel())) {
			wrapper.eq("channel", search.getChannel());
		}
		if (StringUtils.isNotBlank(search.getContactMobile())) {
			wrapper.eq("contact_mobile", search.getContactMobile());
		}
		if (StringUtils.isNotBlank(search.getTicketUser())) {
			wrapper.eq("ticket_user", search.getTicketUser());
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			wrapper.like("program_code","%" + search.getProgramCode() + "%");
		}

		if (StringUtils.isNotBlank(search.getStadiumName())) {
			wrapper.like("stadium_name","%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			wrapper.like("venue_name","%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			wrapper.like("program_name","%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getRealName())) {
			wrapper.like("real_name","%" + search.getRealName() + "%");
		}
		if (StringUtils.isNotBlank(search.getCategory())) {
			wrapper.like("category","%" + search.getCategory() + "%");
		}
		if (StringUtils.isNotBlank(search.getSmallCategory())) {
			wrapper.like("small_category","%" + search.getSmallCategory() + "%");
		}
		if (ObjectUtils.isNotEmpty(search.getStartTime())) {
			wrapper.ge("tickettime", search.getStartTime());
		}
		if (ObjectUtils.isNotEmpty(search.getEndTime())) {
			wrapper.le("tickettime", search.getEndTime());
		}
		if (ObjectUtils.isNotEmpty(search.getOrderTimeFrom())) {
			wrapper.ge("ordertime", search.getOrderTimeFrom());
		}
		if (ObjectUtils.isNotEmpty(search.getOrderTimeTo())) {
			wrapper.le("ordertime", search.getOrderTimeTo());
		}
		if (ObjectUtils.isNotEmpty(search.getPlayStartTime())) {
			wrapper.ge("play_time", search.getPlayStartTime());
		}
		if (ObjectUtils.isNotEmpty(search.getPlayEndTime())) {
			wrapper.le("play_time", search.getPlayEndTime());
		}
		if (ObjectUtils.isNotEmpty(search.getCheckTimeFrom())) {
			wrapper.ge("check_time", search.getCheckTimeFrom());
		}
		if (ObjectUtils.isNotEmpty(search.getCheckTimeTo())) {
			wrapper.le("check_time", search.getCheckTimeTo());
		}
		if (StringUtils.equals(search.getOrderStatus(), "paidNoTickets")) {
			wrapper.like("order_status", "paid%");
			wrapper.notIn("order_status", "paid_success","paid_return","paid_return_cert","paid_return_succ","paid_return_fail");
		}
		if (StringUtils.equals(search.getOrderStatus(), "paidSuccess")) {
			wrapper.eq("order_status", "paid_success");
		}
		if (StringUtils.equals(search.getOrderStatus(), "paidRefund")) {
			wrapper.like("order_status", "paid_return%");
		}
		if (StringUtils.equals(search.getOrderStatus(), "unPaid")) {
			wrapper.like("order_status", "cancel%").or()
					.like("order_status", "new%");
		}
		if (StringUtils.isNotBlank(search.getTicketMessage())) {
			wrapper.like("ticket_message","%" + search.getTicketMessage() + "%");
		}
		if (StringUtils.isNotBlank(search.getOrderRelatedMessage())) {
			wrapper.like("order_related_message","%" + search.getOrderRelatedMessage() + "%");
		}
		if (StringUtils.isNotBlank(search.getDescription())) {
			wrapper.like("description","%" + search.getDescription() + "%");
		}
		if (StringUtils.isNotBlank(search.getCustomerUnitName())){
			wrapper.like("customer_unit_name","%" + search.getCustomerUnitName() + "%");
		}
		if (StringUtils.isNotBlank(search.getUserName())){
			wrapper.like("user_name","%" + search.getUserName() + "%");
		}
		if (StringUtils.isNotBlank(search.getPlatform())){
			wrapper.eq("platform",search.getPlatform() );
		}
		if (search.getPrintStatus() !=null){
			if (search.getPrintStatus()){
				wrapper.gt("print_num",0);
			}else {
				wrapper.eq("print_num",0);
			}
		}

		if (StringUtils.isNotBlank(search.getMemberType())){
			if (StringUtils.equals("ordinary",search.getMemberType())){
				wrapper.isNotNull("mobile");
			} else if (StringUtils.equals("customer",search.getMemberType())) {
				wrapper.isNotNull("customer_unit_name");
			}
		}

		if(StringUtils.isNotBlank(search.getMemberLevelIds())){
			List<Long> memberLevelIdList = BeanUtil.getIdList(search.getMemberLevelIds(), ",");
			wrapper.in("member_level_id", memberLevelIdList);
			wrapper.eq("member_status", Status.Y);
		}

		if(StringUtils.isNotBlank(search.getShowIds())){
			List<Long> showIdList = BeanUtil.getIdList(search.getShowIds(), ",");
			wrapper.in("show_id", showIdList);
		}
		if (ObjectUtils.isNotEmpty(search.getPaidtimeStart())) {
			wrapper.ge("paidtime", search.getPaidtimeStart());
		}
		if (ObjectUtils.isNotEmpty(search.getPaidtimeEnd())) {
			wrapper.le("paidtime", search.getPaidtimeEnd());
		}
		if (StringUtils.isNotBlank(search.getSeatAttr())){
			wrapper.eq("seatAttr", "%" + search.getSeatAttr()  + "%");
		}
		if (StringUtils.isNotBlank(search.getOrganizerType())) {
			wrapper.like("organizer_type", "%" + search.getOrganizerType() + "%");
		}

		boolean haveConsumer = CollectionUtils.isNotEmpty(search.getAuthorityStadiumIds()) || search.getAuthorityUserGroupId() != null || search.getAuthorityUserId() != null;
		Consumer<QueryWrapper<ReportSeatSaleCheckDetail>> consumer = consumerWrapper -> {
			if (CollectionUtils.isNotEmpty(search.getAuthorityStadiumIds())) {
				consumerWrapper.in("stadium_id", search.getAuthorityStadiumIds());
			}
			if (search.getAuthorityUserGroupId() != null) {
				consumerWrapper.eq("user_group_id", search.getAuthorityUserGroupId());
			}
			if (search.getAuthorityUserId() != null) {
				consumerWrapper.eq("add_user_id", search.getAuthorityUserId());
			}
		};
		if (CollectionUtils.isNotEmpty(search.getAuthorityProgramIds())) {
			wrapper.and(wr -> wr.in("program_id", search.getAuthorityProgramIds()).or(haveConsumer, consumer));
		} else {
			wrapper.and(haveConsumer, consumer);
		}
		Page<ReportSeatSaleCheckDetail> pageResult = seatSaleCheckDetailService.page(page, wrapper);
		/*
		List<Long> ticketPriceIds = BeanUtil.getBeanPropertyList(pageResult.getRecords(),"ticketPriceId",true);
		List<TicketTypeVo> ticketPriceVos = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(ticketPriceIds)){
			ticketPriceVos = commonUntransService.getTicketPrices(ticketPriceIds);
		}
		*/
		List<Long> programIds = BeanUtil.getBeanPropertyList(pageResult.getRecords(),"programId",true);
		List<ProgramVo> ProgramVos = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(programIds)){
			ProgramVos = commonUntransService.getPrograms(programIds);
		}
		Map<Long, ProgramVo> ProgramMap = BeanUtil.beanListToMap(ProgramVos, "id");
		for(ReportSeatSaleCheckDetail detail : pageResult.getRecords()){
			if(StringUtils.isNotBlank(detail.getCertificateType())){
				detail.setCertificateType(IdCardConst.idCardMap.get(detail.getCertificateType()));
			}
			if(StringUtils.isNotBlank(detail.getSellType())){
				detail.setSellType(MapUtils.getString(commonUntransService.getAllSellType(user.getCompanyId()), detail.getSellType()));
			}
			ProgramVo programVo = ProgramMap.get(detail.getProgramId());
			if(programVo != null){
				detail.setProgramStartTime(programVo.getStartTime());
				detail.setProgramEndTime(programVo.getEndTime());
			}
			/*
			for(TicketTypeVo vo : ticketPriceVos){
				if(Objects.equals(detail.getTicketPriceId(), vo.getId())){
//					detail.setTicketPrice(vo.getTicketPrice());
					detail.setTicketDescription(vo.getDescription());
					detail.setTicketRemark(vo.getRemark());
					break;
				}
			}
			*/
			if(!Status.isY(detail.getMemberStatus())){
				detail.setMemberLevelName("");
				detail.setCardNo("");
			}
		}

		return ResultCode.getSuccessReturn(pageResult);
	}

	@Override
	public ResultCode<Page<ReportSeatSaleCheckDetailVo>> getCountsBySql(AuthUser user, SearReportStandCheckDetailCount search, Page<ReportSeatSaleCheckDetailVo> page) {
		if(StringUtils.isBlank(search.getShowIds()) && (search.getStartTime() == null || search.getEndTime() == null)
				&& (search.getPaidtimeStart() == null || search.getPaidtimeEnd() == null)) {
			return ResultCodeHelper.CODE11_PARAMS_REQUIRED("项目场次、出票/退票时间、支付时间，三个筛选条件至少选择一个");
		}
		packSearReportStandCheckDetailCount(user, search);

		Page<ReportSeatSaleCheckDetailVo> pageResult = seatSaleCheckDetailService.getCounts(page, search);
		tagSourceNew(pageResult.getRecords(), user.getCompanyId());
		/*
		List<Long> ticketPriceIds = BeanUtil.getBeanPropertyList(pageResult.getRecords(),"ticketPriceId",true);
		List<TicketTypeVo> ticketPriceVos = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(ticketPriceIds)){
			ticketPriceVos = commonUntransService.getTicketPrices(ticketPriceIds);
		}
		*/
		List<Long> programIds = BeanUtil.getBeanPropertyList(pageResult.getRecords(),"programId",true);
		List<ProgramVo> ProgramVos = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(programIds)){
			ProgramVos = commonUntransService.getPrograms(programIds);
		}
		Map<Long, ProgramVo> ProgramMap = BeanUtil.beanListToMap(ProgramVos, "id");
		Map sellTypeMap = commonUntransService.getAllSellType(user.getCompanyId());
		Map<String, String> originRemarkMap = new HashMap<>();
		if(CollectionUtils.isNotEmpty(pageResult.getRecords())){
			ResultCode<Map<String, String>> originRemarkRes = originChannelApiService.getOriginRemarkMap(user.getCompanyId());
			if(originRemarkRes.isSuccess()){
				originRemarkMap = originRemarkRes.getData();
			}
		}
		for(ReportSeatSaleCheckDetailVo detail : pageResult.getRecords()){
			if(StringUtils.isNotBlank(detail.getCertificateType())){
				detail.setCertificateType(IdCardConst.idCardMap.get(detail.getCertificateType()));
			}
			if(StringUtils.isNotBlank(detail.getSellType())){
				detail.setSellType(MapUtils.getString(sellTypeMap, detail.getSellType()));
			}
			ProgramVo programVo = ProgramMap.get(detail.getProgramId());
			if(programVo != null){
				detail.setProgramStartTime(programVo.getStartTime());
				detail.setProgramEndTime(programVo.getEndTime());
			}
			/*
			for(TicketTypeVo vo : ticketPriceVos){
				if(Objects.equals(detail.getTicketPriceId(), vo.getId())){
//					detail.setTicketPrice(vo.getTicketPrice());
					detail.setTicketDescription(vo.getDescription());
					detail.setTicketRemark(vo.getRemark());
					break;
				}
			}
			*/
			if(!Status.isY(detail.getMemberStatus())){
				detail.setMemberLevelName("");
				detail.setCardNo("");
			}
			if(StringUtils.isNotBlank(detail.getOrigin())){
				detail.setOriginRemark(originRemarkMap.get(detail.getOrigin()));
			}
		}

		return ResultCode.getSuccessReturn(pageResult);
	}

	private SearReportStandCheckDetailCount packSearReportStandCheckDetailCount(AuthUser user, SearReportStandCheckDetailCount search) {
		dataLevelUntransService.getDataLevelSearch(search, user, false, true);
		search.setCompanyId(user.getCompanyId());
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%"+search.getProgramCode()+"%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getRealName())) {
			search.setRealName("%"+search.getRealName()+"%");
		}
		if (StringUtils.isNotBlank(search.getCategory())) {
			search.setCategory("%"+search.getCategory()+"%");
		}
		if (StringUtils.isNotBlank(search.getSmallCategory())) {
			search.setSmallCategory("%"+search.getSmallCategory()+"%");
		}
		if (StringUtils.isNotBlank(search.getTicketMessage())) {
			search.setTicketMessage("%" + search.getTicketMessage() + "%");
		}
		if (StringUtils.isNotBlank(search.getOrderRelatedMessage())) {
			search.setOrderRelatedMessage("%" + search.getOrderRelatedMessage() + "%");
		}
		if (StringUtils.isNotBlank(search.getDescription())) {
			search.setDescription("%" + search.getDescription() + "%");
		}
		if (StringUtils.isNotBlank(search.getCustomerUnitName())) {
			search.setCustomerUnitName("%" + search.getCustomerUnitName() + "%");
		}
		if (StringUtils.isNotBlank(search.getUserName())) {
			search.setUserName("%" + search.getUserName() + "%");
		}
		if(StringUtils.isNotBlank(search.getMemberLevelIds())){
			List<Long> memberLevelIdList = BeanUtil.getIdList(search.getMemberLevelIds(), ",");
			search.setMemberLevelIdList(memberLevelIdList);
		}
		if(StringUtils.isNotBlank(search.getShowIds())){
			List<Long> showIdList = BeanUtil.getIdList(search.getShowIds(), ",");
			search.setShowIdList(showIdList);
		}
		if (StringUtils.isNotBlank(search.getSeatAttr())){
			search.setSeatAttr("%" + search.getSeatAttr()  + "%");
		}
		if (StringUtils.isNotBlank(search.getOrganizerType())){
			search.setOrganizerType("%" + search.getOrganizerType()  + "%");
		}
		if(StringUtils.isNotBlank(search.getOrigins())){
			String[] originArray = search.getOrigins().split(",");
			List<String> originList = Arrays.asList(originArray);
			search.setOriginList(originList);
		}
		if (StringUtils.isNotBlank(search.getStatus()) && search.getStatus().contains(",")) {
			String[] statusArray = search.getStatus().split(",");
			List<String> statusList = Arrays.asList(statusArray);
			search.setStatusList(statusList);
			search.setStatus(null);
		}
		fillSearchBySoruce(search, user);
		return search;
	}

	private void tagSourceNew(List<ReportSeatSaleCheckDetailVo> dataList, Long companyId) {
		if(CollectionUtils.isEmpty(dataList)){
			return;
		}
		List<ApiUserVo> apiUsers = commonUntransService.findItemsByCompanyId(companyId);
		List<Long> apiUserIds = BeanUtil.getBeanPropertyList(apiUsers, "guid", true);
		List<Long> userGroupIds = BeanUtil.getBeanPropertyList(dataList, "userGroupId", true);
		List<UserGroupVo> userGroups = commonUntransService.getUserGroupByIds(userGroupIds);
		Map<Long, UserGroupVo> userGroupMap = BeanUtil.beanListToMap(userGroups, "id");
		for (ReportSeatSaleCheckDetailVo vo : dataList) {
			// 填充来源、渠道
			UserGroupVo groupVo = userGroupMap.get(vo.getUserGroupId());
			String groupType = groupVo.getUserGroupType();
			if (apiUserIds.contains(vo.getAddUserId())) {
				vo.setSource("线上自营");
				vo.setSourceChannel(MapUtils.getString(Platform.ONLINE_PLATFORM_MAP, vo.getPlatform()));
			} else if (StringUtils.equals(GroupType.OTA, groupType)) {
				vo.setSource("线上代理");
				vo.setSourceChannel(groupVo.getUserGroupName());
			} else if (StringUtils.equals(GroupType.AGENT, groupType)) {
				vo.setSource("线下代理");
				vo.setSourceChannel(groupVo.getUserGroupName());
			} else if (StringUtils.equals(Platform.PLATFORM_OFFLINE, vo.getPlatform())) {
				vo.setSource("线下票房");
				vo.setSourceChannel(groupVo.getUserGroupName());
			}
		}

	}

	@Override
	public List<List<Object>> getTotalCount(AuthUser user, SearReportStandCheckDetailCount search) {
		List<List<Object>> resultList = new ArrayList<>();
		if ((search.getStartTime() == null || search.getEndTime() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return resultList;
		}
		search.setCompanyId(user.getCompanyId());
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%"+search.getProgramCode()+"%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getRealName())) {
			search.setRealName("%"+search.getRealName()+"%");
		}
		if (StringUtils.isNotBlank(search.getCategory())) {
			search.setCategory("%"+search.getCategory()+"%");
		}
		if (StringUtils.isNotBlank(search.getSmallCategory())) {
			search.setSmallCategory("%"+search.getSmallCategory()+"%");
		}
		List<ReportSeatSaleCheckDetail> result = seatSaleCheckDetailService.getTotalCount(search);
		for (ReportSeatSaleCheckDetail detail : result) {
			List<Object> objList = new ArrayList<>();
			objList.add(detail.getTradeNo());
			objList.add(detail.getMobile());
			objList.add(detail.getTickettime());
			objList.add(detail.getProgramId());
			objList.add(detail.getProgramCode());
			objList.add(detail.getProgramName());
			objList.add(detail.getCategory());
			objList.add(detail.getSmallCategory());
			objList.add(detail.getStadiumName());
			objList.add(detail.getVolume());
			objList.add(detail.getVenueName());
			objList.add(detail.getShowName());
			objList.add(detail.getPlayTime());
			objList.add(detail.getVenueAreaName());
			objList.add(detail.getRowNo() + "排");
			objList.add(detail.getColNo() + "座");
			objList.add(detail.getUuid());
			objList.add(MapUtils.getString(commonUntransService.getAllSellType(user.getCompanyId()), detail.getSellType()));
			objList.add(detail.getRealName());
			objList.add(IdCardConst.idCardMap.get(detail.getCertificateType()));
			objList.add(detail.getCertificateNo());
			objList.add(detail.getAmount());
			objList.add(detail.getDiscount());
			objList.add(detail.getPaidAmount());
			objList.add(detail.getQuantity());
			objList.add(ReportConstant.payTypeMap.get(detail.getPayType()));
			objList.add(detail.getPaymethod());
			objList.add(detail.getPayseqno());
			objList.add(detail.getLogisticsMode());
			objList.add(detail.getLogisticsAddress());
			objList.add(detail.getTicketMessage());
			objList.add(detail.getOrderRelatedMessage());
			objList.add(detail.getDescription());
			objList.add(detail.getUserGroupName());
			objList.add(detail.getUserName());
			objList.add(detail.getCheckTime());
			objList.add(detail.getOrderStatus());
			objList.add(detail.getOutTradeNo());
			objList.add(detail.getChannel());
			objList.add(detail.getContactMobile());
			objList.add(detail.getTicketUser());
			resultList.add(objList);
		}
		return resultList;
	}

	@Override
	public ResultCode<ReportSeatDetailTotalVo> getTotals(AuthUser user, SearReportStandCheckDetailCount search){
		if(StringUtils.isBlank(search.getShowIds()) && (search.getStartTime() == null || search.getEndTime() == null)
				&& (search.getPaidtimeStart() == null || search.getPaidtimeEnd() == null)) {
			return ResultCodeHelper.CODE11_PARAMS_REQUIRED("项目场次、出票/退票时间、支付时间，三个筛选条件至少选择一个");
		}
		ReportSeatDetailTotalVo result = new ReportSeatDetailTotalVo();
		packSearReportStandCheckDetailCount(user, search);

		ReportSeatDetailTotalVo tmp = seatSaleCheckDetailService.getTotals(search);
		if (ObjectUtils.isNotEmpty(tmp)) {
			VoCopyUtil.copyFromObj(result, tmp);
		}
		initNum(result);

		Double balanceTotal = seatSaleCheckDetailService.getBalanceTotalAmount(search);
		result.setBalanceTotal(balanceTotal == null ? 0.00 : ValueUtil.roundDouble(balanceTotal));
		return ResultCode.getSuccessReturn(result);
	}

	/**
	 * 来源对应下拉选 source = online/ota/agent/offline
	 * 线上自营，线上代理（OTA），线下代理，线下票房
	 */
	private void fillSearchBySoruce(SearReportStandCheckDetailCount search, AuthUser user) {
		String source = search.getSource();
		if (StringUtils.isNotBlank(source)) {
			if (StringUtils.equals("online", source)) {
				List<ApiUserVo> apiUsers = commonUntransService.findItemsByCompanyId(user.getCompanyId());
				if (CollectionUtils.isEmpty(apiUsers)) {
					throw new BizRTException(ResultCodeHelper.CODE11_DATA_NOT_EXISTS, "自营用户不存在！");
				}
				search.setAddUserId4Source(BeanUtil.getBeanPropertyList(apiUsers, "guid", true));
			} else if (StringUtils.equalsAny(source, "ota", "agent")) {
				List<Long> groupIds = commonUntransService.getUserGroupIdsListByGroupType(user.getCompanyId(), source);
				if (CollectionUtils.isEmpty(groupIds)) {
					throw new BizRTException(ResultCodeHelper.CODE11_DATA_NOT_EXISTS, "代理用户组不存在");
				}
				search.setUserGroupIdList4Source(groupIds);
			} else if (StringUtils.equals("offline", source)) {
				search.setPlatform(Platform.PLATFORM_OFFLINE);
			}
		}
	}

	private void initNum(ReportSeatDetailTotalVo result){
		if(null == result.getAmount()){
			result.setAmount(0.00);
		}else{
			result.setAmount(ValueUtil.round(result.getAmount()));
		}
		if(null == result.getDiscount()){
			result.setDiscount(0.00);
		}else{
			result.setDiscount(ValueUtil.round(result.getDiscount()));
		}
		if(null == result.getPaidAmount()){
			result.setPaidAmount(0.00);
		}else{
			result.setPaidAmount(ValueUtil.round(result.getPaidAmount()));
		}
		if(null == result.getQuantity()){
			result.setQuantity(0);
		}
		if(null == result.getSettlementAmount()){
			result.setSettlementAmount(0.00);
		}else{
			result.setSettlementAmount(ValueUtil.round(result.getSettlementAmount()));
		}
		if(null == result.getTotalMobiles()){
			result.setTotalMobiles(0);
		}
	}




	@Override
	public ResultCode<Map> getTodayTotalsCount(Long companyId,Date date) {
		if (date == null){
			date = DateUtil.getCurDate();
		}
		ReportTotalTodaySaleVo total = seatSaleCheckDetailService.getTotalsByDate(companyId, date);
		List<ReportShowTodaySaleVo> scheduleTotalCountList= seatSaleCheckDetailService.getScheduleTotalCountByDate(companyId, date);
		Map<Long, ReportProgramTodaySaleVo> programMap = new HashMap<>();
		for(ReportShowTodaySaleVo showTodaySaleVo : scheduleTotalCountList){
			Long programId = showTodaySaleVo.getProgramId();
			ReportProgramTodaySaleVo programSale = programMap.get(programId);
			if (programSale == null){
				programSale = new ReportProgramTodaySaleVo();
				programSale.setProgramId(programId);
				programSale.setProgramName(showTodaySaleVo.getProgramName());
				programMap.put(programId,programSale);
			}
			List<ReportTodaySaleVo> prices = showTodaySaleVo.getPrices();
			if (CollectionUtils.isEmpty(prices)){
				continue;
			}
			for(ReportTodaySaleVo priceCount : prices){
				programSale.setSaleCount(sumInt(programSale.getSaleCount(),priceCount.getSaleCount()));
				programSale.setSaleAmount(sumDouble(programSale.getSaleAmount(),priceCount.getSaleAmount()));
			}
		}
		Map<String,Object> result = new HashMap<>();
		result.put("total",total);
		result.put("programSaleList",programMap.values());
		result.put("showPriceSaleList",scheduleTotalCountList);
		return ResultCode.getSuccessReturn(result);
	}

	private Integer sumInt(Integer num1, Integer num2) {
		if (num1 == null){
			return num2;
		}
		if (num2 == null){
			return num1;
		}
		return num1 + num2;
	}
	private Double sumDouble(Double num1, Double num2) {
		if (num1 == null){
			return num2;
		}
		if (num2 == null){
			return num1;
		}
		return ValueUtil.roundDouble(num1 + num2);
	}

	@Override
	public void refreshHistories() {
		int pageSize = 500;
		for (int i = 1; i < 10000; i++) {
			Page<ReportSeatSaleCheckDetail> page = new Page<>(i, pageSize);
			Page<ReportSeatSaleCheckDetail> pagingedQuery = seatSaleCheckDetailService.pagingQuery(page);
			List<ReportSeatSaleCheckDetail> records = pagingedQuery.getRecords();
			if (CollectionUtils.isEmpty(records)){
				return;
			}
			Map<Long, Map<String, String>> lockTypesMap = records.stream().map(ReportSeatSaleCheckDetail::getCompanyId).distinct().map(aLong -> {
				ResultCode<List<Map>> holdTypes = stadiumApiService.getLockTypeList(aLong);
				List<Map> data = holdTypes.getData();
				Pair<Long, Map<String, String>> pair = new Pair<>();
				pair.setKey(aLong);
				Map<String, String> tempMap = new HashMap<>();
				if (CollectionUtils.isNotEmpty(data)) {
					for (Map datum : data) {
						tempMap.put(MapUtils.getString(datum, "value"), MapUtils.getString(datum, "name"));
					}
				}
				pair.setValue(tempMap);
				return pair;
			}).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
			Map<Long, Map<String, String>> holdTypesMap = records.stream().map(ReportSeatSaleCheckDetail::getCompanyId).distinct().map(aLong -> {
				ResultCode<List<Map>> holdTypes = stadiumApiService.getHoldTypes(aLong);
				List<Map> data = holdTypes.getData();
				Pair<Long, Map<String, String>> pair = new Pair<>();
				pair.setKey(aLong);
				Map<String, String> tempMap = new HashMap<>();
				if (CollectionUtils.isNotEmpty(data)) {
					for (Map datum : data) {
						tempMap.put(MapUtils.getString(datum, "value"), MapUtils.getString(datum, "name"));
					}
				}
				pair.setValue(tempMap);
				return pair;
			}).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
			Map<Long, Map<String, String>> reserveTypesMap = records.stream().map(ReportSeatSaleCheckDetail::getCompanyId).distinct().map(aLong -> {
				ResultCode<List<Map>> holdTypes = stadiumApiService.getReserveTypeList(aLong);
				List<Map> data = holdTypes.getData();
				Pair<Long, Map<String, String>> pair = new Pair<>();
				pair.setKey(aLong);
				pair.setValue(BeanUtil.getKeyValuePairMap(data, "code", "name"));
				return pair;
			}).collect(Collectors.toMap(Pair::getKey, Pair::getValue));

			for (ReportSeatSaleCheckDetail record : records) {
				ScheduleSeatSimpleVo scheduleSeatVo = seatSaleCheckDetailService.getScheduleSeatVo(record.getShowId(), record.getVenueAreaId(), record.getRowNo(), record.getColNo());
				if (Objects.isNull(scheduleSeatVo)){
					continue;
				}
				String reserveNoName = scheduleSeatVo.getReserveNoName();
				record.setReserveNoName(reserveNoName);
				if (StringUtils.equals("预留", reserveNoName)) {
					Map<String, String> typeMap = reserveTypesMap.get(record.getCompanyId());
					record.setHoldTypeName(MapUtils.getString(typeMap, scheduleSeatVo.getHoldFlag()));
				} else if (StringUtils.equals("保留", reserveNoName)) {
					Map<String, String> typeMap = holdTypesMap.get(record.getCompanyId());
					record.setHoldTypeName(MapUtils.getString(typeMap, scheduleSeatVo.getHoldFlag()));
				} else if (StringUtils.equals("锁座", reserveNoName)) {
					Map<String, String> typeMap = lockTypesMap.get(record.getCompanyId());
					record.setHoldTypeName(MapUtils.getString(typeMap, scheduleSeatVo.getHoldFlag()));
				}
			}
			seatSaleCheckDetailService.saveOrUpdateBatch(records);
			dbLogger.warn("座票销售及入场明细表刷新！{} {}", page.getCurrent(), records.size());
			if (records.size() < pageSize){
				return;
			}
		}
	}

	@Override
	public void updateSeatSaleCheckDetailFields() {
		int pageSize = 500;
		for (int i = 1; i < 100000; i++) {
			Page<ReportSeatSaleCheckDetail> page = new Page<>(i, pageSize);
			Page<ReportSeatSaleCheckDetail> detailPage = seatSaleCheckDetailService.pagingQuery(page);
			List<ReportSeatSaleCheckDetail> recordList = detailPage.getRecords();
			if (CollectionUtils.isEmpty(recordList)) {
				return;
			}
			Map<String, List<ReportSeatSaleCheckDetail>> payTypeListMap = BeanUtil.groupBeanList(recordList, "payType");
			List<ReportSeatSaleCheckDetail> payList = payTypeListMap.get("pay");
			List<ReportSeatSaleCheckDetail> refundList = payTypeListMap.get("refund");
			Map<String, ReportSeatSaleCheckDetail> detailMap4pay = new HashMap<>();
			Map<String, ReportSeatSaleCheckDetail> detailMap4refund = new HashMap<>();
			if (CollectionUtils.isNotEmpty(payList)) {
				List<String> uuids = BeanUtil.getBeanPropertyList(payList, "uuid", true);
				List<ReportSeatSaleCheckDetail> detailList4pay = seatSaleCheckDetailService.getSeatCountByUuidList(uuids);
				detailMap4pay = detailList4pay.stream().collect(Collectors.toMap(e -> e.getTradeNo() + "#" + e.getUuid(), o -> o, (newValue, oldValue) -> oldValue));
				/*
				for (ReportSeatSaleCheckDetail detail : payList) {
					ReportSeatSaleCheckDetail detail4pay = detailMap4pay.get(detail.getTradeNo() + "#" + detail.getUuid());
					if (detail4pay != null) {
						LambdaUpdateWrapper<ReportSeatSaleCheckDetail> updateWrapper = new LambdaUpdateWrapper<>();
						updateWrapper.eq(ReportSeatSaleCheckDetail::getId, detail.getId());
						updateWrapper.set(ReportSeatSaleCheckDetail::getTicketRemark, detail4pay.getTicketRemark());
						updateWrapper.set(ReportSeatSaleCheckDetail::getTicketDescription, detail4pay.getTicketDescription());
						updateWrapper.set(ReportSeatSaleCheckDetail::getTicketName, detail4pay.getTicketName());
						seatSaleCheckDetailService.update(null, updateWrapper);
					}
				}
				*/
			}
			if (CollectionUtils.isNotEmpty(refundList)) {
				List<String> uuids = BeanUtil.getBeanPropertyList(refundList, "uuid", true);
				List<ReportSeatSaleCheckDetail> detailList4refund = seatSaleCheckDetailService.getRefundSeatCountByUuidList(uuids);
				detailMap4refund = detailList4refund.stream().collect(Collectors.toMap(e -> e.getTradeNo() + "#" + e.getUuid(), o -> o, (newValue, oldValue) -> oldValue));
				/*
				for (ReportSeatSaleCheckDetail detail : refundList) {
					ReportSeatSaleCheckDetail detail4refund = detailMap4refund.get(detail.getTradeNo() + "#" + detail.getUuid());
					if (detail4refund != null) {
						LambdaUpdateWrapper<ReportSeatSaleCheckDetail> updateWrapper = new LambdaUpdateWrapper<>();
						updateWrapper.eq(ReportSeatSaleCheckDetail::getId, detail.getId());
						updateWrapper.set(ReportSeatSaleCheckDetail::getPrintNum, detail4refund.getPrintNum());
						updateWrapper.set(ReportSeatSaleCheckDetail::getPrintTime, detail4refund.getPrintTime());
						updateWrapper.set(ReportSeatSaleCheckDetail::getOrderStatus, detail4refund.getOrderStatus());
						updateWrapper.set(ReportSeatSaleCheckDetail::getContactMobile, detail4refund.getContactMobile());
						updateWrapper.set(ReportSeatSaleCheckDetail::getTicketUser, detail4refund.getTicketUser());
						updateWrapper.set(ReportSeatSaleCheckDetail::getTransport, detail4refund.getTransport());
						updateWrapper.set(ReportSeatSaleCheckDetail::getTicketRemark, detail4refund.getTicketRemark());
						updateWrapper.set(ReportSeatSaleCheckDetail::getTicketDescription, detail4refund.getTicketDescription());
						updateWrapper.set(ReportSeatSaleCheckDetail::getTicketName, detail4refund.getTicketName());
						seatSaleCheckDetailService.update(null, updateWrapper);
					}
				}
				*/
			}
			for (ReportSeatSaleCheckDetail detail : recordList) {
				setFields(detail, detailMap4pay, detailMap4refund);
			}
			seatSaleCheckDetailService.saveOrUpdateBatch(recordList);
			dbLogger.warn("座票销售明细表刷新历史数据新增字段！{} {}", page.getCurrent(), recordList.size());
			if (recordList.size() < pageSize) {
				return;
			}
		}
	}

	private void setFields(ReportSeatSaleCheckDetail detail, Map<String, ReportSeatSaleCheckDetail> detailMap4pay, Map<String, ReportSeatSaleCheckDetail> detailMap4refund) {
		if(StringUtils.equals("pay", detail.getPayType())) {
			setFields4pay(detail, detailMap4pay);
		} else if(StringUtils.equals("refund", detail.getPayType())) {
			setFields4refund(detail, detailMap4refund);
		}
	}

	private void setFields4refund(ReportSeatSaleCheckDetail detail, Map<String, ReportSeatSaleCheckDetail> detailMap4refund) {
		ReportSeatSaleCheckDetail detail4refund = detailMap4refund.get(detail.getTradeNo() + "#" + detail.getUuid());
		if (detail4refund != null) {
			detail.setPrintNum(detail4refund.getPrintNum());
			detail.setPrintTime(detail4refund.getPrintTime());
			detail.setOrderStatus(detail4refund.getOrderStatus());
			detail.setContactMobile(detail4refund.getContactMobile());
			detail.setTicketUser(detail4refund.getTicketUser());
			detail.setTransport(detail4refund.getTransport());
			detail.setTicketRemark(detail4refund.getTicketRemark());
			detail.setTicketDescription(detail4refund.getTicketDescription());
			detail.setTicketName(detail4refund.getTicketName());
		}
	}

	private void setFields4pay(ReportSeatSaleCheckDetail detail, Map<String, ReportSeatSaleCheckDetail> detailMap4pay) {
		ReportSeatSaleCheckDetail detail4pay = detailMap4pay.get(detail.getTradeNo() + "#" + detail.getUuid());
		if (detail4pay != null) {
			detail.setTicketRemark(detail4pay.getTicketRemark());
			detail.setTicketDescription(detail4pay.getTicketDescription());
			detail.setTicketName(detail4pay.getTicketName());
		}
	}

	@Override
	public void updateSeatSaleCheckDetailStatus() {
		int pageSize = 500;
		for (int i = 1; i < 100000; i++) {
			Page<ReportSeatSaleCheckDetail> page = new Page<>(i, pageSize);
			Page<ReportSeatSaleCheckDetail> detailPage = seatSaleCheckDetailService.pagingQueryId(page);
			List<ReportSeatSaleCheckDetail> recordList = detailPage.getRecords();
			if (CollectionUtils.isEmpty(recordList)) {
				return;
			}
			Map<String, List<ReportSeatSaleCheckDetail>> payTypeListMap = BeanUtil.groupBeanList(recordList, "payType");
			List<ReportSeatSaleCheckDetail> payList = payTypeListMap.get("pay");
			List<ReportSeatSaleCheckDetail> refundList = payTypeListMap.get("refund");

			List<ReportSeatSaleCheckDetail> detailList4pay = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(payList)) {
				List<Long> seatDetailIdList = payList.stream().map(e -> Long.valueOf(e.getId().substring(4))).collect(Collectors.toList());
				detailList4pay = seatSaleCheckDetailService.getSeatCountByIdList(seatDetailIdList);
				seatSaleCheckDetailService.updateBatchByIdList4pay(detailList4pay);
			}

			List<ReportSeatSaleCheckDetail> detailList4refund = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(refundList)) {
				List<Long> orderRefundDetailIdList = refundList.stream().map(e -> Long.valueOf(e.getId().substring(6))).collect(Collectors.toList());
				detailList4refund = seatSaleCheckDetailService.getRefundSeatCountByIdList(orderRefundDetailIdList);
				seatSaleCheckDetailService.updateBatchByIdList4refund(detailList4refund);
			}

			dbLogger.warn("座票销售明细表刷新票状态字段！{} {} {} {}", page.getCurrent(), recordList.size(), detailList4pay.size(), detailList4refund.size());
		}
	}
}

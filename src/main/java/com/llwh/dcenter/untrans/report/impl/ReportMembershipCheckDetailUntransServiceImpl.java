package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.constant.IdCardConst;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.MembershipCheckDetailSearch;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportMembershipCheckDetail;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportMembershipCheckDetailService;
import com.llwh.dcenter.untrans.report.ReportMembershipCheckDetailUntransService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportMembershipCheckDetailUntransServiceImpl implements ReportMembershipCheckDetailUntransService {
	@Autowired
	private ReportMembershipCheckDetailService membershipCheckDetailService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	private static final String UK = "ReportMemberShipCheckDetail";
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());
	@Override
	public void updateReportMembershipCheckDetailJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			startTime = DateUtil.addMinute(record.getLastUpdateTime(), ReportConstant.UPDATE_REPEAT_MINUTE);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = DateUtil.getCurTruncTimestamp();
		}
		try {
			updateReportMembershipCheckDetailCount(startTime, endTime);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateReportMembershipCheckDetailCount(Timestamp startTime, Timestamp endTime) {
		List<ReportMembershipCheckDetail> checkDetailCount = new ArrayList<>();
		checkDetailCount.addAll(membershipCheckDetailService.getCheckDetailCount(startTime, endTime));
		checkDetailCount.addAll(membershipCheckDetailService.getCheckDetailCountOffline(startTime, endTime));
		checkDetailCount.forEach(count ->{
				count.setCertificateType(IdCardConst.idCardMap.get(count.getCertificateType()));
		});
		membershipCheckDetailService.saveOrUpdateBatch(checkDetailCount);
		dbLogger.warn("刷新会员卡入场明细表：{}",checkDetailCount.size());
	}

	@Override
	public ResultCode<Page<ReportMembershipCheckDetail>> getCounts(AuthUser user, Page<ReportMembershipCheckDetail> page,
	                                                               MembershipCheckDetailSearch search) {
		if (search.getDatefrom() == null || search.getDateto() == null) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		QueryWrapper<ReportMembershipCheckDetail> wrapper = new QueryWrapper<>();
		wrapper.ge("check_time", search.getDatefrom());
		wrapper.lt("check_time", search.getDateto());
		wrapper.eq("company_id", user.getCompanyId());
		wrapper.orderByAsc("check_time");
		if (StringUtils.isNotBlank(search.getMemberCardTypeId())) {
			wrapper.eq("membership_card_id", search.getMemberCardTypeId());
		}
		if (StringUtils.isNotBlank(search.getMobile())) {
			wrapper.eq("mobile", search.getMobile());
		}
		if (StringUtils.isNotBlank(search.getMembershipCardType())) {
			wrapper.like("membership_card_type", "%" +  search.getMembershipCardType() + "%");
		}
		if (StringUtils.isNotBlank(search.getCardNo())) {
			wrapper.eq("card_no", search.getCardNo());
		}
		if (StringUtils.isNotBlank(search.getTradeNo())) {
			wrapper.eq("trade_no", search.getTradeNo());
		}
		if (search.getMemberId() != null) {
			wrapper.eq("member_id", search.getMemberId());
		}
		if (search.getTbsUserId() != null) {
			wrapper.eq("tbs_user_id", search.getTbsUserId());
		}
		if (search.getCheckUserGroupId() != null) {
			wrapper.eq("check_user_group_id", search.getCheckUserGroupId());
		}
		if (CollectionUtils.isNotEmpty(search.getMembershipCardIdList())){
			wrapper.in("membership_card_id", search.getMembershipCardIdList());
		}
		Page<ReportMembershipCheckDetail> pageResult = membershipCheckDetailService.page(page,wrapper);
		return ResultCode.getSuccessReturn(pageResult);
	}

	@Override
	public List<List<Object>> getTotalCount(Date datefrom, Date dateto, String membershipCardTypeId, AuthUser user,Page page) {
		List<List<Object>> resultList = new ArrayList<>();
		if (datefrom == null || dateto == null) {
			return resultList;
		}
		Long companyId = user.getCompanyId();
		dateto = DateUtil.addDay(dateto, 1);
		List<ReportMembershipCheckDetail> tempList = membershipCheckDetailService.getTotalCount(datefrom, dateto, membershipCardTypeId,companyId,page);
		for (ReportMembershipCheckDetail detail : tempList) {
			List<Object> objList = new ArrayList<>();
			objList.add(detail.getCardNo());
			objList.add(detail.getTradeNo());
			objList.add(detail.getMembershipCardType());
			objList.add(detail.getPrice());
			objList.add(detail.getUserGroupName());
			objList.add(detail.getCheckUserGroupName());
			objList.add(detail.getCheckNo());
			objList.add(detail.getRealName());
			objList.add(detail.getCertificateType());
			objList.add(detail.getCertificateNo());
			objList.add(detail.getCheckTime());
			resultList.add(objList);
		}
		return resultList;
	}
}

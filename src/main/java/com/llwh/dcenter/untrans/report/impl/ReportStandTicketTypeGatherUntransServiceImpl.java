package com.llwh.dcenter.untrans.report.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.BeanUtil;

import com.llwh.dcenter.helper.StandTicketTypeCountSearch;
import com.llwh.dcenter.service.report.ReportStandTicketTypeGatherService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.untrans.report.ReportStandTicketTypeGatherUntransService;
import com.llwh.dcenter.vo.common.ProgramVo;
import com.llwh.dcenter.vo.common.StadiumVo;
import com.llwh.dcenter.vo.common.TicketTypeVo;
import com.llwh.dcenter.vo.common.VenueVo;
import com.llwh.dcenter.vo.report.stand.StandTicketTypeGatherVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/5/30 15:06
 */
@Service
public class ReportStandTicketTypeGatherUntransServiceImpl implements ReportStandTicketTypeGatherUntransService {
	private String[] outputFields = new String[] {"ticketTypeId", "ticketTypeName", "ticketPrice", "quantity", "realPay", "settlementAmount"};
	@Autowired
	private DataLevelUntransService dataLevelUntransService;
	@Autowired
	private ReportStandTicketTypeGatherService standTicketTypeGatherService;
	@Autowired
	private CommonUntransService commonUntransService;

	@Override
	public ResultCode<Map> getCounts(AuthUser user, StandTicketTypeCountSearch search) {
		if(StringUtils.isBlank(search.getShowIds()) && search.getTicketTimeFrom() == null && search.getTicketTimeTo() == null){
			return ResultCodeHelper.CODE11_PARAMS_ERROR("请指定场次或者时间");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, true, true);
		if(StringUtils.isNotBlank(search.getShowIds())){
			String[] showIdArray = StringUtils.split(search.getShowIds(), ",");
			List<Long> showIdList = Arrays.stream(showIdArray).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
			search.setShowIdList(showIdList);
		}

		Map model = new HashMap<>(2);
		model.put("stadiumList", new ArrayList<>());
		Map totalMap = new HashMap();
		totalMap.put("quantity", 0);
		totalMap.put("amount", 0.0);
		totalMap.put("realPay", 0.0);
		totalMap.put("settlementAmount", 0.0);
		model.put("totalMap", totalMap);

		List<StandTicketTypeGatherVo> voList = standTicketTypeGatherService.getStandTicketTypeGather(user.getCompanyId(), search);
		if (CollectionUtils.isEmpty(voList)) {
			return ResultCode.getSuccessReturn(model);
		}

		StandTicketTypeGatherVo totalVo = standTicketTypeGatherService.getTotals(user.getCompanyId(), search);
		if(totalVo != null){
			totalMap.put("quantity", totalVo.getQuantity() != null ? totalVo.getQuantity() : 0);
			totalMap.put("amount", totalVo.getAmount() != null ? totalVo.getAmount() : 0.0);
			totalMap.put("realPay", totalVo.getRealPay() != null ? totalVo.getRealPay() : 0.0);
			totalMap.put("settlementAmount", totalVo.getSettlementAmount() != null ? totalVo.getSettlementAmount() : 0.0);
		}

		Map<Long, String> stadiumMap = getStadiumMap(voList);
		Map<Long, String> venueMap = getVenueMap(voList);
		Map<Long, String> programMap = getProgramMap(voList);
		Map<Long, String> ticketTypeMap = getTicketTypeMap(voList);

		setTicketTypeName(voList, ticketTypeMap);

		Map<String, List<StandTicketTypeGatherVo>> program2TicketTypeListMap = BeanUtil.groupBeanListByFun(voList, e -> e.getStadiumId() + "#" + e.getVenueId() + "#" + e.getProgramId(), "");
		List<Map> programList = new ArrayList<>(program2TicketTypeListMap.size());
		for (Map.Entry<String, List<StandTicketTypeGatherVo>> entry : program2TicketTypeListMap.entrySet()) {
			String key = entry.getKey();
			List<StandTicketTypeGatherVo> value = entry.getValue();
			String[] split = key.split("#");
			List<Map> ticketTypeList = BeanUtil.getBeanMapListWithKeys3(value, outputFields);
			Map program = new HashMap();
			program.put("stadiumId", Long.valueOf(split[0]));
			program.put("venueId", Long.valueOf(split[1]));
			program.put("programId", Long.valueOf(split[2]));
			program.put("ticketTypeList", ticketTypeList);
			program.put("programName", programMap.get(program.get("programId")));
			programList.add(program);
		}

		Map<String, List<Map>> venue2ProgramListMap = BeanUtil.groupBeanListByFun(programList, e -> e.get("stadiumId") + "#" + e.get("venueId"), "");
		List<Map> venueList = new ArrayList<>(venue2ProgramListMap.size());
		for (Map.Entry<String, List<Map>> entry : venue2ProgramListMap.entrySet()) {
			String key = entry.getKey();
			List<Map> value = entry.getValue();
			String[] split = key.split("#");
			Map venue = new HashMap();
			venue.put("stadiumId", Long.valueOf(split[0]));
			venue.put("venueId", Long.valueOf(split[1]));
			venue.put("programList", value);
			venue.put("venueName", venueMap.get(venue.get("venueId")));
			venueList.add(venue);
		}

		Map<String, List<Map>> stadium2VenueListMap = BeanUtil.groupBeanListByFun(venueList, e -> e.get("stadiumId") + "", "");
		List<Map> stadiumList = new ArrayList<>(stadium2VenueListMap.size());
		for (Map.Entry<String, List<Map>> entry : stadium2VenueListMap.entrySet()) {
			String key = entry.getKey();
			List<Map> value = entry.getValue();

			Map stadium = new HashMap();
			stadium.put("stadiumId", Long.valueOf(key));
			stadium.put("venueList", value);
			stadium.put("stadiumName", stadiumMap.get(stadium.get("stadiumId")));
			stadiumList.add(stadium);
		}

		model.put("stadiumList", stadiumList);
		return ResultCode.getSuccessReturn(model);
	}

	private void setTicketTypeName(List<StandTicketTypeGatherVo> voList, Map<Long, String> ticketTypeMap) {
		for (StandTicketTypeGatherVo vo : voList) {
			vo.setTicketTypeName(ticketTypeMap.get(vo.getTicketTypeId()));
		}
	}

	private Map<Long, String> getTicketTypeMap(List<StandTicketTypeGatherVo> voList) {
		List<Long> ticketTypeIdList = BeanUtil.getBeanPropertyList(voList, "ticketTypeId", true);
		List<List<Long>> partition = BeanUtil.partition(ticketTypeIdList, 500);
		List<TicketTypeVo> ticketTypeVoList = new ArrayList<>(ticketTypeIdList.size());
		for (List<Long> idsPart : partition) {
			ticketTypeVoList.addAll(commonUntransService.getTicketTypes(idsPart));
		}
		return BeanUtil.beanListToMap(ticketTypeVoList, "id", "ticketTypeName", true);
	}

	private Map<Long, String> getProgramMap(List<StandTicketTypeGatherVo> voList) {
		List<Long> programIdList = BeanUtil.getBeanPropertyList(voList, "programId", true);
		List<List<Long>> partition = BeanUtil.partition(programIdList, 500);
		List<ProgramVo> programVoList = new ArrayList<>(programIdList.size());
		for (List<Long> idsPart : partition) {
			programVoList.addAll(commonUntransService.getPrograms(idsPart));
		}
		return BeanUtil.beanListToMap(programVoList, "id", "programName", true);
	}

	private Map<Long, String> getVenueMap(List<StandTicketTypeGatherVo> voList) {
		List<Long> venueIdList = BeanUtil.getBeanPropertyList(voList, "venueId", true);
		List<List<Long>> partition = BeanUtil.partition(venueIdList, 500);
		List<VenueVo> venueVoList = new ArrayList<>(venueIdList.size());
		for (List<Long> idsPart : partition) {
			venueVoList.addAll(commonUntransService.getVenues(idsPart));
		}
		return BeanUtil.beanListToMap(venueVoList, "id", "venueName", true);
	}

	private Map<Long, String> getStadiumMap(List<StandTicketTypeGatherVo> voList) {
		List<Long> stadiumIdList = BeanUtil.getBeanPropertyList(voList, "stadiumId", true);
		List<List<Long>> partition = BeanUtil.partition(stadiumIdList, 500);
		List<StadiumVo> stadiumVoList = new ArrayList<>(stadiumIdList.size());
		for (List<Long> idsPart : partition) {
			stadiumVoList.addAll(commonUntransService.getStadiums(idsPart));
		}
		return BeanUtil.beanListToMap(stadiumVoList, "id", "stadiumName", true);
	}
}

package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;
import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearReportStandCheckGatherCount;
import com.llwh.dcenter.model.ReportSeatSaleGatherProgram;
import com.llwh.dcenter.vo.report.ReportSeatSaleProgramTotalVo;

public interface ReportSeatSaleGatherProgramUntransService {

	void updateSeatSaleGatherProgramJob();

	void updateSeatSaleGatherProgramCount(Timestamp startTime, Timestamp endTime);

	ResultCode<IPage<ReportSeatSaleGatherProgram>> getCounts(AuthUser user, Page<ReportSeatSaleGatherProgram> page, SearReportStandCheckGatherCount search);

	List<List<Object>> getTotalCount(AuthUser user, SearReportStandCheckGatherCount search);

	ResultCode<ReportSeatSaleProgramTotalVo> getTotals(AuthUser user, SearReportStandCheckGatherCount search);
}

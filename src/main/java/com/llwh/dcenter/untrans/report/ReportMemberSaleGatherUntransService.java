package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.model.ReportMemberSaleGather;

public interface ReportMemberSaleGatherUntransService {
		void updateReportMemberSaleGatherJob();

		void updateReportMemberSaleGatherCount(Timestamp startTime, Timestamp endTime);

		ResultCode<IPage<ReportMemberSaleGather>> getCounts(AuthUser user, Page<ReportMemberSaleGather> page, Timestamp datefrom, Timestamp dateto, String memberCardTypeId, Long userGroupId, String paymethod);

		List<List<Object>> getTotalCount(AuthUser user, Date datefrom, Date dateto, String memberCardTypeId,Page page);

		ResultCode<ReportMemberSaleGather> getTotals(AuthUser user, Timestamp datefrom, Timestamp dateto, String memberCardTypeId, Long userGroupId, String paymethod);
}

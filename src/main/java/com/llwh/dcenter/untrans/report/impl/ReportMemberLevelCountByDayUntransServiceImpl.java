package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.StringUtil;

import com.llwh.dcenter.helper.SearMemberInfoCountByDay;
import com.llwh.dcenter.mapper.ReportMemberLevelCountByDayMapper;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportMemberLevelCountByDay;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportMemberLevelCountByDayService;
import com.llwh.dcenter.untrans.report.ReportMemberLevelCountByDayUntransService;
import com.llwh.dcenter.vo.common.MemberLevelVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportMemberLevelCountByDayUntransServiceImpl implements ReportMemberLevelCountByDayUntransService {
	@Autowired
	private ReportMemberLevelCountByDayMapper reportMemberLevelCountByDayMapper;
	@Autowired
	private ReportMemberLevelCountByDayService reportMemberLevelCountByDayService;
	@Autowired
	private ReportCountUpdateRecordService reportCountUpdateRecordService;
	private static final String UK = "ReportMemberLevelCountByDay";
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());

	@Override
	public void updateMemberLevelCountByDayJob() {
		ReportCountUpdateRecord record = reportCountUpdateRecordService.getById(UK);
		Timestamp starttime = null;
		Timestamp endtime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			endtime = DateUtil.getEndTimestamp(record.getLastUpdateTime());
			starttime = DateUtil.getBeginTimestamp(endtime);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			endtime = DateUtil.getEndTimestamp(DateUtil.getCurrAddedDayTime(-1));
		}
		try {
			updateMemberLevelCountByDay(starttime, endtime);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		reportCountUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateMemberLevelCountByDay(Timestamp starttime, Timestamp endtime) {
		List<ReportMemberLevelCountByDay> list = new ArrayList<>();
		List<ReportMemberLevelCountByDay> pointList = new ArrayList<>();
		List<MemberLevelVo> levelList = reportMemberLevelCountByDayMapper.listAllLevel();
		List<MemberLevelVo> membershipLevels = reportMemberLevelCountByDayMapper.listAllMembershipLevel();
		List<ReportMemberLevelCountByDay> normalList = reportMemberLevelCountByDayService.getLevelCountByDay(endtime);
		List<ReportMemberLevelCountByDay> receivelist = reportMemberLevelCountByDayService.getPointReceiveCountByDay(starttime, endtime);
		pointList.addAll(receivelist);
		List<ReportMemberLevelCountByDay> spendlist = reportMemberLevelCountByDayService.getPointSpendCountByDay(starttime, endtime);
		pointList.addAll(spendlist);
		List<ReportMemberLevelCountByDay> totalList = reportMemberLevelCountByDayService.getPointLevelTotalByDay(endtime);

		Map<String, List<ReportMemberLevelCountByDay>> numMap = BeanUtil.groupBeanListByFun(normalList, count -> count.getMemberLevelId() +"-"+ count.getMembershipLevelId() +"-"+ count.getCompanyId(), "");
		Map<String, List<ReportMemberLevelCountByDay>> pointMap = BeanUtil.groupBeanListByFun(pointList, count -> count.getMemberLevelId() +"-"+ count.getMembershipLevelId() +"-"+ count.getCompanyId(), "");
		Map<String, List<ReportMemberLevelCountByDay>> totalPointMap = BeanUtil.groupBeanListByFun(totalList, count -> count.getMemberLevelId() +"-"+ count.getMembershipLevelId() +"-"+ count.getCompanyId(), "");

		List<Long> companyIds = BeanUtil.getBeanPropertyList(normalList, "companyId", true);
		Map<Long, List<MemberLevelVo>> membershipLevelMap = BeanUtil.groupBeanList(membershipLevels, "companyId");

		List<String> levelGroups = new ArrayList<>();
		//考虑某些企业只存在会员卡等级
		for(MemberLevelVo levelVo : membershipLevels){
			levelGroups.add("0-"+ levelVo.getId() + "-" + levelVo.getCompanyId());
		}
		//企业既存在会员等级也存在会员卡等级
		for(MemberLevelVo levelVo : levelList){
			levelGroups.add(levelVo.getId() +"-null-"+ levelVo.getCompanyId());
			List<MemberLevelVo> specialList = membershipLevelMap.get(levelVo.getCompanyId());
			if(CollectionUtils.isNotEmpty(specialList)){
				for (MemberLevelVo specialLevelVo : specialList){
					levelGroups.add(levelVo.getId() +"-"+ specialLevelVo.getId() +"-"+ levelVo.getCompanyId());
				}
			}
		}
		//考虑某些企业不存在等级
		for(Long companyId : companyIds){
			levelGroups.add("0-null-" + companyId);
		}


		Timestamp now = DateUtil.getCurFullTimestamp();
		for(String levelGroup : levelGroups){
			String[] levelArr = StringUtils.split(levelGroup, "-");
			ReportMemberLevelCountByDay num;
			List<ReportMemberLevelCountByDay> nums = numMap.get(levelGroup);
			List<ReportMemberLevelCountByDay> points = pointMap.get(levelGroup);
			List<ReportMemberLevelCountByDay> totalPoints = totalPointMap.get(levelGroup);
			if(CollectionUtils.isEmpty(nums)){
				num = new ReportMemberLevelCountByDay();
				num.setMemberLevelId(Long.parseLong(levelArr[0]));
				num.setMembershipLevelId(StringUtils.equals(levelArr[1], "null") ? null : Long.parseLong(levelArr[1]));
				num.setCompanyId(Long.parseLong(levelArr[2]));
			}else{
				num = nums.get(0);
			}
			if(CollectionUtils.isNotEmpty(points)){
				for(ReportMemberLevelCountByDay point : points){
					if(point.getReceivePointTotal() != null){
						num.setReceivePointTotal(point.getReceivePointTotal());
					}
					if(point.getSpendPointTotal() != null){
						num.setSpendPointTotal(point.getSpendPointTotal());
					}
				}
			}
			if(CollectionUtils.isNotEmpty(totalPoints)){
				num.setPointTotal(totalPoints.get(0).getPointTotal());
			}
			String id = endtime + "-" + levelGroup;
			num.setId(StringUtil.md5(id));
			num.setCountYear(DateUtil.getYear(endtime));
			num.setCountMonth(DateUtil.getMonth(endtime));
			num.setCountDate(endtime);
			num.setAddtime(now);
			num.setUpdatetime(now);
			if(num.getDayNum() == null){
				num.setDayNum(0);
			}
			if(num.getReceivePointTotal() == null){
				num.setReceivePointTotal(0);
			}
			if(num.getSpendPointTotal() == null){
				num.setSpendPointTotal(0);
			}
			if(num.getPointTotal() == null){
				num.setPointTotal(0L);
			}
			if(num.getMembershipLevelId() != null){
				num.setCountType("special");
			}else{
				num.setCountType("normal");
			}
			list.add(num);
		}
		reportMemberLevelCountByDayService.saveOrUpdateBatch(list);
		dbLogger.warn("刷新会员各等级积分人数统计（按天）：{}, 共{}", endtime, list.size());
	}


	@Override
	public ResultCode<List<ReportMemberLevelCountByDay>> getCounts(AuthUser user, SearMemberInfoCountByDay search) {
		if (search.getStartTime() == null || search.getEndTime() == null) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("请输入查询日期");
		}
		search.setCompanyId(user.getCompanyId());
		search.setStartTime(DateUtil.getMonthFirstDay(search.getStartTime()));
		search.setEndTime(DateUtil.getEndTimestamp(DateUtil.getMonthLastDay(search.getEndTime())));
		List<ReportMemberLevelCountByDay> list = reportMemberLevelCountByDayService.listMemberInfoCount(search);
		return ResultCode.getSuccessReturn(list);
	}



}

package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearReportSeatSaleGatherVenueArea;
import com.llwh.dcenter.model.ReportSeatSaleGatherVenueArea;

public interface ReportSeatSaleGatherVenueAreaUntransService {

	void updateSeatSaleGatherVenueAreaJob();

	void updateSeatSaleGatherVenueAreaCount(Timestamp startTime, Timestamp endTime);

	ResultCode<Page<ReportSeatSaleGatherVenueArea>> getCounts(AuthUser user, SearReportSeatSaleGatherVenueArea search, Page<ReportSeatSaleGatherVenueArea> page);
}

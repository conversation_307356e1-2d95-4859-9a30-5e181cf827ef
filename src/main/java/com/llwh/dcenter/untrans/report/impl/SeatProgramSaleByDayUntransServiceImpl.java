package com.llwh.dcenter.untrans.report.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.support.Sort;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.ValueUtil;

import com.llwh.dcenter.helper.ProgramStatisticsSearch;
import com.llwh.dcenter.service.report.SeatProgramSaleByDayService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.untrans.report.SeatProgramSaleByDayUntransService;
import com.llwh.dcenter.vo.common.ProgramSettleVo;
import com.llwh.dcenter.vo.common.ScheduleVo;
import com.llwh.dcenter.vo.programStatistics.ProgramSaleByDayVo;
import com.llwh.dcenter.vo.programStatistics.ProgramSaleBySellTypeVo;
import com.llwh.dcenter.vo.programStatistics.ProgramStatisticsVo;
import com.llwh.dcenter.vo.programStatistics.ScheduleStatisticsVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SeatProgramSaleByDayUntransServiceImpl implements SeatProgramSaleByDayUntransService {

	@Autowired
	private SeatProgramSaleByDayService seatProgramSaleByDayService;
	@Autowired
	private CommonUntransService commonUntransService;
	@Autowired
	private DataLevelUntransService dataLevelUntransService;

	@Override
	public ResultCode<Map> getCounts(AuthUser user, ProgramStatisticsSearch search) {
		if(StringUtils.isBlank(search.getScheduleIds()) && search.getTicketDateFrom() == null && search.getTicketDateTo() == null){
			return ResultCodeHelper.CODE11_PARAMS_ERROR("请指定场次或者时间");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, false, true);
		if(StringUtils.isNotBlank(search.getScheduleIds())){
			String[] scheduleIds = StringUtils.split(search.getScheduleIds(), ",");
			List<Long> scheduleIdList = Arrays.stream(scheduleIds).map(s->Long.parseLong(s.trim())).collect(Collectors.toList());
			search.setScheduleIdList(scheduleIdList);
		}
		Map model = new HashMap<>(2);
		model.put("programList", new ArrayList<>());
		Map totalMap = new HashMap();
		totalMap.put("quantity", 0);
		totalMap.put("amount", 0.0);
		totalMap.put("paidAmount", 0.0);
		totalMap.put("settlementAmount", 0.0);
		model.put("totalMap", totalMap);
		ResultCode<List<ProgramSaleBySellTypeVo>> code = seatProgramSaleByDayService.getSaleDetailsBySellType(user.getCompanyId(), search);
		if(code.notSuccess()){
			return ResultCode.getSuccessReturn(model);
		}
		ResultCode<ProgramSaleBySellTypeVo> totalCode = seatProgramSaleByDayService.getTotals(user.getCompanyId(), search);
		if(totalCode.isSuccess()){
			ProgramSaleBySellTypeVo total = totalCode.getData();
			if(total != null){
				totalMap.put("quantity", total.getQuantity() != null ? total.getQuantity() : 0);
				totalMap.put("amount", total.getAmount() != null ? ValueUtil.roundDouble(total.getAmount()) : 0.0);
				totalMap.put("paidAmount", total.getPaidAmount() != null ? ValueUtil.roundDouble(total.getPaidAmount()) : 0.0);
				totalMap.put("settlementAmount", total.getSettlementAmount() != null ? ValueUtil.roundDouble(total.getSettlementAmount()) : 0.0);
			}
		}
		Double balanceTotal = seatProgramSaleByDayService.getBalanceTotalAmount(user.getCompanyId(), search);
		model.put("balanceTotal", balanceTotal == null ? 0.00 : ValueUtil.roundDouble(balanceTotal));

		List<ProgramSaleBySellTypeVo> dataList = code.getData();
		if(CollectionUtils.isEmpty(dataList)){
			return ResultCode.getSuccessReturn(model);
		}
		List<Long> scheduleIds = BeanUtil.getBeanPropertyList(dataList, "scheduleId", true);
		List<Long> programIds = BeanUtil.getBeanPropertyList(dataList, "programId", true);
		Map<Long, List<ProgramSaleBySellTypeVo>> programListMap = BeanUtil.groupBeanList(dataList, "programId");
		Map<Long, List<String>> programSellTypeCodeMap = new HashMap<>(programListMap.size());
		for(Entry<Long, List<ProgramSaleBySellTypeVo>> entry : programListMap.entrySet()){
			Long programId = entry.getKey();
			List<ProgramSaleBySellTypeVo> vos = entry.getValue();
			List<String> sellTypes = BeanUtil.getBeanPropertyList(vos, "sellType", true);
			Collections.sort(sellTypes);
			programSellTypeCodeMap.put(programId, sellTypes);
		}

		List<ScheduleVo> schedules = commonUntransService.getSchedules(scheduleIds);
		List<ProgramSettleVo> programVos = commonUntransService.listProgramStadium(programIds);
		Map<Long, ScheduleVo> scheduleVoMap = BeanUtil.beanListToMap(schedules, "id");
		Map<Long, ProgramSettleVo> programVoMap = BeanUtil.beanListToMap(programVos, "programId");
		Map<String, String> sellTypeMap = commonUntransService.getAllSellType(user.getCompanyId());
		Map<String, List<ProgramSaleBySellTypeVo>> scheduleMap = BeanUtil.groupBeanListByFun(dataList, count -> {
			return count.getScheduleId() + "-" + count.getProgramId();
		}, "");
		List<ScheduleStatisticsVo> scheduleStatisticsVos = new ArrayList<>(scheduleMap.size());
		for(Entry<String, List<ProgramSaleBySellTypeVo>> scheduleEntry: scheduleMap.entrySet()){
			String scheduleKey = scheduleEntry.getKey();
			String[] scheduleArr = StringUtils.split(scheduleKey, "-");
			Long scheduleId = Long.valueOf(scheduleArr[0]);
			Long programId = Long.valueOf(scheduleArr[1]);
			ScheduleStatisticsVo vo = new ScheduleStatisticsVo();
			vo.setProgramId(programId);
			vo.setScheduleId(scheduleId);
			ScheduleVo scheduleVo = scheduleVoMap.get(scheduleId);
			if(scheduleVo != null){
				vo.setPlayTime(scheduleVo.getPlayTime());
				vo.setScheduleName(scheduleVo.getShowName());
			}
			List<ProgramSaleBySellTypeVo> scheduleList = scheduleEntry.getValue();
			Map<Date, List<ProgramSaleBySellTypeVo>> ticketDateMap = BeanUtil.groupBeanList(scheduleList, "ticketDate");
			List<ProgramSaleByDayVo> dayVoList = new ArrayList<>(ticketDateMap.size());

			Integer scheduleTotal = 0;
			Double scheduleAmount = 0.0;
			Double schedulePaidAmount = 0.0;
			Double scheduleSettlementAmount = 0.0;
			List<String> programSellTypeList = programSellTypeCodeMap.get(programId);
			LinkedHashMap<String, Integer> sellTypeTotalMap = new LinkedHashMap<>(programSellTypeList.size());
			LinkedHashMap<String, String> scheduleSellTypeMap = new LinkedHashMap<>(programSellTypeList.size());
			for(String sellTypeCode: programSellTypeList){
				sellTypeTotalMap.put(sellTypeMap.get(sellTypeCode), 0);
				scheduleSellTypeMap.put(sellTypeCode, sellTypeMap.get(sellTypeCode));
			}
			for(Entry<Date, List<ProgramSaleBySellTypeVo>> dateEntry : ticketDateMap.entrySet()){
				ProgramSaleByDayVo dayVo = new ProgramSaleByDayVo();
				dayVo.setTicketDate(DateUtil.formatDate(dateEntry.getKey()));
				List<ProgramSaleBySellTypeVo> sellTypeVos = dateEntry.getValue();
				Integer sellTypeTotal = 0;
				Double totalAmount = 0.0;
				Double totalPaidAmount = 0.0;
				Double totalSettlementAmount = 0.0;
				Map<String, ProgramSaleBySellTypeVo> sellTypeVoMap = BeanUtil.beanListToMap(sellTypeVos, "sellType");

				LinkedHashMap map = new LinkedHashMap<>(scheduleSellTypeMap.size());
				for(Entry<String, String> sellTypeEntry: scheduleSellTypeMap.entrySet()){
					String sellTypeCode = sellTypeEntry.getKey();
					String sellTypeName = sellTypeEntry.getValue();
					ProgramSaleBySellTypeVo sellTypeVo = sellTypeVoMap.get(sellTypeCode);
					if(sellTypeVo != null){
						sellTypeTotal += sellTypeVo.getQuantity();
						totalAmount = ValueUtil.roundDouble(totalAmount + sellTypeVo.getAmount());
						totalPaidAmount = ValueUtil.roundDouble(totalPaidAmount + sellTypeVo.getPaidAmount());
						totalSettlementAmount = ValueUtil.roundDouble(totalSettlementAmount + sellTypeVo.getSettlementAmount());
						map.put(sellTypeName, sellTypeVo.getQuantity());
						sellTypeTotalMap.put(sellTypeName, sellTypeTotalMap.get(sellTypeName) + sellTypeVo.getQuantity());
					}else{
						map.put(sellTypeName, 0);
					}
				}

				map.put("总数", sellTypeTotal);
				dayVo.setSellTypeMap(map);
				dayVo.setTotalAmount(totalAmount);
				dayVo.setTotalPaidAmount(totalPaidAmount);
				dayVo.setTotalSettlementAmount(totalSettlementAmount);
				dayVoList.add(dayVo);
				scheduleTotal += sellTypeTotal;
				scheduleAmount = ValueUtil.roundDouble(totalAmount + scheduleAmount);
				schedulePaidAmount = ValueUtil.roundDouble(totalPaidAmount + schedulePaidAmount);
				scheduleSettlementAmount = ValueUtil.roundDouble(totalSettlementAmount + scheduleSettlementAmount);
			}
			ProgramSaleByDayVo subTotalVo = new ProgramSaleByDayVo();
			subTotalVo.setTicketDate("小计");
			sellTypeTotalMap.put("总数", scheduleTotal);
			subTotalVo.setSellTypeMap(sellTypeTotalMap);
			subTotalVo.setTotalAmount(scheduleAmount);
			subTotalVo.setTotalPaidAmount(schedulePaidAmount);
			subTotalVo.setTotalSettlementAmount(scheduleSettlementAmount);
			dayVoList.add(subTotalVo);
			dayVoList.sort(Sort.asc("ticketDate"));
			vo.setDataList(dayVoList);
			scheduleStatisticsVos.add(vo);
		}

		Map<Long, List<ScheduleStatisticsVo>> programMap = BeanUtil.groupBeanList(scheduleStatisticsVos, "programId");
		List<ProgramStatisticsVo> programStatisticsVos = new ArrayList<>(programMap.size());
		for(Entry<Long, List<ScheduleStatisticsVo>> programEntry : programMap.entrySet()){
			Long programId = programEntry.getKey();
			List<ScheduleStatisticsVo> scheduleList = programEntry.getValue();
			scheduleList.sort(Sort.asc("playTime"));
			ProgramStatisticsVo programStatisticsVo = new ProgramStatisticsVo();
			programStatisticsVo.setProgramId(programId);
			ProgramSettleVo programVo = programVoMap.get(programId);
			if(programVo != null){
				programStatisticsVo.setProgramName(programVo.getProgramName());
				programStatisticsVo.setStadiumName(programVo.getStadiumName());
				programStatisticsVo.setVenueName(programVo.getVenueName());
			}
			programStatisticsVo.setScheduleList(scheduleList);
			List<ProgramSaleByDayVo> programDataList = new ArrayList<>(dataList.size());
			for(ScheduleStatisticsVo vo : scheduleList){
				List<ProgramSaleByDayVo> scheduleDataList = vo.getDataList();
				Map<String, ProgramSaleByDayVo> scheduleData = BeanUtil.beanListToMap(scheduleDataList, "ticketDate");
				programDataList.add(scheduleData.get("小计"));
			}
			ProgramSaleByDayVo sumProgram = new ProgramSaleByDayVo();
			sumProgram.setTicketDate("合计");
			sumProgram.setTotalAmount(0.0);
			sumProgram.setTotalPaidAmount(0.0);
			sumProgram.setTotalSettlementAmount(0.0);
			List<String> programSellTypeList = programSellTypeCodeMap.get(programId);
			LinkedHashMap<String, Integer> programSellTypeMap = new LinkedHashMap<>(programSellTypeList.size() + 1);
			for(String sellTypeCode: programSellTypeList){
				programSellTypeMap.put(sellTypeMap.get(sellTypeCode), 0);
			}
			programSellTypeMap.put("总数", 0);
			for(ProgramSaleByDayVo vo : programDataList){
				Map<String, Integer> scheduleSellTypeMap = vo.getSellTypeMap();
				for(Entry<String, Integer> entry : scheduleSellTypeMap.entrySet()){
					programSellTypeMap.put(entry.getKey(), programSellTypeMap.get(entry.getKey()) + entry.getValue());
				}
				sumProgram.setTotalAmount(ValueUtil.roundDouble(sumProgram.getTotalAmount() + vo.getTotalAmount()));
				sumProgram.setTotalPaidAmount(ValueUtil.roundDouble(sumProgram.getTotalPaidAmount() + vo.getTotalPaidAmount()));
				sumProgram.setTotalSettlementAmount(ValueUtil.roundDouble(sumProgram.getTotalSettlementAmount() + vo.getTotalSettlementAmount()));
			}
			sumProgram.setSellTypeMap(programSellTypeMap);
			programStatisticsVo.setSumMap(BeanUtil.getBeanMap(sumProgram));
			programStatisticsVos.add(programStatisticsVo);
		}
		model.put("programList", programStatisticsVos);
		return ResultCode.getSuccessReturn(model);
	}

}

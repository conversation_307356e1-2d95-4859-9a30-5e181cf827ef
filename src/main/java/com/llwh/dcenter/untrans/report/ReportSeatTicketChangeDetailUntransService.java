package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.ReportSeatTicketChangeDetailSearch;
import com.llwh.dcenter.model.ReportSeatTicketChangeDetail;

/**
 * <AUTHOR>
 */
public interface ReportSeatTicketChangeDetailUntransService {
	void updateJob();

	void updateCount(Timestamp startTime, Timestamp endTime);

	ResultCode<Page<ReportSeatTicketChangeDetail>> getCountsBySql(AuthUser user, ReportSeatTicketChangeDetailSearch search, Page<ReportSeatTicketChangeDetail> page);
}

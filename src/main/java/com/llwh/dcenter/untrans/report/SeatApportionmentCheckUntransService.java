package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.model.DiscountApportionmentCheck;

public interface SeatApportionmentCheckUntransService {

	void updateSeatApportionmentCheckJob();

	void updateSeatApportionmentCheck(Timestamp startTime, Timestamp endTime);

	ResultCode<Page<DiscountApportionmentCheck>> getCounts(AuthUser user, SearReportStandCheckDetailCount search, Page<DiscountApportionmentCheck> page);

}

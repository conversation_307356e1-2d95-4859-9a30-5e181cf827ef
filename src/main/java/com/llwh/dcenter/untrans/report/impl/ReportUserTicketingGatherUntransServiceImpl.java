package com.llwh.dcenter.untrans.report.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.support.Sort;
import cn.fancylab.util.BeanUtil;

import com.llwh.dcenter.helper.ReportUserTicketingGatherSearch;
import com.llwh.dcenter.service.report.ReportUserTicketingGatherService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.untrans.report.ReportUserTicketingGatherUntransService;
import com.llwh.dcenter.vo.common.ProgramSettleVo;
import com.llwh.dcenter.vo.common.ScheduleVo;
import com.llwh.dcenter.vo.common.UserGroupVo;
import com.llwh.dcenter.vo.report.ReportMemberSaleDetailVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/6/6 17:03
 */
@Service
public class ReportUserTicketingGatherUntransServiceImpl implements ReportUserTicketingGatherUntransService {
	@Autowired
	private DataLevelUntransService dataLevelUntransService;
	@Autowired
	private ReportUserTicketingGatherService userTicketingGatherService;
	@Autowired
	private CommonUntransService commonUntransService;

	@Override
	public ResultCode<Map> getCounts(AuthUser user, ReportUserTicketingGatherSearch search) {
		if(StringUtils.isBlank(search.getScheduleIds()) && search.getTicketDateFrom() == null && search.getTicketDateTo() == null){
			return ResultCodeHelper.CODE11_PARAMS_ERROR("请指定场次或者时间");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, true, true);
		if(StringUtils.isNotBlank(search.getScheduleIds())){
			String[] scheduleIds = StringUtils.split(search.getScheduleIds(), ",");
			List<Long> scheduleIdList = Arrays.stream(scheduleIds).map(s->Long.parseLong(s.trim())).collect(Collectors.toList());
			search.setScheduleIdList(scheduleIdList);
		}

		Map<String, Object> model = new HashMap<>(2);
		model.put("userGroupList", new ArrayList<>());
		Map<String, Integer> totalMap = new HashMap();
		totalMap.put("totalQuantity", 0);
		totalMap.put("ticketingType1Quantity", 0);
		totalMap.put("ticketingType2Quantity", 0);
		totalMap.put("ticketingType3Quantity", 0);
		totalMap.put("ticketingType4Quantity", 0);
		totalMap.put("totalOrderQuantity", 0);
		totalMap.put("ticketingType1OrderQuantity", 0);
		totalMap.put("ticketingType2OrderQuantity", 0);
		totalMap.put("ticketingType3OrderQuantity", 0);
		totalMap.put("ticketingType4OrderQuantity", 0);
		model.put("totalMap", totalMap);

		List<Map<String, Object>> counts = userTicketingGatherService.getCounts(user.getCompanyId(), search);
		if (CollectionUtils.isEmpty(counts)) {
			return ResultCode.getSuccessReturn(model);
		}

		List<Map<String, Object>> totals = userTicketingGatherService.getTotals(user.getCompanyId(), search);
		if (CollectionUtils.isNotEmpty(totals)) {
			int ticketingType1Quantity = MapUtils.getIntValue(totalMap, "ticketingType1Quantity", 0);
			int ticketingType2Quantity = MapUtils.getIntValue(totalMap, "ticketingType2Quantity", 0);
			int ticketingType3Quantity = MapUtils.getIntValue(totalMap, "ticketingType3Quantity", 0);
			int ticketingType4Quantity = MapUtils.getIntValue(totalMap, "ticketingType4Quantity", 0);
			int totalQuantity = MapUtils.getIntValue(totalMap, "totalQuantity", 0);
			int ticketingType1OrderQuantity = MapUtils.getIntValue(totalMap, "ticketingType1OrderQuantity", 0);
			int ticketingType2OrderQuantity = MapUtils.getIntValue(totalMap, "ticketingType2OrderQuantity", 0);
			int ticketingType3OrderQuantity = MapUtils.getIntValue(totalMap, "ticketingType3OrderQuantity", 0);
			int ticketingType4OrderQuantity = MapUtils.getIntValue(totalMap, "ticketingType4OrderQuantity", 0);
			int totalOrderQuantity = MapUtils.getIntValue(totalMap, "totalOrderQuantity", 0);
			for (Map<String, Object> tMap : totals) {
				String ticketingType = MapUtils.getString(tMap, "ticketingType");
				int quantity = MapUtils.getIntValue(tMap, "quantity", 0);
				int orderQuantity = MapUtils.getIntValue(tMap, "orderQuantity", 0);
				if (StringUtils.equals("ticketingType1", ticketingType)) {
					ticketingType1Quantity += quantity;
					ticketingType1OrderQuantity += orderQuantity;
				} else if (StringUtils.equals("ticketingType2", ticketingType)) {
					ticketingType2Quantity += quantity;
					ticketingType2OrderQuantity += orderQuantity;
				} else if (StringUtils.equals("ticketingType3", ticketingType)) {
					ticketingType3Quantity += quantity;
					ticketingType3OrderQuantity += orderQuantity;
				} else if (StringUtils.equals("ticketingType4", ticketingType)) {
					ticketingType4Quantity += quantity;
					ticketingType4OrderQuantity += orderQuantity;
				}
				totalQuantity += quantity;
				totalOrderQuantity += orderQuantity;
			}
			totalMap.put("totalQuantity", totalQuantity);
			totalMap.put("ticketingType1Quantity", ticketingType1Quantity);
			totalMap.put("ticketingType2Quantity", ticketingType2Quantity);
			totalMap.put("ticketingType3Quantity", ticketingType3Quantity);
			totalMap.put("ticketingType4Quantity", ticketingType4Quantity);
			totalMap.put("totalOrderQuantity", totalOrderQuantity);
			totalMap.put("ticketingType1OrderQuantity", ticketingType1OrderQuantity);
			totalMap.put("ticketingType2OrderQuantity", ticketingType2OrderQuantity);
			totalMap.put("ticketingType3OrderQuantity", ticketingType3OrderQuantity);
			totalMap.put("ticketingType4OrderQuantity", ticketingType4OrderQuantity);
		}

		Map<Long, String> userGroupNameMap = getUserGroupMap(counts);
		Map<Long, String> userNameMap = getUserMap(counts);
		List<Long> programIds = BeanUtil.getBeanPropertyList(counts, "programId", true);
		List<ProgramSettleVo> programVos = commonUntransService.listProgramStadium(programIds);
		Map<Long, ProgramSettleVo> programVoMap = BeanUtil.beanListToMap(programVos, "programId");

		List<Long> scheduleIds = BeanUtil.getBeanPropertyList(counts, "scheduleId", true);
		List<ScheduleVo> schedules = commonUntransService.getSchedules(scheduleIds);
		Map<Long, ScheduleVo> scheduleVoMap = BeanUtil.beanListToMap(schedules, "id");
		// 出票类型
//		Map<String, String> ticketingTypeMap = new HashMap<>(4) {{
//			put("ticketingType1", "纸质票（已打印）");
//			put("ticketingType2", "纸质票（未打印）");
//			put("ticketingType3", "电子票（已换票）");
//			put("ticketingType4", "电子票（未换票）");
//		}};

		// 场次统计
		Map<String, List<Map<String, Object>>> schedule2ticketingTypeListMap = BeanUtil.groupBeanListByFun(counts,
				e -> e.get("userGroupId") + "#" + e.get("addUserId") + "#" + e.get("programId") + "#" + e.get("scheduleId"), "");
		List<Map<String, Object>> scheduleList = new ArrayList<>(schedule2ticketingTypeListMap.size());
		for (Map.Entry<String, List<Map<String, Object>>> entry : schedule2ticketingTypeListMap.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> valueList = entry.getValue();

			String[] split = key.split("#");
			Long userGroupId = Long.valueOf(split[0]);
			Long addUserId = Long.valueOf(split[1]);
			Long programId = Long.valueOf(split[2]);
			Long scheduleId = Long.valueOf(split[3]);
			HashMap scheduleMap = new HashMap<>();
			scheduleMap.put("userGroupId", userGroupId);
			scheduleMap.put("addUserId", addUserId);
			scheduleMap.put("programId", programId);
			scheduleMap.put("scheduleId", scheduleId);
			ScheduleVo scheduleVo = scheduleVoMap.get(scheduleId);
			if(scheduleVo != null){
				scheduleMap.put("playTime", scheduleVo.getPlayTime());
				scheduleMap.put("scheduleName", scheduleVo.getShowName());
			}

			int ticketingType1Quantity = 0;
			int ticketingType2Quantity = 0;
			int ticketingType3Quantity = 0;
			int ticketingType4Quantity = 0;
			int totalQuantity = 0;
			int ticketingType1OrderQuantity = 0;
			int ticketingType2OrderQuantity = 0;
			int ticketingType3OrderQuantity = 0;
			int ticketingType4OrderQuantity = 0;
			int totalOrderQuantity = 0;
			for (Map<String, Object> tMap : valueList) {
				String ticketingType = MapUtils.getString(tMap, "ticketingType");
				int quantity = MapUtils.getIntValue(tMap, "quantity", 0);
				int orderQuantity = MapUtils.getIntValue(tMap, "orderQuantity", 0);
				if (StringUtils.equals("ticketingType1", ticketingType)) {
					ticketingType1Quantity += quantity;
					ticketingType1OrderQuantity += orderQuantity;
				} else if (StringUtils.equals("ticketingType2", ticketingType)) {
					ticketingType2Quantity += quantity;
					ticketingType2OrderQuantity += orderQuantity;
				} else if (StringUtils.equals("ticketingType3", ticketingType)) {
					ticketingType3Quantity += quantity;
					ticketingType3OrderQuantity += orderQuantity;
				} else if (StringUtils.equals("ticketingType4", ticketingType)) {
					ticketingType4Quantity += quantity;
					ticketingType4OrderQuantity += orderQuantity;
				}
				totalQuantity += quantity;
				totalOrderQuantity += orderQuantity;
			}
			scheduleMap.put("ticketingType1Quantity", ticketingType1Quantity);
			scheduleMap.put("ticketingType2Quantity", ticketingType2Quantity);
			scheduleMap.put("ticketingType3Quantity", ticketingType3Quantity);
			scheduleMap.put("ticketingType4Quantity", ticketingType4Quantity);
			scheduleMap.put("totalQuantity", totalQuantity);
			scheduleMap.put("ticketingType1OrderQuantity", ticketingType1OrderQuantity);
			scheduleMap.put("ticketingType2OrderQuantity", ticketingType2OrderQuantity);
			scheduleMap.put("ticketingType3OrderQuantity", ticketingType3OrderQuantity);
			scheduleMap.put("ticketingType4OrderQuantity", ticketingType4OrderQuantity);
			scheduleMap.put("totalOrderQuantity", totalOrderQuantity);
			scheduleList.add(scheduleMap);
		}

		// 项目统计
		Map<String, List<Map<String, Object>>> program2scheduleListMap = BeanUtil.groupBeanListByFun(scheduleList,
				e -> e.get("userGroupId") + "#" + e.get("addUserId") + "#" + e.get("programId"), "");
		List<Map<String, Object>> programList = new ArrayList<>(program2scheduleListMap.size());
		for (Map.Entry<String, List<Map<String, Object>>> entry : program2scheduleListMap.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> valueList = entry.getValue();
			valueList.sort(Sort.asc("playTime"));

			String[] split = key.split("#");
			Long userGroupId = Long.valueOf(split[0]);
			Long addUserId = Long.valueOf(split[1]);
			Long programId = Long.valueOf(split[2]);
			HashMap programMap = new HashMap<>();
			programMap.put("userGroupId", userGroupId);
			programMap.put("addUserId", addUserId);
			programMap.put("programId", programId);
			ProgramSettleVo programVo = programVoMap.get(programId);
			if(programVo != null){
				programMap.put("programName", programVo.getProgramName());
				programMap.put("stadiumName", programVo.getStadiumName());
				programMap.put("venueName", programVo.getVenueName());
			}
			programMap.put("scheduleList", valueList);

			int ticketingType1Quantity = 0;
			int ticketingType2Quantity = 0;
			int ticketingType3Quantity = 0;
			int ticketingType4Quantity = 0;
			int totalQuantity = 0;
			int ticketingType1OrderQuantity = 0;
			int ticketingType2OrderQuantity = 0;
			int ticketingType3OrderQuantity = 0;
			int ticketingType4OrderQuantity = 0;
			int totalOrderQuantity = 0;
			for (Map<String, Object> scheduleMap : valueList) {
				ticketingType1Quantity += MapUtils.getIntValue(scheduleMap, "ticketingType1Quantity", 0);
				ticketingType2Quantity += MapUtils.getIntValue(scheduleMap, "ticketingType2Quantity", 0);
				ticketingType3Quantity += MapUtils.getIntValue(scheduleMap, "ticketingType3Quantity", 0);
				ticketingType4Quantity += MapUtils.getIntValue(scheduleMap, "ticketingType4Quantity", 0);
				totalQuantity += MapUtils.getIntValue(scheduleMap, "totalQuantity", 0);
				ticketingType1OrderQuantity += MapUtils.getIntValue(scheduleMap, "ticketingType1OrderQuantity", 0);
				ticketingType2OrderQuantity += MapUtils.getIntValue(scheduleMap, "ticketingType2OrderQuantity", 0);
				ticketingType3OrderQuantity += MapUtils.getIntValue(scheduleMap, "ticketingType3OrderQuantity", 0);
				ticketingType4OrderQuantity += MapUtils.getIntValue(scheduleMap, "ticketingType4OrderQuantity", 0);
				totalOrderQuantity += MapUtils.getIntValue(scheduleMap, "totalOrderQuantity", 0);
			}
			Map<String, Integer> total = new HashMap<>();
			total.put("ticketingType1Quantity", ticketingType1Quantity);
			total.put("ticketingType2Quantity", ticketingType2Quantity);
			total.put("ticketingType3Quantity", ticketingType3Quantity);
			total.put("ticketingType4Quantity", ticketingType4Quantity);
			total.put("totalQuantity", totalQuantity);
			total.put("ticketingType1OrderQuantity", ticketingType1OrderQuantity);
			total.put("ticketingType2OrderQuantity", ticketingType2OrderQuantity);
			total.put("ticketingType3OrderQuantity", ticketingType3OrderQuantity);
			total.put("ticketingType4OrderQuantity", ticketingType4OrderQuantity);
			total.put("totalOrderQuantity", totalOrderQuantity);

			programMap.put("total", total);
			programList.add(programMap);
		}

		// 用户统计
		Map<String, List<Map<String, Object>>> addUser2programListMap = BeanUtil.groupBeanListByFun(programList,
				e -> e.get("userGroupId") + "#" + e.get("addUserId"), "");
		List<Map<String, Object>> userList = new ArrayList<>(addUser2programListMap.size());
		for (Map.Entry<String, List<Map<String, Object>>> entry : addUser2programListMap.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> valueList = entry.getValue();

			String[] split = key.split("#");
			Long userGroupId = Long.valueOf(split[0]);
			Long addUserId = Long.valueOf(split[1]);
			HashMap userMap = new HashMap<>();
			userMap.put("userGroupId", userGroupId);
			userMap.put("addUserId", addUserId);
			userMap.put("addUserName", MapUtils.getString(userNameMap, addUserId, addUserId+""));
			userMap.put("programList", valueList);

			int ticketingType1Quantity = 0;
			int ticketingType2Quantity = 0;
			int ticketingType3Quantity = 0;
			int ticketingType4Quantity = 0;
			int totalQuantity = 0;
			int ticketingType1OrderQuantity = 0;
			int ticketingType2OrderQuantity = 0;
			int ticketingType3OrderQuantity = 0;
			int ticketingType4OrderQuantity = 0;
			int totalOrderQuantity = 0;
			for (Map<String, Object> programMap : valueList) {
				Map<String, Integer> programTotalMap = (Map<String, Integer>) MapUtils.getMap(programMap, "total", new HashMap<>());
				ticketingType1Quantity += MapUtils.getIntValue(programTotalMap, "ticketingType1Quantity", 0);
				ticketingType2Quantity += MapUtils.getIntValue(programTotalMap, "ticketingType2Quantity", 0);
				ticketingType3Quantity += MapUtils.getIntValue(programTotalMap, "ticketingType3Quantity", 0);
				ticketingType4Quantity += MapUtils.getIntValue(programTotalMap, "ticketingType4Quantity", 0);
				totalQuantity += MapUtils.getIntValue(programTotalMap, "totalQuantity", 0);
				ticketingType1OrderQuantity += MapUtils.getIntValue(programTotalMap, "ticketingType1OrderQuantity", 0);
				ticketingType2OrderQuantity += MapUtils.getIntValue(programTotalMap, "ticketingType2OrderQuantity", 0);
				ticketingType3OrderQuantity += MapUtils.getIntValue(programTotalMap, "ticketingType3OrderQuantity", 0);
				ticketingType4OrderQuantity += MapUtils.getIntValue(programTotalMap, "ticketingType4OrderQuantity", 0);
				totalOrderQuantity += MapUtils.getIntValue(programTotalMap, "totalOrderQuantity", 0);
			}
			Map<String, Integer> total = new HashMap<>();
			total.put("ticketingType1Quantity", ticketingType1Quantity);
			total.put("ticketingType2Quantity", ticketingType2Quantity);
			total.put("ticketingType3Quantity", ticketingType3Quantity);
			total.put("ticketingType4Quantity", ticketingType4Quantity);
			total.put("totalQuantity", totalQuantity);
			total.put("ticketingType1OrderQuantity", ticketingType1OrderQuantity);
			total.put("ticketingType2OrderQuantity", ticketingType2OrderQuantity);
			total.put("ticketingType3OrderQuantity", ticketingType3OrderQuantity);
			total.put("ticketingType4OrderQuantity", ticketingType4OrderQuantity);
			total.put("totalOrderQuantity", totalOrderQuantity);

			userMap.put("total", total);
			userList.add(userMap);
		}

		// 用户组统计
		Map<String, List<Map<String, Object>>> userGroupId2userListMap = BeanUtil.groupBeanListByFun(userList, e -> e.get("userGroupId") + "", "");
		List<Map<String, Object>> userGroupList = new ArrayList<>(userGroupId2userListMap.size());
		for (Map.Entry<String, List<Map<String, Object>>> entry : userGroupId2userListMap.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> value = entry.getValue();

			Long userGroupId = Long.valueOf(key);
			HashMap userGroupMap = new HashMap<>();
			userGroupMap.put("userGroupId", userGroupId);
			userGroupMap.put("userGroupName", MapUtils.getString(userGroupNameMap, userGroupId, userGroupId+""));
			userGroupMap.put("userList", value);

			userGroupList.add(userGroupMap);
		}

		model.put("userGroupList", userGroupList);
		return ResultCode.getSuccessReturn(model);
	}

	private Map<Long, String> getUserMap(List<Map<String, Object>> counts) {
		List<Long> addUserIdList = BeanUtil.getBeanPropertyList(counts, "addUserId", true);
		List<List<Long>> partition = BeanUtil.partition(addUserIdList, 500);
		List<ReportMemberSaleDetailVo> userList = new ArrayList<>(addUserIdList.size());
		for (List<Long> idsPart : partition) {
			userList.addAll(commonUntransService.getUserNames(idsPart));
		}
		return BeanUtil.beanListToMap(userList, "tbsUserId", "realName", true);
	}

	private Map<Long, String> getUserGroupMap(List<Map<String, Object>> counts) {
		List<Long> userGroupIdList = BeanUtil.getBeanPropertyList(counts, "userGroupId", true);
		List<List<Long>> partition = BeanUtil.partition(userGroupIdList, 500);
		List<UserGroupVo> userGroupVoList = new ArrayList<>(userGroupIdList.size());
		for (List<Long> idsPart : partition) {
			userGroupVoList.addAll(commonUntransService.getUserGroupByIds(idsPart));
		}
		return BeanUtil.beanListToMap(userGroupVoList, "id", "userGroupName", true);
	}
}

package com.llwh.dcenter.untrans.report;

import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.llwh.dcenter.helper.StandTicketTypeCountSearch;

/**
 * <AUTHOR>
 * @since 2024/5/30 14:50
 */
public interface ReportStandTicketTypeGatherUntransService {
	/**
	 * 站票票种销售统计
	 * @param user
	 * @param search
	 * @return
	 */
	ResultCode<Map> getCounts(AuthUser user, StandTicketTypeCountSearch search);

}

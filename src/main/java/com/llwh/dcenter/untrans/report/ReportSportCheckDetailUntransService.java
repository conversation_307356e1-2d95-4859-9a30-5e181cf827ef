package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearReportSportCheckDetailCount;
import com.llwh.dcenter.model.ReportSportCheckDetail;

public interface ReportSportCheckDetailUntransService {

	void updateSportCheckDetailJob();

	void updateSportCheckDetailCount(Timestamp startTime, Timestamp endTime);

	ResultCode<Page<ReportSportCheckDetail>> getCounts(AuthUser user, SearReportSportCheckDetailCount search, Page<ReportSportCheckDetail> page);

	ResultCode<ReportSportCheckDetail> getTotals(AuthUser user, SearReportSportCheckDetailCount search);

}

package com.llwh.dcenter.untrans.report.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.VoCopyUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.base.constant.Paymethod;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.SearchReportDouyinSeatOrderCheckCpsDetailCount;
import com.llwh.dcenter.helper.SearchSourcePlatform;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportDouyinSeatSaleCheckCpsDetail;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportDouyinSeatSaleCheckCpsDetailService;
import com.llwh.dcenter.service.report.ReportMemberInfoService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.untrans.common.SourcePlatformUntransService;
import com.llwh.dcenter.untrans.report.ReportDouyinSeatSaleCheckCpsDetailUntransService;
import com.llwh.dcenter.vo.DouyinOrderSettleVo;
import com.llwh.dcenter.vo.common.PaymethodVo;
import com.llwh.dcenter.vo.common.ProgramVo;
import com.llwh.dcenter.vo.report.ReportDouyinSeatSaleCheckCpsDetailVo;
import com.llwh.dcenter.vo.report.ReportMemberLevelCardNoVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportDouyinSeatSaleCheckCpsDetailUntransServiceImpl implements ReportDouyinSeatSaleCheckCpsDetailUntransService {

	@Autowired
	private ReportDouyinSeatSaleCheckCpsDetailService reportDouyinSeatSaleCheckCpsDetailService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	private static final String UK = "ReportDouyinSeatSaleCheckCpsDetail";
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());
	@Autowired
	private CommonUntransService commonUntransService;

	@Autowired
	private ReportMemberInfoService reportMemberInfoService;

	@Autowired
	private DataLevelUntransService dataLevelUntransService;
	@Autowired
	private SourcePlatformUntransService sourcePlatformUntransService;

	@Override
	public void updateDouyinSeatSaleCheckCpsDetailJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			int hour = record.getLastUpdateTime().toLocalDateTime().getHour();
			int minute = record.getLastUpdateTime().toLocalDateTime().getMinute();
			// 防止夜里更新的数据这里查询不到
			if (hour < 9 && minute < 15){
				startTime = DateUtil.addHour(record.getLastUpdateTime(), -10);
			}else {
				startTime = DateUtil.addMinute(record.getLastUpdateTime(), -20);
			}
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = Timestamp.valueOf(LocalDateTime.now().minusYears(1));
		}
		try {
			updateDouyinSeatSaleCheckCpsDetailCount(startTime, endTime, null);
			updateDouyinSeatSaleCheckCpsDetailForCpsInfoJob(startTime, endTime);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}


	private void updateDouyinSeatSaleCheckCpsDetailForCpsInfoJob(Timestamp startTime, Timestamp endTime) {
		List<String> tradeNos = reportDouyinSeatSaleCheckCpsDetailService.getLatestUpdatedSeatCps(startTime, endTime);
		if (CollectionUtils.isEmpty(tradeNos)) {
			return;
		}
		dbLogger.warn("更新订单号：{}", tradeNos);
		for (String tradeNo : tradeNos) {
			updateDouyinSeatSaleCheckCpsDetailCount(null, null, tradeNo);
		}
	}

	@Override
	public void updateDouyinSeatSaleCheckCpsDetailCount(Timestamp startTime, Timestamp endTime, String tradeNo) {
		List<ReportDouyinSeatSaleCheckCpsDetail> countList = reportDouyinSeatSaleCheckCpsDetailService.getSeatCount(startTime, endTime, tradeNo);
		List<ReportDouyinSeatSaleCheckCpsDetail> refundSeatCount = reportDouyinSeatSaleCheckCpsDetailService.getRefundSeatCount(startTime, endTime, tradeNo);
		if (CollectionUtils.isNotEmpty(refundSeatCount)) {
			countList.addAll(refundSeatCount);
		}
		if (CollectionUtils.isEmpty(countList)) {
			dbLogger.warn("查询为空：{}-{}|tradeNo={}", startTime, endTime, tradeNo);
			return;
		}
		countList = countList.stream().filter(r -> Paymethod.isDouyinPay(r.getPaymethod())).collect(Collectors.toList());
		List<String> tradeNos = countList.stream().map(ReportDouyinSeatSaleCheckCpsDetail::getTradeNo).collect(Collectors.toList());
		List<ReportDouyinSeatSaleCheckCpsDetail> seatCps = reportDouyinSeatSaleCheckCpsDetailService.getSeatCps(tradeNos);
		if (CollectionUtils.isNotEmpty(seatCps)) {
			for (ReportDouyinSeatSaleCheckCpsDetail detail : countList) {
				Optional<ReportDouyinSeatSaleCheckCpsDetail> first = seatCps.stream().filter(reportDouyinSeatOrderCpsDetail -> reportDouyinSeatOrderCpsDetail.getTradeNo().equals(detail.getTradeNo())).findFirst();
				if (first.isPresent()) {
					ReportDouyinSeatSaleCheckCpsDetail cpsDetail = first.get();
					detail.setCommissionUserNickname(cpsDetail.getCommissionUserNickname());
					detail.setCommissionUserDouyinid(cpsDetail.getCommissionUserDouyinid());
					detail.setCommissionRate(cpsDetail.getCommissionRate());
					detail.setCpsTaskId(cpsDetail.getCpsTaskId());
					detail.setTotalCommissionAmount(cpsDetail.getTotalCommissionAmount());
					detail.setTotalRefundAmount(cpsDetail.getTotalRefundAmount());
					detail.setTotalDeliveryAmount(cpsDetail.getTotalDeliveryAmount());
					detail.setSourceType(cpsDetail.getSourceType());
					detail.setSourceId(cpsDetail.getSourceId());
					detail.setCpsStatus(cpsDetail.getCpsStatus());
				}
			}
		}

		List<DouyinOrderSettleVo> settleVoList = reportDouyinSeatSaleCheckCpsDetailService.getSeatSettle(tradeNos);
		if (CollectionUtils.isNotEmpty(settleVoList)) {
			for (ReportDouyinSeatSaleCheckCpsDetail detail : countList) {
				List<DouyinOrderSettleVo> orderSettleVoList = settleVoList.stream()
						.filter(douyinOrderSettleVo -> douyinOrderSettleVo.getTradeNo().equals(detail.getTradeNo()))
						.collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(orderSettleVoList)) {
					detail.setSettleAmount(orderSettleVoList.stream().mapToLong(DouyinOrderSettleVo::getSettleAmount).sum());
					detail.setRake(orderSettleVoList.stream().mapToLong(DouyinOrderSettleVo::getRake).sum());
					detail.setRakeRate((Objects.isNull(detail.getSettleAmount()) || detail.getSettleAmount() == 0) ? 0.00d : Double.parseDouble(BigDecimal.valueOf(detail.getRake()).divide(BigDecimal.valueOf(detail.getSettleAmount()),4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).toString()));
					detail.setCommission(orderSettleVoList.stream().mapToLong(DouyinOrderSettleVo::getCommission).sum());
					detail.setSettleTime(new Timestamp(orderSettleVoList.stream().mapToLong(DouyinOrderSettleVo::getSettleTime).max().getAsLong()));
					detail.setSettleFinalAmount(orderSettleVoList.stream().mapToLong(o -> {
						String settleDetail = o.getSettleDetail();
						if (StringUtils.isBlank(settleDetail)){
							dbLogger.warn("分成信息为空|tradeNo={}", o.getTradeNo());
							return 0;
						}
						String[] split = settleDetail.split("-分成金额\\(分\\)");
						if (split.length != 2){
							dbLogger.warn("分成信息解析失败|tradeNo={}", o.getTradeNo());
							return 0;
						}
						return Long.parseLong(split[1].trim());
					}).sum());
					long count = orderSettleVoList.stream().map(DouyinOrderSettleVo::getSettleStatus).distinct().count();
					if (count == 1) {
						detail.setSettleStatus(orderSettleVoList.get(0).getSettleStatus());
					} else {
						detail.setSettleStatus(orderSettleVoList.stream().map(v -> v.getItemOrderId() + ":" + v.getSettleStatus()).distinct().collect(Collectors.joining(",")));
					}
					detail.setSettleMessage(orderSettleVoList.stream().filter(v -> StringUtils.isNotBlank(v.getSettleMessage()))
							.map(v -> StringUtils.isNotBlank(v.getItemOrderId()) ? v.getItemOrderId() + ":" + v.getSettleMessage() : v.getSettleMessage())
							.distinct().collect(Collectors.joining(",")));
				}
			}
		}

		List<Long> memberIds = BeanUtil.getBeanPropertyList(countList, "memberId", true);
		List<Long> companyIds = BeanUtil.getBeanPropertyList(countList, "companyId", true);
		Map<String, ReportMemberLevelCardNoVo> cardNoMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(memberIds)) {
			List<ReportMemberLevelCardNoVo> memberCardNos = reportMemberInfoService.getMemberCardNos(memberIds, companyIds, endTime);
			cardNoMap = memberCardNos.stream().collect(Collectors.toMap(item -> {
				return item.getCompanyId() + "" + item.getMemberId();
			}, item -> item, (oldObj, newObj) -> newObj));
		}

		List<String> names = BeanUtil.getBeanPropertyList(countList, "paymethod", true);
		List<PaymethodVo> paymethodVos = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(names)) {
			paymethodVos = commonUntransService.getPayMethods(names);
		}
		List<PaymethodVo> finalPaymethodVos = paymethodVos;
		for (ReportDouyinSeatSaleCheckCpsDetail count : countList) {
			if (CollectionUtils.isNotEmpty(finalPaymethodVos)) {
				finalPaymethodVos.forEach(vo -> {
					if (vo.getName().equals(count.getPaymethod())) {
						count.setPaymethod(vo.getCnName());
					}
				});
			}

			ReportMemberLevelCardNoVo reportMemberLevelCardNoVo = cardNoMap.get(count.getCompanyId() + "" + count.getMemberId());
			if (reportMemberLevelCardNoVo != null) {
				count.setMemberId(reportMemberLevelCardNoVo.getMemberId());
			}
			count.setUpdatetime(DateUtil.getCurFullTimestamp());
		}
		reportDouyinSeatSaleCheckCpsDetailService.saveOrUpdateBatch(countList);
		if (StringUtils.isBlank(tradeNo)) {
			dbLogger.warn("刷新座票销售及入场明细表：{}~{} {}", startTime, endTime, countList.size());
		}
	}

	@Override
	public void updateDouyinRefundSeatSaleCheckCpsDetailCount(Timestamp startTime, Timestamp endTime, String tradeNo) {
		List<ReportDouyinSeatSaleCheckCpsDetail> countList = reportDouyinSeatSaleCheckCpsDetailService.getRefundSeatCount(startTime, endTime, tradeNo);
		if (CollectionUtils.isEmpty(countList)) {
			return;
		}
		List<ReportDouyinSeatSaleCheckCpsDetail> seatCps = reportDouyinSeatSaleCheckCpsDetailService.getSeatCps(countList.stream().map(ReportDouyinSeatSaleCheckCpsDetail::getTradeNo).collect(Collectors.toList()));
		if (CollectionUtils.isNotEmpty(seatCps)) {
			for (ReportDouyinSeatSaleCheckCpsDetail detail : countList) {
				Optional<ReportDouyinSeatSaleCheckCpsDetail> first = seatCps.stream().filter(reportDouyinSeatOrderCpsDetail -> reportDouyinSeatOrderCpsDetail.getTradeNo().equals(detail.getTradeNo())).findFirst();
				if (first.isPresent()) {
					ReportDouyinSeatSaleCheckCpsDetail cpsDetail = first.get();
					detail.setCommissionUserNickname(cpsDetail.getCommissionUserNickname());
					detail.setCommissionUserDouyinid(cpsDetail.getCommissionUserDouyinid());
					detail.setCommissionRate(cpsDetail.getCommissionRate());
					detail.setCpsTaskId(cpsDetail.getCpsTaskId());
					detail.setTotalCommissionAmount(cpsDetail.getTotalCommissionAmount());
					detail.setTotalRefundAmount(cpsDetail.getTotalRefundAmount());
					detail.setTotalDeliveryAmount(cpsDetail.getTotalDeliveryAmount());
					detail.setSourceType(cpsDetail.getSourceType());
					detail.setSourceId(cpsDetail.getSourceId());
					detail.setCpsStatus(cpsDetail.getCpsStatus());
				}
			}
		}

		List<Long> memberIds = BeanUtil.getBeanPropertyList(countList, "memberId", true);
		List<Long> companyIds = BeanUtil.getBeanPropertyList(countList, "companyId", true);
		Map<String, ReportMemberLevelCardNoVo> cardNoMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(memberIds)) {
			List<ReportMemberLevelCardNoVo> memberCardNos = reportMemberInfoService.getMemberCardNos(memberIds, companyIds, endTime);
			cardNoMap = memberCardNos.stream().collect(Collectors.toMap(item -> {
				return item.getCompanyId() + "" + item.getMemberId();
			}, item -> item, (oldObj, newObj) -> newObj));
		}

		List<String> names = BeanUtil.getBeanPropertyList(countList, "paymethod", true);
		List<PaymethodVo> paymethodVos = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(names)) {
			paymethodVos = commonUntransService.getPayMethods(names);
		}
		List<PaymethodVo> finalPaymethodVos = paymethodVos;
		for (ReportDouyinSeatSaleCheckCpsDetail count : countList) {
			if (CollectionUtils.isNotEmpty(finalPaymethodVos)) {
				finalPaymethodVos.forEach(vo -> {
					if (vo.getName().equals(count.getPaymethod())) {
						count.setPaymethod(vo.getCnName());
					}
				});
			}

			ReportMemberLevelCardNoVo reportMemberLevelCardNoVo = cardNoMap.get(count.getCompanyId() + "" + count.getMemberId());
			if (reportMemberLevelCardNoVo != null) {
				count.setMemberId(reportMemberLevelCardNoVo.getMemberId());
			}
			count.setUpdatetime(DateUtil.getCurFullTimestamp());
		}
		reportDouyinSeatSaleCheckCpsDetailService.saveOrUpdateBatch(countList);
		if (StringUtils.isBlank(tradeNo)) {
			dbLogger.warn("刷新座票销售及入场明细表：{}~{} {}", startTime, endTime, countList.size());
		}
	}

	@Override
	public ResultCode<Page<ReportDouyinSeatSaleCheckCpsDetail>> getCounts(AuthUser user, SearchReportDouyinSeatOrderCheckCpsDetailCount search, Page<ReportDouyinSeatSaleCheckCpsDetail> page) {
		if ((search.getStartTime() == null || search.getEndTime() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null) &&
				(search.getOrderTimeFrom() == null || search.getOrderTimeTo() == null)) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, false, true);
		QueryWrapper<ReportDouyinSeatSaleCheckCpsDetail> wrapper = new QueryWrapper<>();
		wrapper.eq("company_id", user.getCompanyId());
		wrapper.orderByAsc("tickettime", "updatetime", "id");
		if (search.getStadiumId() != null) {
			wrapper.eq("stadium_id", search.getStadiumId());
		}
		if (search.getVenueId() != null) {
			wrapper.eq("venue_id", search.getVenueId());
		}
		if (search.getProgramId() != null) {
			wrapper.eq("program_id", search.getProgramId());
		}
		if (search.getShowNameId() != null) {
			wrapper.eq("show_id", search.getShowNameId());
		}
		if (StringUtils.isNotBlank(search.getPaymethod())) {
			wrapper.eq("paymethod", search.getPaymethod());
		}
		if (StringUtils.isNotBlank(search.getTradeNo())) {
			wrapper.eq("trade_no", search.getTradeNo());
		}
		if (StringUtils.isNotBlank(search.getOutTradeNo())) {
			wrapper.eq("out_trade_no", search.getOutTradeNo());
		}
		if (StringUtils.isNotBlank(search.getChannel())) {
			wrapper.eq("channel", search.getChannel());
		}
		if (StringUtils.isNotBlank(search.getContactMobile())) {
			wrapper.eq("contact_mobile", search.getContactMobile());
		}
		if (StringUtils.isNotBlank(search.getTicketUser())) {
			wrapper.eq("ticket_user", search.getTicketUser());
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			wrapper.like("program_code", "%" + search.getProgramCode() + "%");
		}

		if (StringUtils.isNotBlank(search.getStadiumName())) {
			wrapper.like("stadium_name", "%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			wrapper.like("venue_name", "%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			wrapper.like("program_name", "%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getCategory())) {
			wrapper.like("category", "%" + search.getCategory() + "%");
		}
		if (StringUtils.isNotBlank(search.getSmallCategory())) {
			wrapper.like("small_category", "%" + search.getSmallCategory() + "%");
		}
		if (ObjectUtils.isNotEmpty(search.getStartTime())) {
			wrapper.ge("tickettime", search.getStartTime());
		}
		if (ObjectUtils.isNotEmpty(search.getEndTime())) {
			wrapper.le("tickettime", search.getEndTime());
		}
		if (ObjectUtils.isNotEmpty(search.getOrderTimeFrom())) {
			wrapper.ge("ordertime", search.getOrderTimeFrom());
		}
		if (ObjectUtils.isNotEmpty(search.getOrderTimeTo())) {
			wrapper.le("ordertime", search.getOrderTimeTo());
		}
		if (ObjectUtils.isNotEmpty(search.getPlayStartTime())) {
			wrapper.ge("play_time", search.getPlayStartTime());
		}
		if (ObjectUtils.isNotEmpty(search.getPlayEndTime())) {
			wrapper.le("play_time", search.getPlayEndTime());
		}
		if (StringUtils.equals(search.getOrderStatus(), "paidNoTickets")) {
			wrapper.like("order_status", "paid%");
			wrapper.notIn("order_status", "paid_success", "paid_return", "paid_return_cert", "paid_return_succ", "paid_return_fail");
		}
		if (StringUtils.equals(search.getOrderStatus(), "paidSuccess")) {
			wrapper.eq("order_status", "paid_success");
		}
		if (StringUtils.equals(search.getOrderStatus(), "paidRefund")) {
			wrapper.like("order_status", "paid_return%");
		}
		if (StringUtils.equals(search.getOrderStatus(), "unPaid")) {
			wrapper.like("order_status", "cancel%").or()
					.like("order_status", "new%");
		}
		if (StringUtils.isNotBlank(search.getUserName())) {
			wrapper.like("user_name", "%" + search.getUserName() + "%");
		}
		if (StringUtils.isNotBlank(search.getPlatform())) {
			wrapper.eq("platform", search.getPlatform());
		}

		if (StringUtils.isNotBlank(search.getShowIds())) {
			List<Long> showIdList = BeanUtil.getIdList(search.getShowIds(), ",");
			wrapper.in("show_id", showIdList);
		}
		if (ObjectUtils.isNotEmpty(search.getPaidtimeStart())) {
			wrapper.ge("paidtime", search.getPaidtimeStart());
		}
		if (ObjectUtils.isNotEmpty(search.getPaidtimeEnd())) {
			wrapper.le("paidtime", search.getPaidtimeEnd());
		}

		boolean haveConsumer = CollectionUtils.isNotEmpty(search.getAuthorityStadiumIds()) || search.getAuthorityUserGroupId() != null || search.getAuthorityUserId() != null;
		Consumer<QueryWrapper<ReportDouyinSeatSaleCheckCpsDetail>> consumer = consumerWrapper -> {
			if (CollectionUtils.isNotEmpty(search.getAuthorityStadiumIds())) {
				consumerWrapper.in("stadium_id", search.getAuthorityStadiumIds());
			}
			if (search.getAuthorityUserGroupId() != null) {
				consumerWrapper.eq("user_group_id", search.getAuthorityUserGroupId());
			}
			if (search.getAuthorityUserId() != null) {
				consumerWrapper.eq("add_user_id", search.getAuthorityUserId());
			}
		};
		if (CollectionUtils.isNotEmpty(search.getAuthorityProgramIds())) {
			wrapper.and(wr -> wr.in("program_id", search.getAuthorityProgramIds()).or(haveConsumer, consumer));
		} else {
			wrapper.and(haveConsumer, consumer);
		}
		Page<ReportDouyinSeatSaleCheckCpsDetail> pageResult = reportDouyinSeatSaleCheckCpsDetailService.page(page, wrapper);
		List<Long> programIds = BeanUtil.getBeanPropertyList(pageResult.getRecords(), "programId", true);
		List<ProgramVo> ProgramVos = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(programIds)) {
			ProgramVos = commonUntransService.getPrograms(programIds);
		}
		Map<Long, ProgramVo> ProgramMap = BeanUtil.beanListToMap(ProgramVos, "id");
		for (ReportDouyinSeatSaleCheckCpsDetail detail : pageResult.getRecords()) {

			ProgramVo programVo = ProgramMap.get(detail.getProgramId());
			if (programVo != null) {
				detail.setProgramStartTime(programVo.getStartTime());
				detail.setProgramEndTime(programVo.getEndTime());
			}
		}

		return ResultCode.getSuccessReturn(pageResult);
	}

	@Override
	public ResultCode<Page<ReportDouyinSeatSaleCheckCpsDetailVo>> getCountsBySql(AuthUser user, SearchReportDouyinSeatOrderCheckCpsDetailCount search, Page<ReportDouyinSeatSaleCheckCpsDetailVo> page) {
		if(StringUtils.isBlank(search.getShowIds()) && (search.getStartTime() == null || search.getEndTime() == null)
				&& (search.getPaidtimeStart() == null || search.getPaidtimeEnd() == null)) {
			return ResultCodeHelper.CODE11_PARAMS_REQUIRED("项目场次、出票/退票时间、支付时间，三个筛选条件至少选择一个");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, false, true);
		search.setCompanyId(user.getCompanyId());
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}

		if (StringUtils.isNotBlank(search.getCategory())) {
			search.setCategory("%" + search.getCategory() + "%");
		}
		if (StringUtils.isNotBlank(search.getSmallCategory())) {
			search.setSmallCategory("%" + search.getSmallCategory() + "%");
		}

		if (StringUtils.isNotBlank(search.getUserName())) {
			search.setUserName("%" + search.getUserName() + "%");
		}
		if (StringUtils.isNotBlank(search.getCommissionUserNickname())) {
			search.setCommissionUserNickname("%" + search.getCommissionUserNickname() + "%");
		}
		if (StringUtils.isNotBlank(search.getShowIds())) {
			List<Long> showIdList = BeanUtil.getIdList(search.getShowIds(), ",");
			search.setShowIdList(showIdList);
		}
		SearchSourcePlatform searchSourcePlatform = new SearchSourcePlatform(search.getSource());
		sourcePlatformUntransService.fillSearchBySoruce(searchSourcePlatform, user);
		VoCopyUtil.copyObject(search, searchSourcePlatform, true);

		Page<ReportDouyinSeatSaleCheckCpsDetailVo> pageResult = reportDouyinSeatSaleCheckCpsDetailService.getCounts(page, search);

		List<Long> programIds = BeanUtil.getBeanPropertyList(pageResult.getRecords(), "programId", true);
		List<ProgramVo> ProgramVos = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(programIds)) {
			ProgramVos = commonUntransService.getPrograms(programIds);
		}
		Map<Long, ProgramVo> ProgramMap = BeanUtil.beanListToMap(ProgramVos, "id");
		for (ReportDouyinSeatSaleCheckCpsDetailVo detail : pageResult.getRecords()) {

			detail.setCommissionYuan(convertToYuan(detail.getCommission()));
			detail.setRakeYuan(convertToYuan(detail.getRake()));
			detail.setSettleAmountYuan(convertToYuan(detail.getSettleAmount()));
			detail.setTotalCommissionAmountYuan(convertToYuan(detail.getTotalCommissionAmount()));
			detail.setTotalDeliveryAmountYuan(convertToYuan(detail.getTotalDeliveryAmount()));
			detail.setTotalRefundAmountYuan(convertToYuan(detail.getTotalRefundAmount()));
			detail.setSettleFinalAmountYuan(convertToYuan(detail.getSettleFinalAmount()));

			ProgramVo programVo = ProgramMap.get(detail.getProgramId());
			if (programVo != null) {
				detail.setProgramStartTime(programVo.getStartTime());
				detail.setProgramEndTime(programVo.getEndTime());
			}
		}

		return ResultCode.getSuccessReturn(pageResult);
	}

	private static Double convertToYuan(Long amount) {
		if (Objects.isNull(amount)){
			return null;
		}
		return BigDecimal.valueOf(amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue();
	}


	@Override
	public List<List<Object>> getTotalCount(AuthUser user, SearchReportDouyinSeatOrderCheckCpsDetailCount search) {
		List<List<Object>> resultList = new ArrayList<>();
		if ((search.getStartTime() == null || search.getEndTime() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return resultList;
		}
		search.setCompanyId(user.getCompanyId());
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getCategory())) {
			search.setCategory("%" + search.getCategory() + "%");
		}
		if (StringUtils.isNotBlank(search.getSmallCategory())) {
			search.setSmallCategory("%" + search.getSmallCategory() + "%");
		}
		if (StringUtils.isNotBlank(search.getSettleStatus())) {
			search.setSettleStatus("%" + search.getSettleStatus() + "%");
		}
		List<ReportDouyinSeatSaleCheckCpsDetail> result = reportDouyinSeatSaleCheckCpsDetailService.getTotalCount(search);
		for (ReportDouyinSeatSaleCheckCpsDetail detail : result) {
			List<Object> objList = new ArrayList<>();
			objList.add(detail.getTradeNo());
			objList.add(detail.getMobile());
			objList.add(detail.getTickettime());
			objList.add(detail.getProgramId());
			objList.add(detail.getProgramCode());
			objList.add(detail.getProgramName());
			objList.add(detail.getCategory());
			objList.add(detail.getSmallCategory());
			objList.add(detail.getStadiumName());
			objList.add(detail.getVolume());
			objList.add(detail.getVenueName());
			objList.add(detail.getShowName());
			objList.add(detail.getPlayTime());
			objList.add(detail.getAmount());
			objList.add(detail.getDiscount());
			objList.add(detail.getPaidAmount());
			objList.add(detail.getQuantity());
			objList.add(ReportConstant.payTypeMap.get(detail.getPayType()));
			objList.add(detail.getPaymethod());
			objList.add(detail.getPayseqno());
			objList.add(detail.getUserGroupName());
			objList.add(detail.getOrderStatus());
			objList.add(detail.getOutTradeNo());
			objList.add(detail.getChannel());
			objList.add(detail.getContactMobile());
			objList.add(detail.getTicketUser());
			resultList.add(objList);
		}
		return resultList;
	}

	@Override
	public void removeNotDouyinPayOrder() {
		reportDouyinSeatSaleCheckCpsDetailService.removeNotDouyinPayOrder(Paymethod.DOUYIN);
	}
}

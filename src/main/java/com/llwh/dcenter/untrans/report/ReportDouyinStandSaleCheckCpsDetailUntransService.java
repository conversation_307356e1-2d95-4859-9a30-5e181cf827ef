package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;
import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearchReportDouyinSeatOrderCheckCpsDetailCount;
import com.llwh.dcenter.model.ReportDouyinStandSaleCheckCpsDetail;
import com.llwh.dcenter.vo.report.ReportDouyinStandSaleCheckCpsDetailVo;

public interface ReportDouyinStandSaleCheckCpsDetailUntransService {

	void updateDouyinStandSaleCheckCpsDetailJob();

	void updateDouyinStandSaleCheckCpsDetailCount(Timestamp startTime, Timestamp endTime, String tradeNo);

	void updateDouyinRefundStandSaleCheckCpsDetailCount(Timestamp startTime, Timestamp endTime, String tradeNo);

	ResultCode<Page<ReportDouyinStandSaleCheckCpsDetail>> getCounts(AuthUser user, SearchReportDouyinSeatOrderCheckCpsDetailCount search, Page<ReportDouyinStandSaleCheckCpsDetail> page);

	ResultCode<Page<ReportDouyinStandSaleCheckCpsDetailVo>> getCountsBySql(AuthUser user, SearchReportDouyinSeatOrderCheckCpsDetailCount search, Page<ReportDouyinStandSaleCheckCpsDetailVo> page);

	List<List<Object>> getTotalCount(AuthUser user, SearchReportDouyinSeatOrderCheckCpsDetailCount search);

	/**
	 * 删除历史数据中非抖音支付的订单
	 */
	void removeNotDouyinPayOrder();
}

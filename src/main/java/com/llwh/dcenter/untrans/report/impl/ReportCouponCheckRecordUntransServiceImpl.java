package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.VmBaseUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.SearReportCouponCheckRecord;
import com.llwh.dcenter.mapper.ReportCouponCheckRecordMapper;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportCouponCheckRecord;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.ReportCouponCheckRecordService;
import com.llwh.dcenter.untrans.report.ReportCouponCheckRecordUntransService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportCouponCheckRecordUntransServiceImpl implements ReportCouponCheckRecordUntransService {
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());
	@Autowired
	private ReportCouponCheckRecordMapper reportCouponCheckRecordMapper;
	@Autowired
	private ReportCouponCheckRecordService reportCouponCheckRecordService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	private static final String UK = "ReportCouponCheckRecord";

	@Override
	public void updateCouponCheckRecordJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			startTime = DateUtil.addMinute(record.getLastUpdateTime(), ReportConstant.UPDATE_REPEAT_MINUTE);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = DateUtil.getCurFullTimestamp();
		}
		try {
			updateCouponCheckRecord(startTime, endTime);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateCouponCheckRecord(Timestamp startTime, Timestamp endTime) {
		List<ReportCouponCheckRecord> recordList = reportCouponCheckRecordMapper.getCouponCheckRecord(startTime, endTime);
		if (CollectionUtils.isEmpty(recordList)) {
			return;
		}
		reportCouponCheckRecordService.saveOrUpdateBatch(recordList);
		QueryWrapper<ReportCouponCheckRecord> queryWrapper = new QueryWrapper<>();
		queryWrapper.ge("check_time", startTime);
		queryWrapper.lt("check_time", endTime);
		queryWrapper.isNull("coupon_batch_id");
		queryWrapper.select("cardpass");
		List<String> cardpassList = reportCouponCheckRecordService.listObjs(queryWrapper, String::valueOf);
		if (CollectionUtils.isNotEmpty(cardpassList)) {
			List<List<String>> partition = Lists.partition(cardpassList, 100);
			for (List<String> cps : partition) {
				List<ReportCouponCheckRecord> couponInfo = reportCouponCheckRecordMapper.getCouponInfo(cps);
				for (ReportCouponCheckRecord record : couponInfo) {
					UpdateWrapper<ReportCouponCheckRecord> updateWrapper = new UpdateWrapper<>();
					updateWrapper.set("coupon_batch_title", record.getCouponBatchTitle());
					updateWrapper.set("coupon_batch_id", record.getCouponBatchId());
					updateWrapper.set("cardno", record.getCardno());
					updateWrapper.set("mobile", record.getMobile());
					updateWrapper.eq("cardpass", record.getCardpass());
					updateWrapper.isNull("coupon_batch_id");
					reportCouponCheckRecordService.update(updateWrapper);
				}
			}
		}

		QueryWrapper<ReportCouponCheckRecord> merchantWrapper = new QueryWrapper<>();
		merchantWrapper.ge("check_time", startTime);
		merchantWrapper.lt("check_time", endTime);
		merchantWrapper.isNotNull("merchant_id");
		merchantWrapper.isNull("merchant_user_name");
		merchantWrapper.select("distinct merchant_id");
		List<Long> merchantIds = reportCouponCheckRecordService.listObjs(merchantWrapper, o -> Long.valueOf(o.toString()));
		if (CollectionUtils.isNotEmpty(merchantIds)) {
			List<List<Long>> partition = Lists.partition(merchantIds, 100);
			for (List<Long> longs : partition) {
				List<ReportCouponCheckRecord> merchantInfos = reportCouponCheckRecordMapper.getMerchantInfo(longs);
				for (ReportCouponCheckRecord record : merchantInfos) {
					UpdateWrapper<ReportCouponCheckRecord> updateWrapper = new UpdateWrapper<>();
					updateWrapper.set("merchant_user_name", record.getMerchantUserName());
					updateWrapper.set("agency_id", record.getAgencyId());
					updateWrapper.set("agency_name", record.getAgencyName());
					updateWrapper.eq("merchant_id", record.getMerchantId());
					updateWrapper.isNull("merchant_user_name");
					reportCouponCheckRecordService.update(updateWrapper);
				}
			}
		}
		dbLogger.warn("刷新优惠券核销明细表：{},{},{}", recordList.size(), startTime, endTime);
	}

	@Override
	public ResultCode<Page<ReportCouponCheckRecord>> getCounts(AuthUser user, Page<ReportCouponCheckRecord> page, SearReportCouponCheckRecord search) {
		if (search.getCheckTimeFrom() == null || search.getCheckTimeTo() == null) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("核销时间范围不能为空");
		}
		QueryWrapper<ReportCouponCheckRecord> query = new QueryWrapper<>();
		query.eq("company_id", user.getCompanyId());
		query.ge("check_time", search.getCheckTimeFrom());
		query.lt("check_time", search.getCheckTimeTo());
		if (StringUtils.isNotBlank(search.getCouponBatchTitle())) {
			query.like("coupon_batch_title","%" + search.getCouponBatchTitle() + "%");
		}
		if (StringUtils.isNotBlank(search.getCardno())) {
			query.like("cardno","%" + search.getCardno() + "%");
		}
		if (StringUtils.isNotBlank(search.getMobile())) {
			query.eq("mobile",search.getMobile());
		}
		if (search.getTbsUserId() != null) {
			query.eq("tbs_user_id", search.getTbsUserId());
		}
		if (search.getCheckUserGroupId() != null) {
			query.eq("check_user_group_id", search.getCheckUserGroupId());
		}
		if (search.getAgencyId() != null) {
			query.eq("agency_id", search.getAgencyId());
		}
		if (StringUtils.equals(AuthUser.USERTYPE_TEAM, user.getUsertype())) {
			query.eq("agency_id", user.getMerchant().getAgencyId());
		}
		query.orderByDesc("check_time");
		Page<ReportCouponCheckRecord> pageResult = reportCouponCheckRecordService.page(page, query);
		if (StringUtils.equals(AuthUser.USERTYPE_TEAM, user.getUsertype())) {
			for (ReportCouponCheckRecord record : pageResult.getRecords()) {
				record.setMobile(VmBaseUtil.getSmobile(record.getMobile()));
			}
		}
		return ResultCode.getSuccessReturn(pageResult);
	}

}

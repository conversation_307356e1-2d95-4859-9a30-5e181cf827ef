package com.llwh.dcenter.untrans.report.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.StringUtil;
import cn.fancylab.util.ValueUtil;

import com.llwh.dcenter.helper.MallOrderGatherByDetailSear;
import com.llwh.dcenter.service.report.ReportMallOrderGatherByDetailService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.report.ReportMallOrderGatherByDetailUntransService;
import com.llwh.dcenter.vo.report.ReportMallOrderGatherVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportMallOrderGatherByDetailUntransServiceImpl implements ReportMallOrderGatherByDetailUntransService {
	@Autowired
	private ReportMallOrderGatherByDetailService mallOrderGatherByDetailService;
	@Autowired
	private CommonUntransService commonUntransService;


	private ResultCode<List<ReportMallOrderGatherVo>> updateStandSaleCheckGatherCount(MallOrderGatherByDetailSear search) {
		List<ReportMallOrderGatherVo> totalList = new ArrayList<>();
		List<ReportMallOrderGatherVo> standCount = mallOrderGatherByDetailService.getStandCount(search);
		if (CollectionUtils.isNotEmpty(standCount)) {
			totalList.addAll(standCount);
		}
		// 退款
		List<ReportMallOrderGatherVo> standRefundCount = mallOrderGatherByDetailService.getStandRefundCount(search);
		if (CollectionUtils.isNotEmpty(standRefundCount)) {
			totalList.addAll(standRefundCount);
		}
		//Timestamp now = DateUtil.getCurFullTimestamp();
		Map<String, List<ReportMallOrderGatherVo>> stringListMap = BeanUtil.groupBeanListByFun(totalList, count -> {
			return StringUtil.md5(count.getOrderType() + "_" + count.getDetailProductId() + "_" + count.getProductName() + "_" + count.getSkuName() + "_" +
					count.getSkuCode() + "_" + count.getDetailSkuId() + "_" + count.getUnitPrice() + "_" + count.getAgencyId(), 35);
		}, "");
		Map<Long, Map<String, Object>> stockNumMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(totalList) && StringUtils.equals("mallcultural", search.getOrderType())) {
			List<Long> detailSkuIdList = BeanUtil.getBeanPropertyList(totalList, "detailSkuId", true);
			List<Map<String, Object>> numMapList = commonUntransService.getCulturalSkuStockNum(search.getCompanyId(), detailSkuIdList);
			stockNumMap = BeanUtil.beanListToMap(numMapList, "detailSkuId");
		}
		List<ReportMallOrderGatherVo> resultList = new ArrayList<>();
		for (String id : stringListMap.keySet()) {
			List<ReportMallOrderGatherVo> countList = stringListMap.get(id);
			if (countList.size() == 1) {
				ReportMallOrderGatherVo count = countList.get(0);
				count.setId(id);
				Integer saleCount = count.getSaleCount();
				if (saleCount == null) {
					saleCount = 0;
					count.setSaleCount(saleCount);
				}
				Double amount = count.getAmount();
				if (amount == null) {
					amount = 0.0;
					count.setAmount(amount);
				}
				Double discount = count.getDiscount();
				if (discount == null) {
					discount = 0.0;
					count.setDiscount(discount);
				}
				Double paidAmount = count.getPaidAmount();
				if (paidAmount == null) {
					paidAmount = 0.0;
					count.setPaidAmount(paidAmount);
				}
				Integer refundCount = count.getRefundCount();
				if (refundCount == null) {
					refundCount = 0;
					count.setRefundCount(refundCount);
				}
				Double refundAmount = count.getRefundAmount();
				if (refundAmount == null) {
					refundAmount = 0.0;
					count.setRefundAmount(refundAmount);
				}
				Double refundDiscount = count.getRefundDiscount();
				if (refundDiscount == null) {
					refundDiscount = 0.0;
					count.setRefundDiscount(refundDiscount);
				}
				Double refundPaidAmount = count.getRefundPaidAmount();
				if (refundPaidAmount == null) {
					refundPaidAmount = 0.0;
					count.setRefundPaidAmount(refundPaidAmount);
				}
				Integer pointValue = count.getPointValue();
				if (pointValue == null) {
					pointValue = 0;
					count.setPointValue(pointValue);
				}
				Integer refundPointValue = count.getRefundPointValue();
				if (refundPointValue == null) {
					refundPointValue = 0;
					count.setRefundPointValue(refundPointValue);
				}
				count.setTotalQuantity(saleCount + refundCount);
				count.setTotalAmount(ValueUtil.round(amount + refundAmount));
				count.setTotalDiscount(ValueUtil.round(discount + refundDiscount));
				count.setTotalPaidAmount(ValueUtil.round(paidAmount + refundPaidAmount));
				count.setTotalPointValue(pointValue + refundPointValue);
				if (count.getTotalQuantity() == 0) {
					count.setUnitPointValue(0);
				} else {
					count.setUnitPointValue(count.getTotalPointValue() / count.getTotalQuantity());
				}
				setOtherFields(count, stockNumMap);
				resultList.add(count);
			} else {
				Integer saleCount = 0;
				Double amount = 0.0;
				Double discount = 0.0;
				Double paidAmount = 0.0;
				Integer pointValue = 0;

				Integer refundCount = 0;
				Double refundAmount = 0.0;
				Double refundDiscount = 0.0;
				Double refundPaidAmount = 0.0;
				Integer refundPointValue = 0;

				Integer totalQuantity = 0;
				Double  totalAmount = 0.0;
				Double totalDisCount = 0.0;
				Double totalPaidAmount = 0.0;
				Integer totalPointValue = 0;
				ReportMallOrderGatherVo reportStandSaleCheckGather = countList.get(0);
				reportStandSaleCheckGather.setId(id);
				for (ReportMallOrderGatherVo temp : countList) {
					saleCount += temp.getSaleCount() == null ? 0 : temp.getSaleCount();
					amount += temp.getAmount() == null ? 0.0 : temp.getAmount();
					amount = ValueUtil.round(amount);
					discount += temp.getDiscount() == null ? 0.0 : temp.getDiscount();
					discount = ValueUtil.round(discount);
					paidAmount += temp.getPaidAmount() == null ? 0.0 : temp.getPaidAmount();
					paidAmount = ValueUtil.round(paidAmount);
					pointValue += temp.getPointValue() == null ? 0 : temp.getPointValue();

					refundCount += temp.getRefundCount() == null ? 0 : temp.getRefundCount();
					refundAmount += temp.getRefundAmount() == null ? 0.0 : temp.getRefundAmount();
					refundAmount = ValueUtil.round(refundAmount);
					refundDiscount += temp.getRefundDiscount() == null ? 0.0 : temp.getRefundDiscount();
					refundDiscount = ValueUtil.round(refundDiscount);
					refundPaidAmount += temp.getRefundPaidAmount() == null ? 0.0 : temp.getRefundPaidAmount();
					refundPaidAmount = ValueUtil.round(refundPaidAmount);
					refundPointValue += temp.getRefundPointValue() == null ? 0 : temp.getRefundPointValue();

					totalQuantity = saleCount + refundCount;
					totalAmount = ValueUtil.round(amount + refundAmount);
					totalDisCount = ValueUtil.round(discount + refundDiscount);
					totalPaidAmount = ValueUtil.round(paidAmount + refundPaidAmount);
					totalPointValue = pointValue + refundPointValue;
				}
				reportStandSaleCheckGather.setSaleCount(saleCount);
				reportStandSaleCheckGather.setAmount(amount);
				reportStandSaleCheckGather.setDiscount(discount);
				reportStandSaleCheckGather.setPaidAmount(paidAmount);
				reportStandSaleCheckGather.setPointValue(pointValue);
				reportStandSaleCheckGather.setRefundCount(refundCount);
				reportStandSaleCheckGather.setRefundAmount(refundAmount);
				reportStandSaleCheckGather.setRefundDiscount(refundDiscount);
				reportStandSaleCheckGather.setRefundPaidAmount(refundPaidAmount);
				reportStandSaleCheckGather.setRefundPointValue(refundPointValue);
				reportStandSaleCheckGather.setTotalQuantity(totalQuantity);
				reportStandSaleCheckGather.setTotalAmount(totalAmount);
				reportStandSaleCheckGather.setTotalDiscount(totalDisCount);
				reportStandSaleCheckGather.setTotalPaidAmount(totalPaidAmount);
				reportStandSaleCheckGather.setTotalPointValue(totalPointValue);
				if (reportStandSaleCheckGather.getTotalQuantity() == 0) {
					reportStandSaleCheckGather.setUnitPointValue(0);
				} else {
					reportStandSaleCheckGather.setUnitPointValue(reportStandSaleCheckGather.getTotalPointValue() / reportStandSaleCheckGather.getTotalQuantity());
				}
				setOtherFields(reportStandSaleCheckGather, stockNumMap);
				resultList.add(reportStandSaleCheckGather);
			}
		}

		return ResultCode.getSuccessReturn(resultList);
	}

	private void setOtherFields(ReportMallOrderGatherVo vo, Map<Long, Map<String, Object>> stockNumMap) {
		if (!StringUtils.equals("mallcultural", vo.getOrderType()) || MapUtils.isEmpty(stockNumMap)) {
			return;
		}
		Long detailSkuId = vo.getDetailSkuId();
		Map<String, Object> numMap = (Map<String, Object>) MapUtils.getMap(stockNumMap, detailSkuId, new HashMap<>());
		vo.setStockNum(MapUtils.getInteger(numMap, "stockNum", 0));
		vo.setRemainingNum(MapUtils.getInteger(numMap, "remainingNum", 0));
	}

	@Override
	public ResultCode<List<ReportMallOrderGatherVo>> getCounts(AuthUser user, MallOrderGatherByDetailSear search) {
		if (search.getOrderTimeFrom() == null || search.getOrderTimeTo() == null) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		Long companyId = user.getCompanyId();
		search.setCompanyId(companyId);
		if(StringUtils.isNotBlank(search.getDetailProductIds())){
			List<Long> detailProductIds = commonUntransService.splitIds(search.getDetailProductIds());
			search.setDetailProductIdList(detailProductIds);
		}
		if(StringUtils.isNotBlank(search.getSaleUserGroupIds())){
			List<Long> saleUserGroupIds = commonUntransService.splitIds(search.getSaleUserGroupIds());
			search.setSaleUserGroupIdList(saleUserGroupIds);
		}
		if(StringUtils.equals("team", user.getUsertype())){
			search.setAgencyId(user.getMerchant().getAgencyId());
		}else{
			if(StringUtils.isNotBlank(search.getAgencyIds())){
				List<Long> AgencyIds = commonUntransService.splitIds(search.getAgencyIds());
				search.setAgencyIdList(AgencyIds);
			}
		}
		return this.updateStandSaleCheckGatherCount(search);
	}

	@Override
	public ResultCode<ReportMallOrderGatherVo> getTotals(AuthUser user, MallOrderGatherByDetailSear search){
		ReportMallOrderGatherVo result = new ReportMallOrderGatherVo();
		initNum(result);
		if (search.getOrderTimeFrom() == null || search.getOrderTimeTo() == null) {
			return ResultCode.getSuccessReturn(result);
		}
		Long companyId = user.getCompanyId();
		search.setCompanyId(companyId);
		if(StringUtils.isNotBlank(search.getDetailProductIds())){
			List<Long> detailProductIds = commonUntransService.splitIds(search.getDetailProductIds());
			search.setDetailProductIdList(detailProductIds);
		}
		if(StringUtils.isNotBlank(search.getSaleUserGroupIds())){
			List<Long> saleUserGroupIds = commonUntransService.splitIds(search.getSaleUserGroupIds());
			search.setSaleUserGroupIdList(saleUserGroupIds);
		}
		if(StringUtils.equals("team", user.getUsertype())){
			search.setAgencyId(user.getMerchant().getAgencyId());
		}else{
			if(StringUtils.isNotBlank(search.getAgencyIds())){
				List<Long> AgencyIds = commonUntransService.splitIds(search.getAgencyIds());
				search.setAgencyIdList(AgencyIds);
			}
		}
		ResultCode<List<ReportMallOrderGatherVo>> tmpCode = this.updateStandSaleCheckGatherCount(search);
		if(tmpCode.notSuccess() || CollectionUtils.isEmpty(tmpCode.getData())){
			return ResultCode.getSuccessReturn(result);
		}else{
			List<ReportMallOrderGatherVo> tmpList = tmpCode.getData();
			for(ReportMallOrderGatherVo tmp : tmpList){
				result.setSaleCount(result.getSaleCount() + tmp.getSaleCount());
				result.setAmount(result.getAmount() + tmp.getAmount());
				result.setDiscount(result.getDiscount() + tmp.getDiscount());
				result.setPaidAmount(result.getPaidAmount() + tmp.getPaidAmount());
				result.setPointValue(result.getPointValue() + tmp.getPointValue());
				result.setRefundCount(result.getRefundCount() + tmp.getRefundCount());
				result.setRefundAmount(result.getRefundAmount() + tmp.getRefundAmount());
				result.setRefundDiscount(result.getRefundDiscount() + tmp.getRefundDiscount());
				result.setRefundPaidAmount(result.getRefundPaidAmount() + tmp.getRefundPaidAmount());
				result.setRefundPointValue(result.getRefundPointValue() + tmp.getRefundPointValue());
				result.setTotalQuantity(result.getTotalQuantity() + tmp.getTotalQuantity());
				result.setTotalAmount(result.getTotalAmount() + tmp.getTotalAmount());
				result.setTotalDiscount(result.getTotalDiscount() + tmp.getTotalDiscount());
				result.setTotalPaidAmount(result.getTotalPaidAmount() + tmp.getTotalPaidAmount());
				result.setTotalPointValue(result.getTotalPointValue() + tmp.getTotalPointValue());
			}
			initNum(result);
		}
		return ResultCode.getSuccessReturn(result);
	}

	private void initNum(ReportMallOrderGatherVo result){
		if(null == result.getSaleCount()){
			result.setSaleCount(0);
		}
		if(null == result.getAmount()){
			result.setAmount(0.00);
		}else{
			result.setAmount(ValueUtil.round(result.getAmount()));
		}
		if(null == result.getDiscount()){
			result.setDiscount(0.00);
		}else{
			result.setDiscount(ValueUtil.round(result.getDiscount()));
		}
		if(null == result.getPaidAmount()){
			result.setPaidAmount(0.00);
		}else{
			result.setPaidAmount(ValueUtil.round(result.getPaidAmount()));
		}
		if(null == result.getRefundCount()){
			result.setRefundCount(0);
		}
		if(null == result.getRefundAmount()){
			result.setRefundAmount(0.00);
		}else{
			result.setRefundAmount(ValueUtil.round(result.getRefundAmount()));
		}
		if(null == result.getRefundDiscount()){
			result.setRefundDiscount(0.00);
		}else{
			result.setRefundDiscount(ValueUtil.round(result.getRefundDiscount()));
		}
		if(null == result.getRefundPaidAmount()){
			result.setRefundPaidAmount(0.00);
		}else{
			result.setRefundPaidAmount(ValueUtil.round(result.getRefundPaidAmount()));
		}
		if(null == result.getTotalQuantity()){
			result.setTotalQuantity(0);
		}
		if(null == result.getTotalAmount()){
			result.setTotalAmount(0.00);
		}else{
			result.setTotalAmount(ValueUtil.round(result.getTotalAmount()));
		}
		if(null == result.getTotalDiscount()){
			result.setTotalDiscount(0.00);
		}else{
			result.setTotalDiscount(ValueUtil.round(result.getTotalDiscount()));
		}
		if(null == result.getTotalPaidAmount()){
			result.setTotalPaidAmount(0.00);
		}else{
			result.setTotalPaidAmount(ValueUtil.round(result.getTotalPaidAmount()));
		}

		if(null == result.getPointValue()){
			result.setPointValue(0);
		}
		if(null == result.getRefundPointValue()){
			result.setRefundPointValue(0);
		}
		if(null == result.getTotalPointValue()){
			result.setTotalPointValue(0);
		}
	}
}

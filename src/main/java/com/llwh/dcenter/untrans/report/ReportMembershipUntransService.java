package com.llwh.dcenter.untrans.report;

import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearchMembership;
import com.llwh.dcenter.vo.report.ReportMemberShipVo;

public interface ReportMembershipUntransService {

	ResultCode<Map> getCounts(AuthUser user, SearchMembership search, Page<ReportMemberShipVo> page);
}

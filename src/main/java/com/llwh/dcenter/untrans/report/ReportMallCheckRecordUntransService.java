package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.ReportMallCheckRecordCount;
import com.llwh.dcenter.model.ReportMallCheckRecord;

/**
 * 商品核销报表
 * <AUTHOR>
 * @since 2024/4/8 19:54
 */
public interface ReportMallCheckRecordUntransService {
	void updateMallCheckRecordJob();

	void updateMallCheckRecordCount(Timestamp startTime, Timestamp endTime, String uuid);

	ResultCode<Page<ReportMallCheckRecord>> getCounts(AuthUser user, Page<ReportMallCheckRecord> page, ReportMallCheckRecordCount search);
}

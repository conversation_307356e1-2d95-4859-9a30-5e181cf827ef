package com.llwh.dcenter.untrans.report.impl;


import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.JsonUtils;
import cn.fancylab.util.VoCopyUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.base.constant.OrderType;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.DbHelper;
import com.llwh.dcenter.helper.DynamicFieldHepler;
import com.llwh.dcenter.helper.ReportMemberinfoStatisticsSearch;
import com.llwh.dcenter.model.ReportMemberinfoStatistics;
import com.llwh.dcenter.model.ReportOrderDetail;
import com.llwh.dcenter.service.report.DynamicFieldService;
import com.llwh.dcenter.service.report.ReportMemberinfoStatisticsService;
import com.llwh.dcenter.service.report.ReportOrderDetailService;
import com.llwh.dcenter.untrans.report.ReportMemberinfoStatisticsUntransService;
import com.llwh.dcenter.vo.common.DynamicFieldVo;
import com.llwh.dcenter.vo.report.ReportMemberOrderStatisticsVo;
import com.llwh.dcenter.vo.report.ReportMemberinfoStatisticsVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class ReportMemberinfoStatisticsUntransServiceImpl extends AbstractBaseReportUntransServiceImpl implements ReportMemberinfoStatisticsUntransService {

	@Autowired
	private ReportMemberinfoStatisticsService reportMemberinfoStatisticsService;

	@Autowired
	private ReportOrderDetailService reportOrderDetailService;

	@Autowired
	private DynamicFieldService dynamicFieldService;
	@Value("${business.ordertypes}")
	private List<String> ordertypes;


	private static final String UK = "ReportMemberinfoStatistics";
	private static final String INNER = "inner";
	private static final String LEFT = "left";

	private static int PAGE_SIZE = 500;

	@Override
	protected String getUk() {
		return UK;
	}

	@Override
	protected void updateReportCount(Long companyId, Timestamp startTime, Timestamp endTime) {
		updateReportMemberinfoStatisticsCount(companyId,startTime,endTime);
	}

	@Override
	public void updateReportMemberinfoStatisticsJob(String companyCodes) {
		updateReportJob(companyCodes);
	}

	@Override
	public void updateReportMemberinfoStatisticsCount(Long companyId,Timestamp startTime, Timestamp endTime) {
		DynamicFieldVo dynamicFieldVo = dynamicFieldService.getByTagAnCategoryFromThvendor(companyId, "personalInfo", "personalForm");
		Map<String,Object> fieldValueMap = new HashMap<>(0);
		if (dynamicFieldVo != null) {
			fieldValueMap = DynamicFieldHepler.getFieldCheckBoxValueMap(dynamicFieldVo.getFields());
		}
		processMemberInfoUpdate(companyId,startTime,endTime,fieldValueMap);
		if (ordertypes.contains(OrderType.TYPE_SHOW)) {
			processOrderInfoUpdate(companyId,startTime,endTime,"show");
		}
		if (ordertypes.contains(OrderType.TYPE_TICKET)) {
			processOrderInfoUpdate(companyId,startTime,endTime,"ticket");
		}
	}

	private void processMemberInfoUpdate(Long companyId,Timestamp startTime, Timestamp endTime,Map<String,Object> fieldValueMap) {
		Set<Long> dateSet = new LinkedHashSet<>();
		if (ordertypes.contains(OrderType.TYPE_SHOW)) {
			dateSet.addAll(reportMemberinfoStatisticsService.getShowMemberIds(companyId,startTime, endTime));
		}
		if (ordertypes.contains(OrderType.TYPE_TICKET)) {
			dateSet.addAll(reportMemberinfoStatisticsService.getTicketMemberIds(companyId,startTime,endTime));
		}
		dateSet.addAll(reportMemberinfoStatisticsService.getMemberInfoMemberIds(companyId,startTime,endTime));

		if (CollectionUtils.isEmpty(dateSet)) {
			return;
		}
		int totalCount = 0;
		int pageNo = 1;
		while (true){
			List<Long> memberIds = dateSet.stream().skip((pageNo - 1) * PAGE_SIZE).limit(PAGE_SIZE).collect(Collectors.toList());
			if (memberIds.size()<=0){
				break;
			}
			List<ReportMemberinfoStatisticsVo> list = reportMemberinfoStatisticsService.getReportMemberinfoStatisticsVo(companyId,memberIds);
			totalCount += list.size();
			List<ReportMemberOrderStatisticsVo> showOrderStatisticsVos = new ArrayList<>();
			if (ordertypes.contains(OrderType.TYPE_SHOW)) {
				showOrderStatisticsVos =reportOrderDetailService.getByShowMemberIds(companyId,memberIds);
			}
			List<ReportMemberOrderStatisticsVo> ticketOrderStatisticsVos = new ArrayList<>();
			if (ordertypes.contains(OrderType.TYPE_TICKET)) {
				ticketOrderStatisticsVos = reportOrderDetailService.getByTicketMemberIds(companyId,memberIds);
			}
			Map<Long,ReportMemberOrderStatisticsVo> showStatisticsMemberMap = BeanUtil.beanListToMap(showOrderStatisticsVos, "memberId");
			Map<Long,ReportMemberOrderStatisticsVo> ticketStatisticsMemberMap = BeanUtil.beanListToMap(ticketOrderStatisticsVos, "memberId");
			List<ReportMemberinfoStatistics> memberList = new ArrayList<>(list.size());
			for(ReportMemberinfoStatisticsVo item : list){
				ReportMemberinfoStatistics member = new ReportMemberinfoStatistics();
				VoCopyUtil.copyFromObj(member,item);
				Long memberId = item.getMemberId();
				member.setUpdatetime(DateUtil.getCurFullTimestamp());
				member.setId(item.getCompanyId() + "_" + memberId);
				ReportMemberOrderStatisticsVo showMemberOrderStatisticsVo = showStatisticsMemberMap.get(memberId);
				ReportMemberOrderStatisticsVo ticketMemberOrderStatisticsVo = ticketStatisticsMemberMap.get(memberId);
				int buyCount = 0;
				int buyScheduleCount = 0;
				double expendTotalAmount = 0;
				if (showMemberOrderStatisticsVo != null ){
					buyCount += showMemberOrderStatisticsVo.getBuyCount();
					buyScheduleCount += showMemberOrderStatisticsVo.getBuyScheduleCount();
					expendTotalAmount += showMemberOrderStatisticsVo.getExpendTotalAmount();
				}
				if (ticketMemberOrderStatisticsVo != null) {
					buyCount += ticketMemberOrderStatisticsVo.getBuyCount();
					buyScheduleCount += ticketMemberOrderStatisticsVo.getBuyScheduleCount();
					expendTotalAmount += ticketMemberOrderStatisticsVo.getExpendTotalAmount();
				}
				member.setBuyCount(buyCount);
				member.setBuyScheduleCount(buyScheduleCount);
				member.setExpendTotalAmount(expendTotalAmount);
				String dynamicJsonValue = item.getDynamicJsonValue();
				if (StringUtils.isNotBlank(dynamicJsonValue)){
					Map map = JsonUtils.readJsonToMap(dynamicJsonValue);
					String favorite = DynamicFieldHepler.getFieldCheckBoxValue(fieldValueMap, "favorite", MapUtils.getString(map, "favorite"));
					member.setFavorite(favorite);
					String memberActivitieType = DynamicFieldHepler.getFieldCheckBoxValue(fieldValueMap, "memberActivitieType", MapUtils.getString(map, "memberActivitieType"));
					member.setMemberActivitieType(memberActivitieType);
					String price = DynamicFieldHepler.getFieldCheckBoxValue(fieldValueMap, "price", MapUtils.getString(map, "price"));
					member.setPreferredPrice(price);
					member.setAddress(MapUtils.getString(map,"address"));
					member.setPostcard(MapUtils.getString(map,"postcard"));
					member.setTelephone(MapUtils.getString(map,"telephone"));
				}
				memberList.add(member);
			}
			DbHelper.splitBachSave(memberList,reportMemberinfoStatisticsService::saveOrUpdateBatch);
			pageNo++;
			if (memberIds.size()<PAGE_SIZE){
				break;
			}
		}
		dbLogger.warn("刷新会员统计分析：{},{},{}", startTime, endTime, totalCount);
	}
	private void processOrderInfoUpdate(Long companyId,Timestamp startTime, Timestamp endTime, String ticketType) {
		int totalCount = 0;
		int pageNo = 1;

		while (true){
			List<ReportOrderDetail> list = "show".equals(ticketType)?reportOrderDetailService.getPageShowReportOrderDetailCount(companyId,startTime,endTime,(pageNo-1) * PAGE_SIZE,PAGE_SIZE)
					: reportOrderDetailService.getPageTicketReportOrderDetailCount(companyId,startTime,endTime,(pageNo-1) * PAGE_SIZE,PAGE_SIZE);
			if (CollectionUtils.isEmpty(list)){
				break;
			}
			list.forEach(item ->{
				item.setId(item.getCompanyId() + "_" + item.getOrderDetailId());
			});
			totalCount += list.size();
			DbHelper.splitBachSave(list,reportOrderDetailService::saveOrUpdateBatch);
			if (list.size() < PAGE_SIZE){
				break;
			}
			pageNo++;
		}
		dbLogger.warn("刷新会员+" + ticketType + "订单统计分析：{},{},{}", startTime, endTime, totalCount);
	}

	@Override
	public ResultCode<Page<ReportMemberinfoStatisticsVo>> getCounts(AuthUser user, Page<ReportMemberinfoStatisticsVo> page, ReportMemberinfoStatisticsSearch search) {
		search.setCompanyId(user.getCompanyId());
		search.setPays(null);
		String joinType = LEFT;
		if (search.getOrderStartTime() != null){
			joinType = INNER;
		}else if (CollectionUtils.isNotEmpty(search.getScheduleIds())){
			joinType = INNER;
		}else if (StringUtils.isNotBlank(search.getPayType())) {
			joinType = INNER;
		}else if (StringUtils.isNotBlank(search.getPlatform())) {
			joinType = INNER;
		}
		return ResultCode.getSuccessReturn(reportMemberinfoStatisticsService.getCountBySearch(search, joinType ,page));
	}

	@Override
	public List<List<Object>> getTotalCount(AuthUser user, ReportMemberinfoStatisticsSearch search) {
		List<List<Object>> resultList = new ArrayList<>();
		Page page = new Page(1,100000);
		ResultCode<Page<ReportMemberinfoStatisticsVo> > counts = getCounts(user, page, search);
		List<ReportMemberinfoStatisticsVo> result = counts.getData().getRecords();

		for (ReportMemberinfoStatisticsVo detail : result) {
			List<Object> objList = new ArrayList<>();
			objList.add(detail.getRealname());
			objList.add(ReportConstant.genderMap.get(detail.getGender()));
			objList.add(detail.getCardNo());
			objList.add(detail.getMemberLevelName());
			objList.add(detail.getExpendTotalAmount());
			objList.add(detail.getBuyScheduleCount());
			objList.add(detail.getBuyCount());
			objList.add(detail.getMobile());
			objList.add(detail.getTelephone());
			objList.add(detail.getEmail());
			objList.add(detail.getAddress());
			objList.add(detail.getPostcard());
			if (detail.getBirthday() != null) {
				objList.add(DateUtil.format(DateUtil.getDateFromTimestamp(detail.getBirthday()),"yyyy-MM-dd"));
			}else {
				objList.add(null);
			}
			objList.add(detail.getSearchExpendTotalAmount());
			objList.add(detail.getPoint());
			objList.add(DateUtil.format(detail.getActiveTime(),"yyyy-MM-dd"));
			objList.add(DateUtil.format(detail.getEndTime(),"yyyy-MM-dd"));
			objList.add(detail.getFavorite());
			objList.add(detail.getMemberActivitieType());
			objList.add(detail.getPreferredPrice());
			resultList.add(objList);
		}
		return resultList;
	}
}

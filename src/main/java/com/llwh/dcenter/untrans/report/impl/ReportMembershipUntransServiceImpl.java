package com.llwh.dcenter.untrans.report.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearchMembership;
import com.llwh.dcenter.vo.report.ReportMemberShipVo;
import com.llwh.dcenter.service.report.ReportMembershipService;
import com.llwh.dcenter.untrans.report.ReportMembershipUntransService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON><PERSON> on 2023/7/20.
 */
@Service
public class ReportMembershipUntransServiceImpl implements ReportMembershipUntransService {
	@Autowired
	private ReportMembershipService reportMembershipService;
	@Override
	public ResultCode<Map> getCounts(AuthUser user, SearchMembership search, Page<ReportMemberShipVo> page) {
		search.setCompanyId(user.getCompanyId());
		IPage<ReportMemberShipVo> counts = reportMembershipService.getCounts(search, page);

		Page<ReportMemberShipVo> pageResult = new Page<>(page.getCurrent(), page.getSize());
		List<ReportMemberShipVo> records = counts.getRecords();
		pageResult.setTotal(page.getTotal());
		Map result = new HashMap();
		result.put("page", pageResult);
		result.put("resultList", records);
		return ResultCode.getSuccessReturn(result);
	}
}

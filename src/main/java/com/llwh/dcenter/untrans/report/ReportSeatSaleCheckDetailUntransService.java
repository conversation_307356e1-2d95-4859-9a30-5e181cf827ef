package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.model.ReportSeatSaleCheckDetail;
import com.llwh.dcenter.vo.ReportSeatSaleCheckDetailVo;
import com.llwh.dcenter.vo.report.ReportSeatDetailTotalVo;

public interface ReportSeatSaleCheckDetailUntransService {

	void updateSeatSaleCheckDetailJob();

	void updateSeatSaleCheckDetailCount(Timestamp startTime, Timestamp endTime, String tradeNo, String uuid);

	ResultCode<Page<ReportSeatSaleCheckDetail>> getCounts(AuthUser user, SearReportStandCheckDetailCount search, Page<ReportSeatSaleCheckDetail> page);
	ResultCode<Page<ReportSeatSaleCheckDetailVo>> getCountsBySql(AuthUser user, SearReportStandCheckDetailCount search, Page<ReportSeatSaleCheckDetailVo> page);

	List<List<Object>> getTotalCount(AuthUser user, SearReportStandCheckDetailCount search);

	ResultCode<ReportSeatDetailTotalVo> getTotals(AuthUser user, SearReportStandCheckDetailCount search);

	ResultCode<Map> getTodayTotalsCount(Long companyId, Date date);

	void refreshHistories();

	void updateSeatSaleCheckDetailFields();

	void updateSeatSaleCheckDetailStatus();
}

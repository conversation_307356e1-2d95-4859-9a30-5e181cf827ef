package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;
import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.llwh.dcenter.helper.SearMemberInfoCountByDay;
import com.llwh.dcenter.model.ReportMemberLevelCountByDay;

public interface ReportMemberLevelCountByDayUntransService {

	void updateMemberLevelCountByDayJob();

	void updateMemberLevelCountByDay(Timestamp starttime, Timestamp endtime);

	ResultCode<List<ReportMemberLevelCountByDay>> getCounts(AuthUser user, SearMemberInfoCountByDay search);


}

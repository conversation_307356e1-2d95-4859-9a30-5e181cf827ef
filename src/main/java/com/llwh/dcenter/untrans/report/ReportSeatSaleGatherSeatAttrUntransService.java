package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.llwh.dcenter.helper.ReportOverallCount;

public interface ReportSeatSaleGatherSeatAttrUntransService {
	void updateSeatSaleGatherSeatAttrJob();

	void updateSeatSaleGatherSeatAttrCount(Timestamp startTime, Timestamp endTime, String tradeNo, Long scheduleId);

	void updateCountByScheduleIds(List<Long> scheduleIds);

	ResultCode<?> getTotals(AuthUser user, ReportOverallCount search);

	ResultCode<Map<String, Object>> getCounts(AuthUser user, ReportOverallCount search);
}

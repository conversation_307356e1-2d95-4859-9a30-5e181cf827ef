package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearHuifuDetail;
import com.llwh.dcenter.model.ReportHuifuDetail;

public interface ReportHuifuDetailUntransService {

	void updateHuifuDetailJob();

	void updateHuifuDetailCount(Timestamp startTime, Timestamp endTime, String tradeNo);

	ResultCode<Map> getCounts(AuthUser user, SearHuifuDetail search, Page<ReportHuifuDetail> page);

	ResultCode<Map> getGroupHuifu(AuthUser user, SearHuifuDetail search);
}

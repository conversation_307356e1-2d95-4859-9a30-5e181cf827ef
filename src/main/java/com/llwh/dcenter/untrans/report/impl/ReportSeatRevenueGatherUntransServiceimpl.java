package com.llwh.dcenter.untrans.report.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.VoCopyUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.base.constant.GroupType;
import com.llwh.base.constant.Platform;
import com.llwh.dcenter.helper.SearReportStandSaleCount;
import com.llwh.dcenter.model.ReportSeatSaleGatherDay;
import com.llwh.dcenter.service.report.ReportSeatSaleGatherDayService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatRevenueGatherUntransService;
import com.llwh.dcenter.vo.common.UserGroupVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportSeatRevenueGatherUntransServiceimpl implements ReportSeatRevenueGatherUntransService {
	@Autowired
	private ReportSeatSaleGatherDayService seatSaleGatherDayService;
	@Autowired
	private DataLevelUntransService dataLevelUntransService;
	@Autowired
	private CommonUntransService commonUntransService;

	@Override
	public ResultCode<Page<ReportSeatSaleGatherDay>> getCounts(AuthUser user, Page<ReportSeatSaleGatherDay> page, SearReportStandSaleCount search) {
		if ((search.getDatefrom() == null || search.getDateto() == null) &&
				search.getProgramId() == null) {
			return ResultCodeHelper.CODE11_PARAMS_REQUIRED("出票日期、项目，两个筛选条件至少选择一个");
		}
		dataLevelUntransService.getDataLevelSearch(search, user, true, true);
		search.setCompanyId(user.getCompanyId());
		if(StringUtils.isNotBlank(search.getProgramCode())){
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		if(StringUtils.isNotBlank(search.getCityName())){
			search.setCityName("%" + search.getCityName() + "%");
		}
		Page<ReportSeatSaleGatherDay> pageResult = seatSaleGatherDayService.getRevenueCounts(page, search);
		fillSoruce(pageResult);
		return ResultCode.getSuccessReturn(pageResult);
	}

	@Override
	public List<List<Object>> getTotalCount(AuthUser user, SearReportStandSaleCount search) {
		List<List<Object>> resultList = new ArrayList<>();
		if ((search.getDatefrom() == null || search.getDateto() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return resultList;
		}
		search.setCompanyId(user.getCompanyId());
		if (search.getDateto() != null) {
			search.setDateto(DateUtil.addDay(search.getDateto(), 1));
		}
		if(StringUtils.isNotBlank(search.getProgramCode())){
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		if(StringUtils.isNotBlank(search.getCityName())){
			search.setCityName("%" + search.getCityName() + "%");
		}
		List<ReportSeatSaleGatherDay> tempList = seatSaleGatherDayService.getRevenueTotalCount(search);
		for (ReportSeatSaleGatherDay gather : tempList) {
			List<Object> objList = new ArrayList<>();
			objList.add(gather.getTicketDate());
			objList.add(gather.getCityName());
			objList.add(gather.getProgramId());
			objList.add(gather.getProgramCode());
			objList.add(gather.getProgramName());
			objList.add(gather.getUserGroupName());
			objList.add(gather.getStadiumName());
			objList.add(gather.getVenueName());
			objList.add(gather.getSaleCount());
			objList.add(gather.getPaidAmount());
			objList.add(gather.getRefundCount());
			objList.add(gather.getRefundPaidAmount());
			objList.add(gather.getTotalQuantity());
			objList.add(gather.getTotalPaidAmount());
			resultList.add(objList);
		}
		return resultList;
	}

	@Override
	public ResultCode<ReportSeatSaleGatherDay> getTotals(AuthUser user, SearReportStandSaleCount search){
		ReportSeatSaleGatherDay result = new ReportSeatSaleGatherDay();
		if ((search.getDatefrom() == null || search.getDateto() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			initNum(result);
			return ResultCode.getSuccessReturn(result);
		}
		search.setCompanyId(user.getCompanyId());
		search.setDateto(DateUtil.addDay(search.getDateto(), 1));
		if(StringUtils.isNotBlank(search.getProgramCode())){
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		if(StringUtils.isNotBlank(search.getCityName())){
			search.setCityName("%" + search.getCityName() + "%");
		}
		ReportSeatSaleGatherDay tmp = seatSaleGatherDayService.getRevenueTotals(search);
		if(ObjectUtils.isEmpty(tmp)){
			initNum(result);
		}else{
			VoCopyUtil.copyFromObj(result,tmp);
			initNum(result);
		}
		return ResultCode.getSuccessReturn(result);
	}

	/**
	 * 将来源渠道映射成中文
	 * @param pageResult
	 */
	private void fillSoruce(Page<ReportSeatSaleGatherDay> pageResult) {
		List<ReportSeatSaleGatherDay> records = pageResult.getRecords();
		if (CollectionUtils.isEmpty(records)){
			return;
		}
		List<Long> userGroupIds = new ArrayList<>();
		List<UserGroupVo> userGroups = new ArrayList<>();
		for (ReportSeatSaleGatherDay vo : records) {
			if (StringUtils.isBlank(vo.getSource())) {
				continue;
			}
			if (!StringUtils.equals("online", vo.getSource())) {
				userGroupIds.add(Long.valueOf(vo.getSourceChannel()));
			}
		}
		if(CollectionUtils.isNotEmpty(userGroupIds)){
			userGroups = commonUntransService.getUserGroupByIds(userGroupIds);
		}
		Map<Long, UserGroupVo> userGroupMap = BeanUtil.beanListToMap(userGroups, "id");
		for (ReportSeatSaleGatherDay vo : records) {
			if (StringUtils.equals("online", vo.getSource())) {
				vo.setSource("线上自营");
				vo.setSourceChannel(MapUtils.getString(Platform.ONLINE_PLATFORM_MAP, vo.getSourceChannel()));
			} else if (StringUtils.equals(GroupType.OTA, vo.getSource())) {
				vo.setSource("线上代理");
				vo.setSourceChannel(userGroupMap.get(Long.valueOf(vo.getSourceChannel())).getUserGroupName());
			} else if (StringUtils.equals(GroupType.AGENT, vo.getSource())) {
				vo.setSource("线下代理");
				vo.setSourceChannel(userGroupMap.get(Long.valueOf(vo.getSourceChannel())).getUserGroupName());
			} else if (StringUtils.equals("offline", vo.getSource())) {
				vo.setSource("线下票房");
				vo.setSourceChannel(userGroupMap.get(Long.valueOf(vo.getSourceChannel())).getUserGroupName());
			}
		}
	}

	private void initNum(ReportSeatSaleGatherDay result){
		if(null == result.getSaleCount()){
			result.setSaleCount(0);
		}
		if(null == result.getPaidAmount()){
			result.setPaidAmount(0.00);
		}
		if(null == result.getRefundCount()){
			result.setRefundCount(0);
		}
		if(null == result.getRefundPaidAmount()){
			result.setRefundPaidAmount(0.00);
		}
		if(null == result.getTotalQuantity()){
			result.setTotalQuantity(0);
		}
		if(null == result.getTotalPaidAmount()){
			result.setTotalPaidAmount(0.00);
		}
	}
}

package com.llwh.dcenter.untrans.report.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.fancylab.api.thvendor.general.api.OriginChannelApiService;
import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.ValueUtil;

import com.google.common.collect.Lists;
import com.llwh.dcenter.helper.ReportSeatProgramOriginGatherSearch;
import com.llwh.dcenter.service.report.ReportSeatProgramOriginGatherService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatProgramOriginGatherUntransService;
import com.llwh.dcenter.vo.common.ProgramSettleVo;
import com.llwh.dcenter.vo.common.ScheduleVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ReportSeatProgramOriginGatherUntransServiceImpl implements ReportSeatProgramOriginGatherUntransService {
	private static final String ORIGIN = "origin";
	private static final String PROGRAM_ID = "programId";
	private static final String SHOW_ID = "showId";
	@Autowired
	private DataLevelUntransService dataLevelUntransService;
	@Autowired
	private ReportSeatProgramOriginGatherService reportSeatProgramOriginGatherService;
	@DubboReference(version = "1.0")
	private OriginChannelApiService originChannelApiService;
	@Autowired
	private CommonUntransService commonUntransService;

	@Override
	public ResultCode<Map> getCounts(AuthUser user, ReportSeatProgramOriginGatherSearch search) {
		dataLevelUntransService.getDataLevelSearch(search, user, true, true);
		if (StringUtils.isNotBlank(search.getOrigins())) {
			String[] originArray = StringUtils.split(search.getOrigins(), ",");
			List<String> originList = Lists.newArrayList(originArray);
			search.setOriginList(originList);
		}

		Map model = new HashMap<>(2);
		model.put("originList", new ArrayList<>());
		model.put("totalList", new ArrayList<>());
		Map totalMap = new HashMap();
		totalMap.put("quantity", 0);
		totalMap.put("amount", 0.0);
		totalMap.put("discount", 0.0);
		totalMap.put("paidAmount", 0.0);
		model.put("totalMap", totalMap);

		List<Map<String, Object>> counts = reportSeatProgramOriginGatherService.getCounts(user.getCompanyId(), search);
		if (CollectionUtils.isEmpty(counts)) {
			return ResultCode.getSuccessReturn(model);
		}

		Map<String, String> originRemarkMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(counts)) {
			ResultCode<Map<String, String>> originRemarkRes = originChannelApiService.getOriginRemarkMap(user.getCompanyId());
			if (originRemarkRes.isSuccess()) {
				originRemarkMap = originRemarkRes.getData();
			}
		}
		List<Long> programIds = BeanUtil.getBeanPropertyList(counts, PROGRAM_ID, true);
		List<ProgramSettleVo> programVos = commonUntransService.listProgramStadium(programIds);
		Map<Long, ProgramSettleVo> programVoMap = BeanUtil.beanListToMap(programVos, PROGRAM_ID);

		List<Long> showIds = BeanUtil.getBeanPropertyList(counts, SHOW_ID, true);
		List<ScheduleVo> schedules = commonUntransService.getSchedules(showIds);
		Map<Long, ScheduleVo> scheduleVoMap = BeanUtil.beanListToMap(schedules, "id");

		Map<String, List<Map<String, Object>>> program2ShowListMap = BeanUtil.groupBeanListByFun(counts, e -> e.get(ORIGIN) + "#" + e.get(PROGRAM_ID), "");
		List<Map<String, Object>> programList = new ArrayList<>(program2ShowListMap.size());
		for (Map.Entry<String, List<Map<String, Object>>> entry : program2ShowListMap.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> valueList = entry.getValue();

			String[] split = key.split("#");
			String origin = split[0];
			Long programId = Long.valueOf(split[1]);
			Map programMap = new HashMap();
			programMap.put(ORIGIN, origin);
			programMap.put(PROGRAM_ID, programId);
			ProgramSettleVo programVo = programVoMap.get(programId);
			if (programVo != null) {
				programMap.put("programName", programVo.getProgramName());
			}

			int quantityTotal = 0;
			double amountTotal = 0.0;
			double discountTotal = 0.0;
			double paidAmountTotal = 0.0;
			for (Map<String, Object> map : valueList) {
				Long showId = (Long) map.get(SHOW_ID);
				if (showId != null) {
					ScheduleVo scheduleVo = scheduleVoMap.get(showId);
					if (scheduleVo != null) {
						map.put("playTime", scheduleVo.getPlayTime());
						map.put("showName", scheduleVo.getShowName());
					}
				}
				quantityTotal += MapUtils.getIntValue(map, "quantity", 0);
				amountTotal = ValueUtil.round(amountTotal + MapUtils.getDoubleValue(map, "amount", 0.0));
				discountTotal = ValueUtil.round(discountTotal + MapUtils.getDoubleValue(map, "discount", 0.0));
				paidAmountTotal = ValueUtil.round(paidAmountTotal + MapUtils.getDoubleValue(map, "paidAmount", 0.0));
			}
			Map<String, Object> totalMap4s = new HashMap<>();
			totalMap4s.put("quantity", quantityTotal);
			totalMap4s.put("amount", amountTotal);
			totalMap4s.put("discount", discountTotal);
			totalMap4s.put("paidAmount", paidAmountTotal);

			programMap.put("showList", valueList);
			programMap.put("totalMap", totalMap4s);
			programList.add(programMap);
		}

		Map<String, List<Map<String, Object>>> origin2ProgramListMap = BeanUtil.groupBeanList(programList, ORIGIN);
		List<Map<String, Object>> originList = new ArrayList<>(origin2ProgramListMap.size());
		List<Map<String, Object>> totalList = new ArrayList<>(origin2ProgramListMap.size());
		int quantityTotal4Total = 0;
		double amountTotal4Total = 0.0;
		double discountTotal4Total = 0.0;
		double paidAmountTotal4Total = 0.0;
		for (Map.Entry<String, List<Map<String, Object>>> entry : origin2ProgramListMap.entrySet()) {
			String key = entry.getKey();
			List<Map<String, Object>> value = entry.getValue();

			String originRemark = MapUtils.getString(originRemarkMap, key, key);

			HashMap<String, Object> originMap = new HashMap<>();
			originMap.put(ORIGIN, key);
			originMap.put("originRemark", originRemark);
			originMap.put("programList", value);
			originList.add(originMap);

			int quantityTotal = 0;
			double amountTotal = 0.0;
			double discountTotal = 0.0;
			double paidAmountTotal = 0.0;
			for (Map<String, Object> map : value) {
				Map<String, Object> totalMap4program = (Map<String, Object>) MapUtils.getMap(map, "totalMap", new HashMap<>());
				quantityTotal += MapUtils.getIntValue(totalMap4program, "quantity", 0);
				amountTotal = ValueUtil.round(amountTotal + MapUtils.getDoubleValue(totalMap4program, "amount", 0.0));
				discountTotal = ValueUtil.round(discountTotal + MapUtils.getDoubleValue(totalMap4program, "discount", 0.0));
				paidAmountTotal = ValueUtil.round(paidAmountTotal + MapUtils.getDoubleValue(totalMap4program, "paidAmount", 0.0));
			}
			Map<String, Object> originMap4Total = new HashMap<>();
			originMap4Total.put(ORIGIN, key);
			originMap4Total.put("originRemark", originRemark);
			originMap4Total.put("quantity", quantityTotal);
			originMap4Total.put("amount", amountTotal);
			originMap4Total.put("discount", discountTotal);
			originMap4Total.put("paidAmount", paidAmountTotal);
			totalList.add(originMap4Total);

			quantityTotal4Total += quantityTotal;
			amountTotal4Total = ValueUtil.round(amountTotal4Total + amountTotal);
			discountTotal4Total = ValueUtil.round(discountTotal4Total + discountTotal);
			paidAmountTotal4Total = ValueUtil.round(paidAmountTotal4Total + paidAmountTotal);
		}
		model.put("originList", originList);
		model.put("totalList", totalList);
		totalMap.put("quantity", quantityTotal4Total);
		totalMap.put("amount", amountTotal4Total);
		totalMap.put("discount", discountTotal4Total);
		totalMap.put("paidAmount", paidAmountTotal4Total);
		model.put("totalMap", totalMap);
		return ResultCode.getSuccessReturn(model);
	}
}

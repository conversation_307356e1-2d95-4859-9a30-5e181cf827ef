package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.ticket.Status;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.JsonUtils;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.ValueUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.llwh.base.constant.DiscountCategory;
import com.llwh.base.constant.Platform;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.SearMallOrderDetailCount;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportMallDiscountApportionment;
import com.llwh.dcenter.model.ReportMallOrderDetail;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportMallDiscountApportionmentService;
import com.llwh.dcenter.service.report.ReportMallOrderDetailService;
import com.llwh.dcenter.service.report.ReportMemberInfoService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.untrans.report.ReportMallOrderDetailUntransService;
import com.llwh.dcenter.vo.common.ApiUserVo;
import com.llwh.dcenter.vo.common.UserGroupVo;
import com.llwh.dcenter.vo.report.ReportMemberLevelCardNoVo;
import com.llwh.dcenter.vo.report.ReportMemberSaleDetailVo;
import com.llwh.dcenter.vo.report.UserMallOrderTotalVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportMallOrderDetailUntransServiceImpl implements ReportMallOrderDetailUntransService {
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());

	private static final String UK = "ReportMallPointOrderDetail";
	@Autowired
	private ReportMallOrderDetailService mallOrderDetailService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	@Autowired
	private ReportMemberInfoService reportMemberInfoService;
	@Autowired
	private CommonUntransService commonUntransService;
	@Autowired
	private ReportMallDiscountApportionmentService reportMallDiscountApportionmentService;

	@Override
	public void updateMallOrderDetailJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			startTime = DateUtil.addMinute(record.getLastUpdateTime(), ReportConstant.UPDATE_REPEAT_MINUTE);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = DateUtil.getCurTruncTimestamp();
		}
		try {
			updateMallOrderDetailCount(startTime, endTime, null);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateMallOrderDetailCount(Timestamp startTime, Timestamp endTime, String tradeNo) {
		List<ReportMallOrderDetail> countList = new ArrayList<>();
		List<ReportMallOrderDetail> seatCount = mallOrderDetailService.getMallOrderCount(startTime, endTime, tradeNo);
		if (CollectionUtils.isNotEmpty(seatCount)) {
			countList.addAll(seatCount);
		}

		List<ReportMallOrderDetail> refundSeatCount = mallOrderDetailService.getRefundMallOrderCount(startTime, endTime, tradeNo);
		if (CollectionUtils.isNotEmpty(refundSeatCount)) {
			countList.addAll(refundSeatCount);
		}
		List<Long> memberIds = BeanUtil.getBeanPropertyList(countList,"memberId",true);
		List<Long> companyIds = BeanUtil.getBeanPropertyList(countList,"companyId",true);
		Map<String, ReportMemberLevelCardNoVo> cardNoMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(memberIds)) {
			List<ReportMemberLevelCardNoVo> memberCardNos = reportMemberInfoService.getMemberCardNos(memberIds, companyIds, endTime);
			cardNoMap = memberCardNos.stream().collect(Collectors.toMap(item ->{
				return item.getCompanyId() + "" + item.getMemberId();
			} , item -> item, (oldObj, newObj) -> newObj));
		}

		List<Long> saleUserIds = BeanUtil.getBeanPropertyList(countList, "saleUserId", true);
		List<Long> saleUserGroupIds = BeanUtil.getBeanPropertyList(countList, "saleUserGroupId", true);
		Map<Long, String> saleUserNameMap = new HashMap<>();
		Map<Long, ApiUserVo> apiUserGuid2VoMap = new HashMap<>();
		Map<Long, String> saleUserGroupNameMap = new HashMap<>();
		Map<Long, String> saleUserGroupTypeMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(saleUserIds)) {
			List<ReportMemberSaleDetailVo> userNames = commonUntransService.getUserNames(saleUserIds);
			saleUserNameMap = BeanUtil.beanListToMap(userNames, "tbsUserId", "userName", true);
			List<ApiUserVo> apiUsersByGuids = commonUntransService.getApiUsersByGuids(saleUserIds);
			apiUserGuid2VoMap = BeanUtil.beanListToMap(apiUsersByGuids, "guid");
		}
		if (CollectionUtils.isNotEmpty(saleUserGroupIds)) {
			List<UserGroupVo> userGroupByIds = commonUntransService.getUserGroupByIds(saleUserGroupIds);
			saleUserGroupNameMap = BeanUtil.beanListToMap(userGroupByIds, "id", "userGroupName", true);
			saleUserGroupTypeMap = BeanUtil.beanListToMap(userGroupByIds, "id", "userGroupType", true);
		}

		Timestamp now = DateUtil.getCurFullTimestamp();
		for (ReportMallOrderDetail count : countList){
			if(StringUtils.isNotBlank(count.getSkuName())){
				String skuName = "";
				List<Map> skuNameMaps = JsonUtils.readJsonToObjectList(Map.class, count.getSkuName());
				int i = 0;
				for(Map map : skuNameMaps){
					i++;
					skuName += MapUtils.getString(map, "value");
					if(i < skuNameMaps.size()){
						skuName += "-";
					}
				}
				count.setSkuName(skuName);
			}
			ReportMemberLevelCardNoVo reportMemberLevelCardNoVo = cardNoMap.get(count.getCompanyId() + "" + count.getMemberId());
			if (reportMemberLevelCardNoVo != null) {
				count.setMemberId(reportMemberLevelCardNoVo.getMemberId());
				count.setCardNo(reportMemberLevelCardNoVo.getCardNo());
				if(reportMemberLevelCardNoVo.getMembershipGrowthValue() != null && reportMemberLevelCardNoVo.getGrowthValue() != null &&
						reportMemberLevelCardNoVo.getMembershipGrowthValue().compareTo(reportMemberLevelCardNoVo.getGrowthValue()) > 0){
					count.setMemberLevelId(reportMemberLevelCardNoVo.getMembershipLevelId());
					count.setMemberLevelName(reportMemberLevelCardNoVo.getMembershipLevelName());
				} else if (reportMemberLevelCardNoVo.getMembershipGrowthValue() != null && reportMemberLevelCardNoVo.getGrowthValue() == null) {
					count.setMemberLevelId(reportMemberLevelCardNoVo.getMembershipLevelId());
					count.setMemberLevelName(reportMemberLevelCardNoVo.getMembershipLevelName());
				} else{
					count.setMemberLevelId(reportMemberLevelCardNoVo.getMemberLevelId());
					count.setMemberLevelName(reportMemberLevelCardNoVo.getMemberLevelName());
				}
				count.setMemberStatus(reportMemberLevelCardNoVo.getStatus());
				count.setRealname(reportMemberLevelCardNoVo.getRealname());
			}
			count.setUpdatetime(now);
			count.setSaleUserName(saleUserNameMap.get(count.getSaleUserId()));
			count.setSaleUserGroupName(saleUserGroupNameMap.get(count.getSaleUserGroupId()));
			setOtherFields(count, apiUserGuid2VoMap, saleUserGroupTypeMap);
		}
		mallOrderDetailService.saveOrUpdateBatch(countList);
		dbLogger.warn("刷新积分商城订单明细表：{} ~ {} {} {}", startTime, endTime, tradeNo, countList.size());
	}

	private void setOtherFields(ReportMallOrderDetail count, Map<Long, ApiUserVo> apiUserGuid2VoMap, Map<Long, String> saleUserGroupTypeMap) {
		if (apiUserGuid2VoMap.containsKey(count.getSaleUserId())) {
			count.setSourceNew("线上自营");
		} else if (StringUtils.equals(saleUserGroupTypeMap.get(count.getSaleUserGroupId()), "ota")) {
			count.setSourceNew("线上代理");
		} else if (StringUtils.equals(saleUserGroupTypeMap.get(count.getSaleUserGroupId()), "agent")) {
			count.setSourceNew("线下代理");
		} else if (StringUtils.equals(count.getPlatform(), "OFFLINE")) {
			count.setSourceNew("线下票房");
		}

		if (apiUserGuid2VoMap.containsKey(count.getSaleUserId())) {
			count.setSourceChannel(MapUtils.getString(Platform.PLATFORM_MAP, count.getPlatform(), count.getPlatform()));
		} else if (StringUtils.equalsAny(saleUserGroupTypeMap.get(count.getSaleUserGroupId()), "ota", "agent")
				|| StringUtils.equals(count.getPlatform(), "OFFLINE")) {
			count.setSourceChannel(count.getSaleUserGroupName());
		}
	}


	@Override
	public ResultCode<Page<ReportMallOrderDetail>> getCounts(AuthUser user, SearMallOrderDetailCount search, Page<ReportMallOrderDetail> page) {
		if (search.getStartTime() == null || search.getEndTime() == null) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		QueryWrapper<ReportMallOrderDetail> wrapper = new QueryWrapper<>();
		wrapper.eq("company_id", user.getCompanyId());
		wrapper.eq("order_type", search.getOrderType());
		wrapper.orderByAsc("order_time","updatetime","id");
		if (ObjectUtils.isNotEmpty(search.getStartTime())) {
			wrapper.ge("order_time", search.getStartTime());
		}
		if (ObjectUtils.isNotEmpty(search.getEndTime())) {
			wrapper.le("order_time", search.getEndTime());
		}
		if(StringUtils.isNotBlank(search.getTradeNo())){
			wrapper.eq("trade_no", search.getTradeNo());
		}
		if(StringUtils.isNotBlank(search.getMainTradeNo())){
			wrapper.eq("main_trade_no", search.getMainTradeNo());
		}
		if(StringUtils.isNotBlank(search.getMobile())){
			wrapper.eq("mobile", search.getMobile());
		}
		if(StringUtils.isNotBlank(search.getMemberName())){
			wrapper.like("member_name", "%" + search.getMemberName() + "%");
		}
		if(StringUtils.isNotBlank(search.getRealname())){
			wrapper.like("realname", "%" + search.getRealname() + "%");
		}
		if(null != search.getDetailProductId()){
			wrapper.eq("detail_product_id", search.getDetailProductId());
		}
		if(StringUtils.isNotBlank(search.getGatewayCode())){
			wrapper.eq("gateway_code", search.getGatewayCode());
		}
		if(null != search.getSaleUserGroupId()){
			wrapper.eq("sale_user_group_id", search.getSaleUserGroupId());
		}
		if(null != search.getSaleUserId()){
			wrapper.eq("sale_user_id", search.getSaleUserId());
		}
		if(StringUtils.isNotBlank(search.getDetailProductIds())){
			List<Long> detailProductIds = BeanUtil.getIdList(search.getDetailProductIds(), ",");
			search.setDetailProductIdList(detailProductIds);
			wrapper.in("detail_product_id", search.getDetailProductIdList());
		}
		if(StringUtils.isNotBlank(search.getSaleUserGroupIds())){
			List<Long> saleUserGroupIds = BeanUtil.getIdList(search.getSaleUserGroupIds(), ",");
			search.setSaleUserGroupIdList(saleUserGroupIds);
			wrapper.in("sale_user_group_id", search.getSaleUserGroupIds());
		}
		if(StringUtils.isNotBlank(search.getMemberLevelIds())){
			List<Long> memberLevelIdList = BeanUtil.getIdList(search.getMemberLevelIds(), ",");
			wrapper.in("member_level_id", memberLevelIdList);
			wrapper.eq("member_status", Status.Y);
		}
		if (StringUtils.equals("team", user.getUsertype())) {
			wrapper.eq("agency_id", user.getMerchant().getAgencyId());
		} else {
			if (StringUtils.isNotBlank(search.getAgencyIds())) {
				List<Long> agencyIdsList = BeanUtil.getIdList(search.getAgencyIds(), ",");
				wrapper.in("agency_id", agencyIdsList);
			}
		}
		Page<ReportMallOrderDetail> pageResult = mallOrderDetailService.page(page, wrapper);

		for(ReportMallOrderDetail detail : pageResult.getRecords()){
			if(!Status.isY(detail.getMemberStatus())){
				detail.setMemberLevelName("");
				detail.setCardNo("");
			}
			detail.setUnitPrice(Math.abs(detail.getUnitPrice()));
			Integer pointValue = detail.getPointValue() == null ? 0 : detail.getPointValue();
			detail.setUnitPointValue(Math.abs(pointValue / detail.getQuantity()));
		}

		if(pageResult.getRecords().size() > 1000){
			List<List<ReportMallOrderDetail>> allDetails = Lists.partition(pageResult.getRecords(), 1000);
			for(List<ReportMallOrderDetail> details : allDetails){
				combineDiscount(details);
			}
		}else{
			combineDiscount(pageResult.getRecords());
		}

		return ResultCode.getSuccessReturn(pageResult);
	}

	private void combineDiscount(List<ReportMallOrderDetail> details){
		List<Long> itemIds = BeanUtil.getBeanPropertyList(details, "itemId", true);
		if(CollectionUtils.isEmpty(itemIds)){
			return;
		}
		QueryWrapper<ReportMallDiscountApportionment> discountWrapper = new QueryWrapper<>();
		discountWrapper.in("item_id", itemIds);
		List<ReportMallDiscountApportionment> discountList = reportMallDiscountApportionmentService.list(discountWrapper);
		Map<String, List<ReportMallDiscountApportionment>> discountMap = BeanUtil.groupBeanListByFun(discountList, discount -> discount.getPayType() + discount.getItemId(), "");
		for(ReportMallOrderDetail detail : details){
			List<ReportMallDiscountApportionment> discounts = discountMap.get(detail.getPayType() + detail.getItemId());
			if(CollectionUtils.isEmpty(discounts)){
				continue;
			}
			for (ReportMallDiscountApportionment discount : discounts) {
				if (DiscountCategory.isMemership(discount.getCategory())) {
					detail.setDiscountMembership(ValueUtil.roundDouble(detail.getDiscountMembership() + discount.getDiscountAmount()));
					detail.setDiscountMembershipDesc(discount.getDescription());
				} else if (DiscountCategory.isCoupon(discount.getCategory())) {
					detail.setDiscountCoupon(ValueUtil.roundDouble(detail.getDiscountCoupon() + discount.getDiscountAmount()));
					detail.setDiscountCouponDesc(discount.getDescription());
				} else if (DiscountCategory.isPoints(discount.getCategory())) {
					detail.setDiscountPoints(ValueUtil.roundDouble(detail.getDiscountPoints() + discount.getDiscountAmount()));
					detail.setDiscountPointsDesc(discount.getDescription());
				} else if (DiscountCategory.isPromotion(discount.getCategory())) {
					detail.setDiscountPromotion(ValueUtil.roundDouble(detail.getDiscountPromotion() + discount.getDiscountAmount()));
					detail.setDiscountPromotionDesc(discount.getDescription());
				} else if (DiscountCategory.isRule(discount.getCategory())) {
					detail.setDiscountRule(ValueUtil.roundDouble(detail.getDiscountRule() + discount.getDiscountAmount()));
					detail.setDiscountRuleDesc(discount.getDescription());
				} else if (DiscountCategory.isMultiCard(discount.getCategory())) {
					detail.setDiscountMulticard(ValueUtil.roundDouble(detail.getDiscountMulticard() + discount.getDiscountAmount()));
					detail.setDiscountMulticardDesc(discount.getDescription());
				} else if (DiscountCategory.isBalance(discount.getCategory())) {
					detail.setDiscountBalance(ValueUtil.roundDouble(detail.getDiscountBalance() + discount.getDiscountAmount()));
					detail.setDiscountBalanceDesc(discount.getDescription());
				} else if (DiscountCategory.isSamePrice(discount.getCategory())) {
					detail.setDiscountSameprice(ValueUtil.roundDouble(detail.getDiscountSameprice() + discount.getDiscountAmount()));
					detail.setDiscountSamepriceDesc(discount.getDescription());
				}
			}
		}
	}

	@Override
	public ResultCode<Map> getUserTotalCount(AuthUser user, String orderType){
		Timestamp endtime = DateUtil.getCurFullTimestamp();
		Timestamp starttime = DateUtil.getBeginTimestamp(endtime);
		List<UserMallOrderTotalVo> totalList = new ArrayList<>();
		List<UserMallOrderTotalVo> saleList = mallOrderDetailService.getUserMallOrderCount(starttime, endtime, user.getUserId(), user.getUsertype(), user.getCompanyId(), orderType);
		if (CollectionUtils.isNotEmpty(saleList)) {
			totalList.addAll(saleList);
		}
		List<UserMallOrderTotalVo> refundList = mallOrderDetailService.getUserMallOrderRefundCount(starttime, endtime, user.getUserId(), user.getUsertype(), user.getCompanyId(), orderType);
		if (CollectionUtils.isNotEmpty(refundList)) {
			totalList.addAll(refundList);
		}
		if(CollectionUtils.isEmpty(totalList)){
			return ResultCode.SUCCESS;
		}
		Map<String, List<UserMallOrderTotalVo>> stringListMap = BeanUtil.groupBeanList(totalList, "gatewayCode");
		List<UserMallOrderTotalVo> resultList = new ArrayList<>();
		for (String id : stringListMap.keySet()) {
			List<UserMallOrderTotalVo> countList = stringListMap.get(id);

			if (countList.size() == 1) {
				UserMallOrderTotalVo count = countList.get(0);
				count.setGatewayCode(id);
				Integer saleQuantity = count.getSaleQuantity();
				if (saleQuantity == null) {
					saleQuantity = 0;
					count.setSaleQuantity(saleQuantity);
				}
				Double saleAmount = count.getSaleAmount();
				if (saleAmount == null) {
					saleAmount = 0.0;
					count.setSaleAmount(ValueUtil.roundDouble(saleAmount));
				}
				Double saleDiscount = count.getSaleDiscount();
				if (saleDiscount == null) {
					saleDiscount = 0.0;
					count.setSaleDiscount(ValueUtil.roundDouble(saleDiscount));
				}
				Double salePaidAmount = count.getSalePaidAmount();
				if (salePaidAmount == null) {
					salePaidAmount = 0.0;
					count.setSalePaidAmount(ValueUtil.roundDouble(salePaidAmount));
				}
				Integer refundQuantity = count.getRefundQuantity();
				if (refundQuantity == null) {
					refundQuantity = 0;
					count.setRefundQuantity(refundQuantity);
				}
				Double refundAmount = count.getRefundAmount();
				if (refundAmount == null) {
					refundAmount = 0.0;
					count.setRefundAmount(ValueUtil.roundDouble(refundAmount));
				}
				Double refundDiscount = count.getRefundDiscount();
				if (refundDiscount == null) {
					refundDiscount = 0.0;
					count.setRefundDiscount(ValueUtil.roundDouble(refundDiscount));
				}
				Double refundPaidAmount = count.getRefundPaidAmount();
				if (refundPaidAmount == null) {
					refundPaidAmount = 0.0;
					count.setRefundPaidAmount(ValueUtil.roundDouble(refundPaidAmount));
				}

				count.setTotalQuantity(saleQuantity + refundQuantity);
				count.setTotalAmount(ValueUtil.roundDouble(saleAmount + refundAmount));
				count.setTotalDiscount(ValueUtil.roundDouble(saleDiscount + refundDiscount));
				count.setTotalPaidAmount(ValueUtil.roundDouble(salePaidAmount + refundPaidAmount));
				resultList.add(count);
			} else {
				Integer saleQuantity = 0;
				Double saleAmount = 0.0;
				Double saleDiscount = 0.0;
				Double salePaidAmount = 0.0;

				Integer refundQuantity = 0;
				Double refundAmount = 0.0;
				Double refundDiscount = 0.0;
				Double refundPaidAmount = 0.0;

				Integer totalQuantity = 0;
				Double  totalAmount = 0.0;
				Double totalDisCount = 0.0;
				Double totalPaidAmount = 0.0;
				UserMallOrderTotalVo reportStandSaleCheckGather = countList.get(0);
				reportStandSaleCheckGather.setGatewayCode(id);
				for (UserMallOrderTotalVo temp : countList) {
					saleQuantity += temp.getSaleQuantity() == null ? 0 : temp.getSaleQuantity();
					saleAmount += temp.getSaleAmount() == null ? 0.0 : temp.getSaleAmount();
					saleDiscount += temp.getSaleDiscount() == null ? 0.0 : temp.getSaleDiscount();
					salePaidAmount += temp.getSalePaidAmount() == null ? 0.0 : temp.getSalePaidAmount();

					refundQuantity += temp.getRefundQuantity() == null ? 0 : temp.getRefundQuantity();
					refundAmount += temp.getRefundAmount() == null ? 0.0 : temp.getRefundAmount();
					refundDiscount += temp.getRefundDiscount() == null ? 0.0 : temp.getRefundDiscount();
					refundPaidAmount += temp.getRefundPaidAmount() == null ? 0.0 : temp.getRefundPaidAmount();

					totalQuantity = saleQuantity + refundQuantity;
					totalAmount = saleAmount + refundAmount;
					totalDisCount = saleDiscount + refundDiscount;
					totalPaidAmount = salePaidAmount + refundPaidAmount;
				}
				reportStandSaleCheckGather.setSaleQuantity(saleQuantity);
				reportStandSaleCheckGather.setSaleAmount(ValueUtil.roundDouble(saleAmount));
				reportStandSaleCheckGather.setSaleDiscount(ValueUtil.roundDouble(saleDiscount));
				reportStandSaleCheckGather.setSalePaidAmount(ValueUtil.roundDouble(salePaidAmount));
				reportStandSaleCheckGather.setRefundQuantity(refundQuantity);
				reportStandSaleCheckGather.setRefundAmount(ValueUtil.roundDouble(refundAmount));
				reportStandSaleCheckGather.setRefundDiscount(ValueUtil.roundDouble(refundDiscount));
				reportStandSaleCheckGather.setRefundPaidAmount(ValueUtil.roundDouble(refundPaidAmount));
				reportStandSaleCheckGather.setTotalQuantity(totalQuantity);
				reportStandSaleCheckGather.setTotalAmount(ValueUtil.roundDouble(totalAmount));
				reportStandSaleCheckGather.setTotalDiscount(ValueUtil.roundDouble(totalDisCount));
				reportStandSaleCheckGather.setTotalPaidAmount(ValueUtil.roundDouble(totalPaidAmount));
				resultList.add(reportStandSaleCheckGather);
			}
		}

		Map gatewayCodeIncome = new HashMap<>(resultList.size());
		Integer saleQuantity = 0;
		Integer refundQuantity = 0;
		Double salePaidAmount = 0.00;
		Double refundPaidAmount = 0.00;
		Double totalAmount = 0.00;
		Double totalDisCount = 0.00;
		Double totalPaidAmount = 0.00;
		for(UserMallOrderTotalVo vo : resultList){
			gatewayCodeIncome.put(vo.getGatewayCode(), vo.getTotalPaidAmount());
			saleQuantity +=  vo.getSaleQuantity();
			refundQuantity += vo.getRefundQuantity();
			salePaidAmount += vo.getSalePaidAmount();
			refundPaidAmount += vo.getRefundPaidAmount();
			totalAmount += vo.getTotalAmount();
			totalDisCount += vo.getTotalDiscount();
			totalPaidAmount += vo.getTotalPaidAmount();
		}
		Map resultMap = new HashMap(8);
		resultMap.put("gatewayCodeIncome", gatewayCodeIncome);
		resultMap.put("saleQuantity", saleQuantity);
		resultMap.put("refundQuantity", refundQuantity);
		resultMap.put("salePaidAmount", ValueUtil.roundDouble(salePaidAmount));
		resultMap.put("refundPaidAmount", ValueUtil.roundDouble(refundPaidAmount));
		resultMap.put("totalAmount", ValueUtil.roundDouble(totalAmount));
		resultMap.put("totalDisCount", ValueUtil.roundDouble(totalDisCount));
		resultMap.put("totalPaidAmount", ValueUtil.roundDouble(totalPaidAmount));
		return ResultCode.getSuccessReturn(resultMap);
	}

	@Override
	public void updateReportMallOrderDetailFields() {
		int pageSize = 500;
		for (int i = 1; i < 100000; i++) {
			Page<ReportMallOrderDetail> page = new Page<>(i, pageSize);
			Page<ReportMallOrderDetail> detailPage = mallOrderDetailService.pagingQuery(page);
			List<ReportMallOrderDetail> recordList = detailPage.getRecords();
			if (CollectionUtils.isEmpty(recordList)) {
				return;
			}
			Map<String, List<ReportMallOrderDetail>> payTypeListMap = BeanUtil.groupBeanList(recordList, "payType");
			List<ReportMallOrderDetail> payList = payTypeListMap.get("pay");
			List<ReportMallOrderDetail> refundList = payTypeListMap.get("refund");
			Map<String, ReportMallOrderDetail> detailMap4pay = new HashMap<>();
			Map<String, ReportMallOrderDetail> detailMap4refund = new HashMap<>();
			if (CollectionUtils.isNotEmpty(payList)) {
				// sale54764
				List<Long> omsOrderItemIdList = payList.stream().map(e -> Long.valueOf(e.getId().substring(4))).collect(Collectors.toList());
				List<ReportMallOrderDetail> mallOrderFields = mallOrderDetailService.getMallOrderFields(omsOrderItemIdList);
				detailMap4pay = BeanUtil.beanListToMap(mallOrderFields, "id");
			}
			if (CollectionUtils.isNotEmpty(refundList)) {
				// refund688
				List<Long> orderRefundDetailIdList = refundList.stream().map(e -> Long.valueOf(e.getId().substring(6))).collect(Collectors.toList());
				List<ReportMallOrderDetail> mallRefundFields = mallOrderDetailService.getMallRefundFields(orderRefundDetailIdList);
				detailMap4refund = BeanUtil.beanListToMap(mallRefundFields, "id");
			}

			List<Long> saleUserIds = BeanUtil.getBeanPropertyList(recordList, "saleUserId", true);
			List<Long> saleUserGroupIds = BeanUtil.getBeanPropertyList(recordList, "saleUserGroupId", true);
			Map<Long, String> saleUserNameMap = new HashMap<>();
			Map<Long, ApiUserVo> apiUserGuid2VoMap = new HashMap<>();
			Map<Long, String> saleUserGroupNameMap = new HashMap<>();
			Map<Long, String> saleUserGroupTypeMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(saleUserIds)) {
				List<ReportMemberSaleDetailVo> userNames = commonUntransService.getUserNames(saleUserIds);
				saleUserNameMap = BeanUtil.beanListToMap(userNames, "tbsUserId", "userName", true);
				List<ApiUserVo> apiUsersByGuids = commonUntransService.getApiUsersByGuids(saleUserIds);
				apiUserGuid2VoMap = BeanUtil.beanListToMap(apiUsersByGuids, "guid");
			}
			if (CollectionUtils.isNotEmpty(saleUserGroupIds)) {
				List<UserGroupVo> userGroupByIds = commonUntransService.getUserGroupByIds(saleUserGroupIds);
				saleUserGroupNameMap = BeanUtil.beanListToMap(userGroupByIds, "id", "userGroupName", true);
				saleUserGroupTypeMap = BeanUtil.beanListToMap(userGroupByIds, "id", "userGroupType", true);
			}

			for (ReportMallOrderDetail detail : recordList) {
				setFields(detail, detailMap4pay, detailMap4refund);
				detail.setSaleUserName(saleUserNameMap.get(detail.getSaleUserId()));
				detail.setSaleUserGroupName(saleUserGroupNameMap.get(detail.getSaleUserGroupId()));
				setOtherFields(detail, apiUserGuid2VoMap, saleUserGroupTypeMap);
			}
			mallOrderDetailService.saveOrUpdateBatch(recordList);
			dbLogger.warn("商城订单明细表刷新历史数据新增字段！{} {}", page.getCurrent(), recordList.size());
			if (recordList.size() < pageSize) {
				return;
			}
		}
	}

	private void setFields(ReportMallOrderDetail detail, Map<String, ReportMallOrderDetail> detailMap4pay, Map<String, ReportMallOrderDetail> detailMap4refund) {
		if(StringUtils.equals("pay", detail.getPayType())) {
			ReportMallOrderDetail detail4pay = detailMap4pay.get(detail.getId());
			doSetFields(detail, detail4pay);
		} else if(StringUtils.equals("refund", detail.getPayType())) {
			ReportMallOrderDetail detail4refund = detailMap4refund.get(detail.getId());
			doSetFields(detail, detail4refund);
		}
	}

	private void doSetFields(ReportMallOrderDetail detail, ReportMallOrderDetail detail4selected) {
		detail.setPaidtime(detail4selected.getPaidtime());
		detail.setPlatform(detail4selected.getPlatform());
	}

	@Override
	public void updateReportMallOrderDetailOrigin() {
		int pageSize = 500;
		for (int i = 1; i < 100000; i++) {
			Page<ReportMallOrderDetail> page = new Page<>(i, pageSize);
			Page<ReportMallOrderDetail> detailPage = mallOrderDetailService.pagingQuery(page);
			List<ReportMallOrderDetail> recordList = detailPage.getRecords();
			if (CollectionUtils.isEmpty(recordList)) {
				return;
			}
			Map<String, List<ReportMallOrderDetail>> payTypeListMap = BeanUtil.groupBeanList(recordList, "payType");
			List<ReportMallOrderDetail> payList = payTypeListMap.get("pay");
			List<ReportMallOrderDetail> refundList = payTypeListMap.get("refund");
			int updatedCount = 0;
			if (CollectionUtils.isNotEmpty(payList)) {
				// sale54764
				List<Long> omsOrderItemIdList = payList.stream().map(e -> Long.valueOf(e.getId().substring(4))).collect(Collectors.toList());
				List<ReportMallOrderDetail> mallOrderOrigins = mallOrderDetailService.getMallOrderOrigin(omsOrderItemIdList);
				for (ReportMallOrderDetail mallOrderOrigin : mallOrderOrigins) {
					LambdaUpdateWrapper<ReportMallOrderDetail> updateWrapper = new LambdaUpdateWrapper<>();
					updateWrapper.eq(ReportMallOrderDetail::getId, mallOrderOrigin.getId());
					updateWrapper.set(ReportMallOrderDetail::getOrigin, mallOrderOrigin.getOrigin());
					mallOrderDetailService.update(null, updateWrapper);
				}
				updatedCount += mallOrderOrigins.size();
			}
			if (CollectionUtils.isNotEmpty(refundList)) {
				// refund688
				List<Long> orderRefundDetailIdList = refundList.stream().map(e -> Long.valueOf(e.getId().substring(6))).collect(Collectors.toList());
				List<ReportMallOrderDetail> mallRefundOrigins = mallOrderDetailService.getMallRefundOrigin(orderRefundDetailIdList);
				for (ReportMallOrderDetail mallOrderOrigin : mallRefundOrigins) {
					LambdaUpdateWrapper<ReportMallOrderDetail> updateWrapper = new LambdaUpdateWrapper<>();
					updateWrapper.eq(ReportMallOrderDetail::getId, mallOrderOrigin.getId());
					updateWrapper.set(ReportMallOrderDetail::getOrigin, mallOrderOrigin.getOrigin());
					mallOrderDetailService.update(null, updateWrapper);
				}
				updatedCount += mallRefundOrigins.size();
			}
			dbLogger.warn("商城订单明细表刷新历史数据origin字段！{} {} {}", page.getCurrent(), recordList.size(), updatedCount);
			if (recordList.size() < pageSize) {
				return;
			}
		}
	}
}

package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.StringUtil;
import cn.fancylab.util.VoCopyUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.api.mall.api.MallOrderDiscountDubboService;
import com.llwh.api.mall.vo.discount.DiscountApportionmentVo;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.SearMallOrderDiscount;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportMallDiscountApportionment;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportMallDiscountApportionmentService;
import com.llwh.dcenter.untrans.report.ReportMallDiscountApportionmentUntransService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportMallDiscountApportionmentUntransServiceImpl implements ReportMallDiscountApportionmentUntransService {

	@Autowired
	private ReportMallDiscountApportionmentService reportMallDiscountApportionmentService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	private static final String UK = "ReportMallDiscountApportionment";
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());
	@DubboReference(version = "1.0")
	private MallOrderDiscountDubboService mallOrderDiscountDubboService;
	private  static int SIZE = 100;

	@Override
	public void updateMallDiscountApportionmentJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			startTime = DateUtil.addMinute(record.getLastUpdateTime(), ReportConstant.UPDATE_REPEAT_MINUTE);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = DateUtil.getCurTruncTimestamp();
		}
		try {
			updateMallDiscountApportionment(startTime, endTime, null, null);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateMallDiscountApportionment(Timestamp startTime, Timestamp endTime, String tradeNo, String serialNo) {
		List<ReportMallDiscountApportionment> countList = new ArrayList<>();
		if(StringUtils.isAllBlank(tradeNo, serialNo) || (StringUtils.isNotBlank(tradeNo) && StringUtils.isBlank(serialNo))){
			List<Map> seatTradeNos = reportMallDiscountApportionmentService.getMallTradeNo(startTime, endTime, tradeNo);
			if(CollectionUtils.isNotEmpty(seatTradeNos)){
				Map<Object, List> map = BeanUtil.groupBeanProperty(seatTradeNos, "companyId", "tradeNo");
				Timestamp now = DateUtil.getCurFullTimestamp();
				for(Object companyId : map.keySet()){
					List<String> tradeNos = map.get(companyId);
					int pages = tradeNos.size() / SIZE + 1;
					for(int i = 0; i < pages; i++) {
						int offset = i * SIZE;
						List<String> subList =  tradeNos.stream().skip(offset).limit(SIZE).collect(Collectors.toList());
						ResultCode<List<DiscountApportionmentVo>> code = mallOrderDiscountDubboService.getDiscountApportionment((Long)companyId, subList);
						if(code.isSuccess()){
							List<DiscountApportionmentVo> voList = code.getData();
							if(CollectionUtils.isNotEmpty(voList)){
								for(DiscountApportionmentVo vo : voList){
									ReportMallDiscountApportionment apportionment = new ReportMallDiscountApportionment();
									VoCopyUtil.copyExclude(apportionment, vo, "id");
									apportionment.setId(StringUtil.md5(vo.getId() + "-" + vo.getItemId() + "-pay", 35));
									apportionment.setPayType("pay");
									apportionment.setDiscountId(vo.getId());
									apportionment.setUpdatetime(now);
									countList.add(apportionment);
								}
							}
						}
					}
				}
			}
		}



		if(StringUtils.isAllBlank(tradeNo, serialNo) || StringUtils.isNotBlank(serialNo)){
			List<Map> seatRefundSerialNos = reportMallDiscountApportionmentService.getMallRefundSerialNo(startTime, endTime, serialNo);
			if(CollectionUtils.isNotEmpty(seatRefundSerialNos)){
				Map<Object, List> map = BeanUtil.groupBeanProperty(seatRefundSerialNos, "companyId", "serialNo");
				Timestamp now = DateUtil.getCurFullTimestamp();
				for(Object companyId : map.keySet()){
					List<String> serialNos = map.get(companyId);
					int pages = serialNos.size() / SIZE + 1;
					for (int i = 0; i < pages; i++) {
						int offset = i * SIZE;
						List<String> subList = serialNos.stream().skip(offset).limit(SIZE).collect(Collectors.toList());
						ResultCode<List<DiscountApportionmentVo>> code = mallOrderDiscountDubboService.getRefundDiscountApportionment((Long) companyId, subList);
						if (code.isSuccess()) {
							List<DiscountApportionmentVo> voList = code.getData();
							if (CollectionUtils.isNotEmpty(voList)) {
								for (DiscountApportionmentVo vo : voList) {
									ReportMallDiscountApportionment apportionment = new ReportMallDiscountApportionment();
									VoCopyUtil.copyExclude(apportionment, vo, "id");
									apportionment.setId(StringUtil.md5(vo.getId() + "-" + vo.getItemId() + "-refund", 35));
									apportionment.setPayType("refund");
									apportionment.setDiscountId(vo.getId());
									apportionment.setUpdatetime(now);
									countList.add(apportionment);
								}
							}
						}
					}
				}
			}
		}


		if(CollectionUtils.isEmpty(countList)){
			return;
		}
		reportMallDiscountApportionmentService.saveOrUpdateBatch(countList);
		if(StringUtils.isBlank(tradeNo)){
			dbLogger.warn("刷新商城优惠分摊表：{}~{} {}", startTime, endTime, countList.size());
		}
	}


	@Override
	public ResultCode<Page<ReportMallDiscountApportionment>> getCounts(AuthUser user, SearMallOrderDiscount search, Page<ReportMallDiscountApportionment> page) {
		if (search.getStartTime() == null || search.getEndTime() == null) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		QueryWrapper<ReportMallDiscountApportionment> wrapper = new QueryWrapper<>();
		wrapper.eq("company_id", user.getCompanyId());
		wrapper.orderByDesc("addtime","id");

		if (StringUtils.isNotBlank(search.getTradeNo())) {
			wrapper.eq("trade_no", search.getTradeNo());
		}
		if (search.getItemId() == null) {
			wrapper.eq("itemId", search.getItemId());
		}

		if (ObjectUtils.isNotEmpty(search.getStartTime())) {
			wrapper.ge("addtime", search.getStartTime());
		}
		if (ObjectUtils.isNotEmpty(search.getEndTime())) {
			wrapper.le("addtime", search.getEndTime());
		}


		Page<ReportMallDiscountApportionment> pageResult = reportMallDiscountApportionmentService.page(page, wrapper);

		return ResultCode.getSuccessReturn(pageResult);
	}


}

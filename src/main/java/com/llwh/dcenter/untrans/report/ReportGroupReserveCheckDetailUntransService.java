package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;
import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearchGroupReserveCheckDetail;
import com.llwh.dcenter.model.ReportGroupReserveCheckDetail;

public interface ReportGroupReserveCheckDetailUntransService{

		void updateGroupReserveCheckDetailJob();

		void updateGroupReserveCheckDetailCount(Timestamp startTime, Timestamp endTime);

		ResultCode<Page<ReportGroupReserveCheckDetail>> getCounts(Page<ReportGroupReserveCheckDetail> page, SearchGroupReserveCheckDetail search, AuthUser user);

		List<List<Object>> getTotalCount(AuthUser user, SearchGroupReserveCheckDetail search);
}

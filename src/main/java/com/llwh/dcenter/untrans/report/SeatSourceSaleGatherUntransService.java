package com.llwh.dcenter.untrans.report;

import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.llwh.dcenter.helper.ProgramStatisticsSearch;
import com.llwh.dcenter.vo.programStatistics.SourceChannelGatherVo;

public interface SeatSourceSaleGatherUntransService {
		ResultCode<List<SourceChannelGatherVo>> getCounts(AuthUser user, ProgramStatisticsSearch search);
		ResultCode<List<SourceChannelGatherVo>> getScheduleCounts(AuthUser user, ProgramStatisticsSearch search);

	ResultCode<List<SourceChannelGatherVo>> getProgramCounts(AuthUser user, ProgramStatisticsSearch search);
}

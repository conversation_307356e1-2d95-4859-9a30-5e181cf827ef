package com.llwh.dcenter.untrans.report;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearchReserveCheckDetail;
import com.llwh.dcenter.model.ReportReserveCheckDetail;

public interface ReportReserveCheckDetailUntransService {

	void updateReserveCheckDetailJob();

	void updateReserveCheckDetailCount(Timestamp startTime, Timestamp endTime);

	ResultCode<Map> getCounts(AuthUser user,SearchReserveCheckDetail search, Page<ReportReserveCheckDetail> page);

	List<List<Object>> getTotalCount(AuthUser user, SearchReserveCheckDetail search,Page page);

	void updateReserveTypeEntrynum();

	ResultCode<Page<ReportReserveCheckDetail>> getCounts4New(AuthUser user, SearchReserveCheckDetail search, Page<ReportReserveCheckDetail> page);

	ResultCode<Map> getTotals4New(AuthUser user, SearchReserveCheckDetail search);
}

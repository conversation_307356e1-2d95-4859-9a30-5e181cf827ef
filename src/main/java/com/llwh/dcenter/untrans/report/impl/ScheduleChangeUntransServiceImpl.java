package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.List;

import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;

import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportSeatSaleGatherSeatAttrService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherSeatAttrUntransService;
import com.llwh.dcenter.untrans.report.ScheduleChangeUntransService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON><PERSON> on 2023/12/25.
 */
@Service
public class ScheduleChangeUntransServiceImpl implements ScheduleChangeUntransService {
	private static final String UK = "scheduleChange";
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());
	@Autowired
	private ReportSeatSaleGatherSeatAttrService reportSeatSaleGatherSeatAttrService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	@Autowired
	private ReportSeatSaleGatherSeatAttrUntransService seatSaleGatherSeatAttrUntransService;

	@Override
	public void updateScheduleChangeJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			//座位划价后刷新报表，定时任务执行需要较短的间隔
			startTime = DateUtil.addSecond(record.getLastUpdateTime(), ReportConstant.UPDATE_REPEAT_MINUTE);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = DateUtil.getCurTruncTimestamp();
		}
		try {
			updateSeatSaleGatherSeatAttrByScheduleIds(startTime, endTime);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateSeatSaleGatherSeatAttrByScheduleIds(Timestamp startTime, Timestamp endTime) {
		List<Long> scheduleIds = reportSeatSaleGatherSeatAttrService.getScheduleChange(startTime, endTime);
		if(CollectionUtils.isNotEmpty(scheduleIds)){
			seatSaleGatherSeatAttrUntransService.updateCountByScheduleIds(scheduleIds);
		}
	}

}

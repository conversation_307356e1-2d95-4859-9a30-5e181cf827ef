package com.llwh.dcenter.untrans.report.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.JsonUtils;
import cn.fancylab.util.LoggerUtils;
import cn.fancylab.util.StringUtil;
import cn.fancylab.util.VoCopyUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.base.constant.OrderType;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.SearReportMulticardDetail;
import com.llwh.dcenter.model.ReportCountUpdateRecord;
import com.llwh.dcenter.model.ReportMulticardDetail;
import com.llwh.dcenter.service.ReportCountUpdateRecordService;
import com.llwh.dcenter.service.report.ReportMulticardDetailService;
import com.llwh.dcenter.untrans.report.ReportMulticardDetailUntransService;
import com.llwh.dcenter.vo.report.ReportMulticardDetailTotalVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class ReportMulticardDetailUntransServiceImpl implements ReportMulticardDetailUntransService {

	@Autowired
	private ReportMulticardDetailService multicardDetailService;
	@Autowired
	private ReportCountUpdateRecordService countUpdateRecordService;
	@Value("${business.ordertypes}")
	private List<String> ordertypes;
	private static final String UK = "ReportMulticardDetail";
	private final transient Logger dbLogger = LoggerUtils.getLogger(getClass());

	@Override
	public void updateMulticardDetailJob() {
		ReportCountUpdateRecord record = countUpdateRecordService.getById(UK);
		Timestamp startTime;
		Timestamp endTime = DateUtil.getCurFullTimestamp();
		if (record != null && record.getLastUpdateTime() != null) {
			startTime = DateUtil.addMinute(record.getLastUpdateTime(), ReportConstant.UPDATE_REPEAT_MINUTE);
			record.setErrorMsg(null);
		} else {
			record = new ReportCountUpdateRecord(UK);
			startTime = DateUtil.getCurTruncTimestamp();
		}
		try {
			updateMulticardDetailCount(startTime, endTime);
			record.setLastUpdateTime(endTime);
		} catch (Throwable e) {
			String exceptionTrace = LoggerUtils.getExceptionTrace(e, 40);
			dbLogger.error(exceptionTrace);
			record.setErrorTime(DateUtil.getCurFullTimestamp());
			record.setErrorMsg(exceptionTrace);
		}
		countUpdateRecordService.saveOrUpdate(record);
	}

	@Override
	public void updateMulticardDetailCount(Timestamp startTime, Timestamp endTime) {
		List<ReportMulticardDetail> countList = new ArrayList<>();

		List<ReportMulticardDetail> payList = getPayList(startTime, endTime);
		if (CollectionUtils.isNotEmpty(payList)) {
			countList.addAll(payList);
		}
		List<ReportMulticardDetail> refundList = getRefundList(startTime, endTime);
		if (CollectionUtils.isNotEmpty(refundList)) {
			countList.addAll(refundList);
		}

		if (ordertypes.contains("dams")) {
			List<ReportMulticardDetail> payDamsList = getDamsPayList(startTime, endTime);
			if (CollectionUtils.isNotEmpty(payDamsList)) {
				countList.addAll(payDamsList);
			}
			List<ReportMulticardDetail> refundDamsList = getDamsRefundList(startTime, endTime);
			if (CollectionUtils.isNotEmpty(refundDamsList)) {
				countList.addAll(refundDamsList);
			}
		}

		if (CollectionUtils.isEmpty(countList)) {
			return;
		}

		conbineTotalNum(countList);
		conbineMembership(countList);

		Timestamp now = DateUtil.getCurFullTimestamp();
		for (ReportMulticardDetail count : countList) {
			count.setId(StringUtil.md5(count.getPayType() + "-" + count.getUuid()));
			if (OrderType.TYPE_DIGITAL_MUSIC.equals(count.getOrderType())) {
				count.setId(StringUtil.md5(count.getPayType() + "-" + count.getTradeNo()));
			}
			count.setUpdatetime(now);
		}
		multicardDetailService.saveOrUpdateBatch(countList);
		dbLogger.warn("刷新次兑卡使用明细表：{}~{} {}", startTime, endTime, countList.size());
	}

	private List<ReportMulticardDetail> getPayList(Timestamp startTime, Timestamp endTime) {
		List<ReportMulticardDetail> payList = new ArrayList<>();
		List<ReportMulticardDetail> discountCount = multicardDetailService.getMulticardDiscountCount(startTime, endTime);
		if (CollectionUtils.isEmpty(discountCount)) {
			return payList;
		}
		Map<String, List<ReportMulticardDetail>> orderTypeMap = BeanUtil.groupBeanList(discountCount, "orderType");
		Map<Long, ReportMulticardDetail> discountMap = BeanUtil.beanListToMap(discountCount, "discountId");
		List<ReportMulticardDetail> uuidList = new ArrayList<>();

		List<ReportMulticardDetail> showDiscountList = orderTypeMap.get(OrderType.TYPE_SHOW);
		if (CollectionUtils.isNotEmpty(showDiscountList)) {
			List<String> tradeNos = BeanUtil.getBeanPropertyList(showDiscountList, "tradeNo", true);
			List<ReportMulticardDetail> showList = multicardDetailService.getShowCount(tradeNos);
			uuidList.addAll(showList);
		}
		List<ReportMulticardDetail> ticketDiscountList = orderTypeMap.get(OrderType.TYPE_TICKET);
		if (CollectionUtils.isNotEmpty(ticketDiscountList)) {
			List<String> tradeNos = BeanUtil.getBeanPropertyList(ticketDiscountList, "tradeNo", true);
			List<ReportMulticardDetail> ticketList = multicardDetailService.getTicketCount(tradeNos);
			uuidList.addAll(ticketList);
		}
		for (ReportMulticardDetail detail : uuidList) {
			Map<String, Double> discountInfoMap = JsonUtils.readJsonToMap(detail.getDiscountInfo());
			for (String discountId : discountInfoMap.keySet()) {
				ReportMulticardDetail multicardDetail = discountMap.get(Long.parseLong(discountId));
				if (multicardDetail != null) {
					ReportMulticardDetail pay = new ReportMulticardDetail();
					VoCopyUtil.copyFromObj(pay, detail);
					pay.setDiscountId(multicardDetail.getDiscountId());
					pay.setPayType(multicardDetail.getPayType());
					pay.setOrderType(multicardDetail.getOrderType());
					pay.setCouponBatchId(multicardDetail.getCouponBatchId());
					pay.setCouponCardNo(multicardDetail.getCouponCardNo());
					payList.add(pay);
					break;
				}
			}
		}
		return payList;
	}

	private List<ReportMulticardDetail> getRefundList(Timestamp startTime, Timestamp endTime) {
		List<ReportMulticardDetail> refundList = new ArrayList<>();
		List<ReportMulticardDetail> refundCount = multicardDetailService.getRefundCount(startTime, endTime);
		if (CollectionUtils.isEmpty(refundCount)) {
			return refundList;
		}
		Map<String, List<ReportMulticardDetail>> orderTypeMap = BeanUtil.groupBeanList(refundCount, "orderType");
		List<ReportMulticardDetail> uuidList = new ArrayList<>();

		List<ReportMulticardDetail> showRefundList = orderTypeMap.get(OrderType.TYPE_SHOW);
		if (CollectionUtils.isNotEmpty(showRefundList)) {
			List<String> uuids = BeanUtil.getBeanPropertyList(showRefundList, "uuid", true);
			List<ReportMulticardDetail> showList = multicardDetailService.getRefundShowCount(uuids);
			uuidList.addAll(showList);
		}
		List<ReportMulticardDetail> ticketRefundList = orderTypeMap.get(OrderType.TYPE_TICKET);
		if (CollectionUtils.isNotEmpty(ticketRefundList)) {
			List<String> uuids = BeanUtil.getBeanPropertyList(ticketRefundList, "uuid", true);
			List<ReportMulticardDetail> ticketList = multicardDetailService.getRefundSeatCount(uuids);
			uuidList.addAll(ticketList);
		}
		List<Long> discountIds = new ArrayList<>(uuidList.size());
		for (ReportMulticardDetail detail : uuidList) {
			Map<String, Double> discountInfoMap = JsonUtils.readJsonToMap(detail.getDiscountInfo());
			if (discountInfoMap.keySet().size() == 1) {
				discountIds.add(Long.parseLong((String) discountInfoMap.keySet().toArray()[0]));
			}
		}
		if (CollectionUtils.isEmpty(discountIds)) {
			return refundList;
		}
		List<ReportMulticardDetail> discountList = multicardDetailService.getRefundMulticardDiscountCount(discountIds);
		if (CollectionUtils.isEmpty(discountList)) {
			return refundList;
		}
		Map<Long, ReportMulticardDetail> discountMap = BeanUtil.beanListToMap(discountList, "discountId");

		for (ReportMulticardDetail detail : uuidList) {
			Map<String, Double> discountInfoMap = JsonUtils.readJsonToMap(detail.getDiscountInfo());
			for (String discountId : discountInfoMap.keySet()) {
				ReportMulticardDetail multicardDetail = discountMap.get(Long.parseLong(discountId));
				if (multicardDetail != null) {
					ReportMulticardDetail refund = new ReportMulticardDetail();
					VoCopyUtil.copyFromObj(refund, detail);
					refund.setDiscountId(multicardDetail.getDiscountId());
					refund.setCouponBatchId(multicardDetail.getCouponBatchId());
					refund.setCouponCardNo(multicardDetail.getCouponCardNo());
					refundList.add(refund);
					break;
				}
			}
		}
		return refundList;
	}

	private void conbineTotalNum(List<ReportMulticardDetail> countList) {
		List<Long> batchIds = BeanUtil.getBeanPropertyList(countList, "couponBatchId", true);
		if (CollectionUtils.isEmpty(batchIds)) {
			return;
		}
		List<ReportMulticardDetail> totalNums = multicardDetailService.getTotalNums(batchIds);
		Map<Long, ReportMulticardDetail> totalNumMap = BeanUtil.beanListToMap(totalNums, "couponBatchId");
		for (ReportMulticardDetail detail : countList) {
			ReportMulticardDetail totalNum = totalNumMap.get(detail.getCouponBatchId());
			if (totalNum == null) {
				continue;
			}
			detail.setTotalnum(totalNum.getTotalnum());
			detail.setCouponName(totalNum.getCouponName());
		}
	}

	private void conbineMembership(List<ReportMulticardDetail> countList) {
		List<String> couponCardNos = BeanUtil.getBeanPropertyList(countList, "couponCardNo", true);
		if (CollectionUtils.isEmpty(couponCardNos)) {
			return;
		}
		List<ReportMulticardDetail> memberships = multicardDetailService.getMemberships(couponCardNos);
		Map<String, ReportMulticardDetail> membershipMap = BeanUtil.beanListToMap(memberships, "couponCardNo");
		for (ReportMulticardDetail detail : countList) {
			ReportMulticardDetail membership = membershipMap.get(detail.getCouponCardNo());
			if (membership == null) {
				continue;
			}
			detail.setMembershipTypeId(membership.getMembershipTypeId());
			detail.setMembershipTypeName(membership.getMembershipTypeName());
			detail.setMembershipCardNo(membership.getMembershipCardNo());
		}
	}

	@Override
	public ResultCode<Page<ReportMulticardDetail>> getCounts(AuthUser user, SearReportMulticardDetail search, Page<ReportMulticardDetail> page) {
		if ((search.getTickettimeFrom() == null || search.getTickettimeTo() == null) && StringUtils.isBlank(search.getMembershipCardNo())) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		QueryWrapper<ReportMulticardDetail> wrapper = new QueryWrapper<>();
		wrapper.eq("company_id", user.getCompanyId());
		wrapper.orderByDesc("tickettime", "updatetime", "id");
		if (ObjectUtils.isNotEmpty(search.getTickettimeFrom())) {
			wrapper.ge("tickettime", search.getTickettimeFrom());
		}
		if (ObjectUtils.isNotEmpty(search.getTickettimeTo())) {
			wrapper.le("tickettime", search.getTickettimeTo());
		}
		if (StringUtils.isNotBlank(search.getMembershipCardNo())) {
			wrapper.eq("membership_card_no", search.getMembershipCardNo());
		}
		if (StringUtils.isNotBlank(search.getMembershipTypeIds())) {
			String[] idArr = StringUtils.split(search.getMembershipTypeIds(), ",");
			wrapper.in("membership_type_id", idArr);
		}
		if (StringUtils.isNotBlank(search.getMobile())) {
			wrapper.eq("mobile", search.getMobile());
		}
		if (StringUtils.isNotBlank(search.getCouponCardNo())) {
			wrapper.eq("coupon_card_no", search.getCouponCardNo());
		}
		if (StringUtils.isNotBlank(search.getTradeNo())) {
			wrapper.eq("trade_no", search.getTradeNo());
		}
		if (search.getProgramId() != null) {
			wrapper.eq("program_id", search.getProgramId());
		}
		Page<ReportMulticardDetail> pageResult = multicardDetailService.page(page, wrapper);


		return ResultCode.getSuccessReturn(pageResult);
	}

	@Override
	public ResultCode<ReportMulticardDetailTotalVo> getTotals(AuthUser user, SearReportMulticardDetail search) {
		ReportMulticardDetailTotalVo result = new ReportMulticardDetailTotalVo();
		if (search.getTickettimeFrom() == null || search.getTickettimeTo() == null) {
			initNum(result);
			return ResultCode.getSuccessReturn(result);
		}
		search.setCompanyId(user.getCompanyId());

		if (StringUtils.isNotBlank(search.getMembershipTypeIds())) {
			String[] idArr = StringUtils.split(search.getMembershipTypeIds(), ",");
			List<String> idList = new ArrayList<>(idArr.length);
			for (String id : idArr) {
				idList.add(id);
			}
			search.setMembershipTypeIdList(idList);
		}

		ReportMulticardDetailTotalVo tmp = multicardDetailService.getTotals(search);
		if (ObjectUtils.isEmpty(tmp)) {
			initNum(result);
		} else {
			VoCopyUtil.copyFromObj(result, tmp);
			initNum(result);
		}
		return ResultCode.getSuccessReturn(result);
	}

	private void initNum(ReportMulticardDetailTotalVo result) {
		if (null == result.getPayTotal()) {
			result.setPayTotal(0);
		}
		if (null == result.getRefundTotal()) {
			result.setRefundTotal(0);
		}
	}


	private List<ReportMulticardDetail> getDamsPayList(Timestamp startTime, Timestamp endTime) {
		List<ReportMulticardDetail> payList = new ArrayList<>();
		List<ReportMulticardDetail> discountCount = multicardDetailService.getDamsMulticardDiscountCount(startTime, endTime);
		if (CollectionUtils.isEmpty(discountCount)) {
			return payList;
		}
		Map<String, ReportMulticardDetail> tradeNoMap = BeanUtil.beanListToMap(discountCount, "tradeNo");

		List<String> tradeNos = BeanUtil.getBeanPropertyList(discountCount, "tradeNo", true);
		List<ReportMulticardDetail> damsList = multicardDetailService.getDamsCount(tradeNos);


		for (ReportMulticardDetail detail : damsList) {
			ReportMulticardDetail multicardDetail = tradeNoMap.get(detail.getTradeNo());
			if (multicardDetail != null) {
				ReportMulticardDetail pay = new ReportMulticardDetail();
				VoCopyUtil.copyFromObj(pay, detail);
				pay.setDiscountId(multicardDetail.getDiscountId());
				pay.setPayType(multicardDetail.getPayType());
				pay.setOrderType(multicardDetail.getOrderType());
				pay.setCouponBatchId(multicardDetail.getCouponBatchId());
				pay.setCouponCardNo(multicardDetail.getCouponCardNo());
				payList.add(pay);
			}
		}
		return payList;
	}


	private List<ReportMulticardDetail> getDamsRefundList(Timestamp startTime, Timestamp endTime) {
		List<ReportMulticardDetail> refundList = new ArrayList<>();
		List<ReportMulticardDetail> refundCount = multicardDetailService.getDamsRefundCount(startTime, endTime);
		if (CollectionUtils.isEmpty(refundCount)) {
			return refundList;
		}
		List<String> tradeNos = refundCount.stream().map(ReportMulticardDetail::getTradeNo).collect(Collectors.toList());
		List<ReportMulticardDetail> damsList = multicardDetailService.getDamsRefundDetailCount(tradeNos);
		if (CollectionUtils.isEmpty(damsList)) {
			return refundList;
		}
		List<ReportMulticardDetail> discountList = multicardDetailService.getDamsRefundMulticardDiscountCount(tradeNos);
		if (CollectionUtils.isEmpty(discountList)) {
			return refundList;
		}
		Map<String, ReportMulticardDetail> tradeNoMap = BeanUtil.beanListToMap(discountList, "tradeNo");
		for (ReportMulticardDetail detail : damsList) {
			ReportMulticardDetail multicardDetail = tradeNoMap.get(detail.getTradeNo());
			if (multicardDetail != null) {
				ReportMulticardDetail refund = new ReportMulticardDetail();
				VoCopyUtil.copyFromObj(refund, detail);
				refund.setDiscountId(multicardDetail.getDiscountId());
				refund.setCouponBatchId(multicardDetail.getCouponBatchId());
				refund.setCouponCardNo(multicardDetail.getCouponCardNo());
				refundList.add(refund);
			}
		}
		return refundList;
	}
}

package com.llwh.dcenter.untrans.report;

import java.util.Map;

import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.CouponReportSearch;

public interface ReportCouponUntransService {
	ResultCode<Page<Map<String, Object>>> getCouponReport(Long companyId, CouponReportSearch search, Page<CouponReportSearch> page);

	ResultCode<Map<String, Object>> getCouponTotal(Long companyId, CouponReportSearch search);

}

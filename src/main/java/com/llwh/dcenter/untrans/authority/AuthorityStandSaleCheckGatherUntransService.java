package com.llwh.dcenter.untrans.authority;

import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearReportStandCheckGatherCount;
import com.llwh.dcenter.model.ReportStandSaleCheckGather;


public interface AuthorityStandSaleCheckGatherUntransService {

	ResultCode<IPage<ReportStandSaleCheckGather>> getCounts(AuthUser user, Page<ReportStandSaleCheckGather> page, SearReportStandCheckGatherCount search);

	List<List<Object>> getTotalCount(AuthUser user, SearReportStandCheckGatherCount search);

	ResultCode<ReportStandSaleCheckGather> getTotals(AuthUser user, SearReportStandCheckGatherCount search);
}

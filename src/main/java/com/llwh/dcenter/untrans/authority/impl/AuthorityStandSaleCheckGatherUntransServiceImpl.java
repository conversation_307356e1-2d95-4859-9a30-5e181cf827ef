package com.llwh.dcenter.untrans.authority.impl;

import java.util.ArrayList;
import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.ticket.Status;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.ValueUtil;
import cn.fancylab.util.VoCopyUtil;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.base.constant.SpecialRights;
import com.llwh.dcenter.helper.SearReportStandCheckGatherCount;
import com.llwh.dcenter.model.ReportStandSaleCheckGather;
import com.llwh.dcenter.service.authority.AuthorityStandSaleCheckGatherService;
import com.llwh.dcenter.untrans.authority.AuthorityStandSaleCheckGatherUntransService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.vo.common.TicketTypeVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AuthorityStandSaleCheckGatherUntransServiceImpl implements AuthorityStandSaleCheckGatherUntransService {
	@Autowired
	private AuthorityStandSaleCheckGatherService standSaleCheckGatherService;
	@Autowired
	private CommonUntransService commonUntransService;



	@Override
	public ResultCode<IPage<ReportStandSaleCheckGather>> getCounts(AuthUser user, Page<ReportStandSaleCheckGather> page, SearReportStandCheckGatherCount search) {
		if ((search.getDatefrom() == null || search.getDateto() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		search.setDateto(DateUtil.addDay(search.getDateto(), 1));
		List<Long> searchProgramIds = commonUntransService.getSponsorProgramByUserGroupId(user.getCompanyId(), user.getUserGroupId(), Status.N);
		search.setCompanyId(user.getCompanyId());
		if(SpecialRights.isGroup(user.getDataLevel())){
			search.setUserGroupId(user.getUserGroupId());
		}else if(SpecialRights.isUser(user.getDataLevel())){
			search.setAddUserId(user.getUserId());
		} else if (SpecialRights.isSuper(user.getDataLevel())) {
			searchProgramIds = null;
		}
		if(StringUtils.isNotBlank(search.getUserGroupIds())){
			List<Long> userGroupIds = commonUntransService.splitIds(search.getUserGroupIds());
			search.setUserGroupIdList(userGroupIds);
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%"+search.getProgramCode()+"%");
		}
		if (StringUtils.isNotBlank(search.getCityName())) {
			search.setCityName("%"+search.getCityName()+"%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getCategory())) {
			search.setCategory("%"+search.getCategory()+"%");
		}
		if (StringUtils.isNotBlank(search.getSmallCategory())) {
			search.setSmallCategory("%"+search.getSmallCategory()+"%");
		}
		page.setOptimizeCountSql(false);
		Page<ReportStandSaleCheckGather> counts = standSaleCheckGatherService.getCounts(page, search, searchProgramIds);
		List<Long> ticketTypeIds = BeanUtil.getBeanPropertyList(counts.getRecords(),"ticketTypeId",true);
		if(CollectionUtils.isNotEmpty(ticketTypeIds)){
			List<TicketTypeVo> ticketTypeVos = commonUntransService.getTicketTypes(ticketTypeIds);
			for(TicketTypeVo vo : ticketTypeVos){
				for(ReportStandSaleCheckGather name : counts.getRecords()){
					if(name.getTicketTypeId().equals(vo.getId())){
						name.setTicketPrice(vo.getTicketPrice());
						name.setTicketDescription(vo.getDescription());
						name.setTicketRemark(vo.getRemark());
						break;
					}
				}
			}
		}
		return ResultCode.getSuccessReturn(counts);
	}

	@Override
	public List<List<Object>> getTotalCount(AuthUser user, SearReportStandCheckGatherCount search) {
		List<List<Object>> resultList = new ArrayList<>();
		if ((search.getDatefrom() == null || search.getDateto() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return resultList;
		}
		search.setCompanyId(user.getCompanyId());
		search.setDateto(DateUtil.addDay(search.getDateto(), 1));
		if(StringUtils.isNotBlank(search.getUserGroupIds())){
			List<Long> userGroupIds = commonUntransService.splitIds(search.getUserGroupIds());
			search.setUserGroupIdList(userGroupIds);
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%"+search.getProgramCode()+"%");
		}
		if (StringUtils.isNotBlank(search.getCityName())) {
			search.setCityName("%"+search.getCityName()+"%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getCategory())) {
			search.setCategory("%"+search.getCategory()+"%");
		}
		if (StringUtils.isNotBlank(search.getSmallCategory())) {
			search.setSmallCategory("%"+search.getSmallCategory()+"%");
		}
		List<ReportStandSaleCheckGather> totalCount = standSaleCheckGatherService.getTotalCount(search);
		for (ReportStandSaleCheckGather gather : totalCount) {
			List<Object> objList = new ArrayList<>();
			objList.add(gather.getCityName());
			objList.add(gather.getProgramId());
			objList.add(gather.getProgramCode());
			objList.add(gather.getStadiumName());
			objList.add(gather.getVenueName());
			objList.add(gather.getUserGroupName());
			objList.add(gather.getProgramName());
			objList.add(gather.getCategory());
			objList.add(gather.getSmallCategory());
			objList.add(gather.getPlayTime());
			objList.add(gather.getTicketTypeName());
			objList.add(gather.getTicketPrice());
			objList.add(gather.getSaleCount());
			objList.add(gather.getAmount());
			objList.add(gather.getDiscount());
			objList.add(gather.getPaidAmount());
			objList.add(gather.getRefundCount());
			objList.add(gather.getRefundAmount());
			objList.add(gather.getRefundDiscount());
			objList.add(gather.getRefundPaidAmount());
			objList.add(gather.getTotalQuantity());
			objList.add(gather.getTotalAmount());
			objList.add(gather.getTotalDiscount());
			objList.add(gather.getTotalPaidAmount());
			resultList.add(objList);
		}
		return resultList;
	}

	@Override
	public ResultCode<ReportStandSaleCheckGather> getTotals(AuthUser user, SearReportStandCheckGatherCount search){
		ReportStandSaleCheckGather result = new ReportStandSaleCheckGather();
		if ((search.getDatefrom() == null || search.getDateto() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			initNum(result);
			return ResultCode.getSuccessReturn(result);
		}
		search.setCompanyId(user.getCompanyId());
		search.setDateto(DateUtil.addDay(search.getDateto(), 1));
		if(StringUtils.isNotBlank(search.getUserGroupIds())){
			List<Long> userGroupIds = commonUntransService.splitIds(search.getUserGroupIds());
			search.setUserGroupIdList(userGroupIds);
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%"+search.getProgramCode()+"%");
		}
		if (StringUtils.isNotBlank(search.getCityName())) {
			search.setCityName("%"+search.getCityName()+"%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getCategory())) {
			search.setCategory("%"+search.getCategory()+"%");
		}
		if (StringUtils.isNotBlank(search.getSmallCategory())) {
			search.setSmallCategory("%"+search.getSmallCategory()+"%");
		}
		ReportStandSaleCheckGather tmp = standSaleCheckGatherService.getTotals(search);
		if(ObjectUtils.isEmpty(tmp)){
			initNum(result);
		}else{
			VoCopyUtil.copyFromObj(result,tmp);
			initNum(result);
		}
		return ResultCode.getSuccessReturn(result);
	}

	private void initNum(ReportStandSaleCheckGather result){
		if(null == result.getSaleCount()){
			result.setSaleCount(0);
		}
		if(null == result.getAmount()){
			result.setAmount(0.00);
		}else{
			result.setAmount(ValueUtil.round(result.getAmount()));
		}
		if(null == result.getDiscount()){
			result.setDiscount(0.00);
		}else{
			result.setDiscount(ValueUtil.round(result.getDiscount()));
		}
		if(null == result.getPaidAmount()){
			result.setPaidAmount(0.00);
		}else{
			result.setPaidAmount(ValueUtil.round(result.getPaidAmount()));
		}
		if(null == result.getRefundCount()){
			result.setRefundCount(0);
		}
		if(null == result.getRefundAmount()){
			result.setRefundAmount(0.00);
		}else{
			result.setRefundAmount(ValueUtil.round(result.getRefundAmount()));
		}
		if(null == result.getRefundDiscount()){
			result.setRefundDiscount(0.00);
		}else{
			result.setRefundDiscount(ValueUtil.round(result.getRefundDiscount()));
		}
		if(null == result.getRefundPaidAmount()){
			result.setRefundPaidAmount(0.00);
		}else{
			result.setRefundPaidAmount(ValueUtil.round(result.getRefundPaidAmount()));
		}
		if(null == result.getTotalQuantity()){
			result.setTotalQuantity(0);
		}
		if(null == result.getTotalAmount()){
			result.setTotalAmount(0.00);
		}else{
			result.setTotalAmount(ValueUtil.round(result.getTotalAmount()));
		}
		if(null == result.getTotalDiscount()){
			result.setTotalDiscount(0.00);
		}else{
			result.setTotalDiscount(ValueUtil.round(result.getTotalDiscount()));
		}
		if(null == result.getTotalPaidAmount()){
			result.setTotalPaidAmount(0.00);
		}else{
			result.setTotalPaidAmount(ValueUtil.round(result.getTotalPaidAmount()));
		}
		if(null == result.getSettlementAmount()){
			result.setSettlementAmount(0.00);
		}else{
			result.setSettlementAmount(ValueUtil.round(result.getSettlementAmount()));
		}
	}
}

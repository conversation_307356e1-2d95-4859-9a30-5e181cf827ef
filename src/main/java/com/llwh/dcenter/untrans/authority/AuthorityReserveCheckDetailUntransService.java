package com.llwh.dcenter.untrans.authority;

import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearchReserveCheckDetail;
import com.llwh.dcenter.model.ReportReserveCheckDetail;

public interface AuthorityReserveCheckDetailUntransService {

	ResultCode<Map> getCounts(AuthUser user,SearchReserveCheckDetail search, Page<ReportReserveCheckDetail> page);

	List<List<Object>> getTotalCount(AuthUser user, SearchReserveCheckDetail search);
}

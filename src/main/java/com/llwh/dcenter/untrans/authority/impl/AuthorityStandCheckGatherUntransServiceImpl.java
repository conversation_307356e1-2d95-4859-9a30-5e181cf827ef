package com.llwh.dcenter.untrans.authority.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.ticket.Status;
import cn.fancylab.util.DateUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.base.constant.SpecialRights;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.model.ReportStandCheckGather;
import com.llwh.dcenter.service.authority.AuthorityStandCheckGatherService;
import com.llwh.dcenter.untrans.authority.AuthorityStandCheckGatherUntransService;
import com.llwh.dcenter.untrans.common.CommonUntransService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AuthorityStandCheckGatherUntransServiceImpl implements AuthorityStandCheckGatherUntransService {

	@Autowired
	private AuthorityStandCheckGatherService standCheckGatherService;
	@Autowired
	private CommonUntransService commonUntransService;

	@Override
	public ResultCode<Page<ReportStandCheckGather>> getCounts(AuthUser user, Page<ReportStandCheckGather> page, SearReportStandCheckDetailCount search) {
		if ((search.getCheckDateFrom() == null || search.getCheckDateTo() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不能为空");
		}
		search.setCompanyId(user.getCompanyId());
		search.setCheckDateTo(DateUtil.addDay(search.getCheckDateTo(), 1));
		List<Long> searchProgramIds = commonUntransService.getSponsorProgramByUserGroupId(user.getCompanyId(), user.getUserGroupId(), Status.N);
		if(SpecialRights.isGroup(user.getDataLevel())){
			search.setCheckUserGroupId(user.getUserGroupId());
		}else if(SpecialRights.isUser(user.getDataLevel())){
			search.setTbsUserId(user.getUserId());
		} else if (SpecialRights.isSuper(user.getDataLevel())) {
			searchProgramIds = null;
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		page.setOptimizeCountSql(false);
		Page<ReportStandCheckGather> pageResult = standCheckGatherService.getTotalCount(page, search, searchProgramIds);
		return ResultCode.getSuccessReturn(pageResult);
	}

	@Override
	public List<List<Object>> getTotalCount(AuthUser user, SearReportStandCheckDetailCount search) {
		List<List<Object>> resultList = new ArrayList<>();
		if ((search.getCheckDateFrom() == null || search.getCheckDateTo() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return resultList;
		}
		search.setCompanyId(user.getCompanyId());
		search.setCheckDateTo(DateUtil.addDay(search.getCheckDateTo(), 1));
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		List<ReportStandCheckGather> tempList = standCheckGatherService.getTotalCount(search);
		for (ReportStandCheckGather detail : tempList) {
			List<Object> objList = new ArrayList<>();
			objList.add(detail.getCheckDate());
			objList.add(detail.getStadiumName());
			objList.add(detail.getVenueName());
			objList.add(detail.getProgramId());
			objList.add(detail.getProgramCode());
			objList.add(detail.getProgramName());
			objList.add(detail.getShowName());
			objList.add(detail.getPlayTime());
			objList.add(detail.getTicketTypeName());
			objList.add(detail.getCheckCount());
			objList.add(detail.getUserGroupName());
			objList.add(detail.getCheckGroupName());
			resultList.add(objList);
		}
		return resultList;
	}

	@Override
	public ResultCode<Map> getTotals(AuthUser user, SearReportStandCheckDetailCount search){
		if ((search.getCheckDateFrom() == null || search.getCheckDateTo() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不能为空");
		}
		search.setCompanyId(user.getCompanyId());
		search.setCheckDateTo(DateUtil.addDay(search.getCheckDateTo(), 1));
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		Map tmp = standCheckGatherService.getTotals(search);
		return ResultCode.getSuccessReturn(tmp);
	}
}

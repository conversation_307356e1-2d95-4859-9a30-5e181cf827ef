package com.llwh.dcenter.untrans.authority.impl;

import java.util.ArrayList;
import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.DateUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.base.constant.SpecialRights;
import com.llwh.dcenter.model.ReportReserveCheckGather;
import com.llwh.dcenter.service.authority.AuthorityReserveCheckGatherService;
import com.llwh.dcenter.untrans.authority.AuthorityReserveCheckGatherUntransService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.vo.req.ReportReserveCheckGatherReq;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AuthorityReserveCheckGatherUntransServiceImpl implements AuthorityReserveCheckGatherUntransService {

	@Autowired
	private AuthorityReserveCheckGatherService authorityReserveCheckGatherService;
	@Autowired
	private CommonUntransService commonUntransService;

	@Override
	public ResultCode<Page<ReportReserveCheckGather>> getCounts(AuthUser user, ReportReserveCheckGatherReq search, Page<ReportReserveCheckGather> page) {
		if (search.getDatefrom() == null || search.getDateto() == null) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不存在");
		}
		search.setDateto(DateUtil.addDay(search.getDateto(), 1));
		search.setCompanyId(user.getCompanyId());
		List<Long> searchReserveProgramIds = commonUntransService.getSponsorReserveProgramByUserGroupId(user.getCompanyId(), user.getUserGroupId());
		if(!SpecialRights.isSuper(user.getDataLevel())){
			if(CollectionUtils.isEmpty(searchReserveProgramIds)){
				return ResultCode.getSuccessReturn(new Page<>());
			}
		} else {
			searchReserveProgramIds = null;
		}
		Page<ReportReserveCheckGather> pageResult = authorityReserveCheckGatherService.getTotalCount(page, search, searchReserveProgramIds);
		return ResultCode.getSuccessReturn(pageResult);
	}

	@Override
	public List<List<Object>> getTotalCount(AuthUser user,ReportReserveCheckGatherReq search) {
		List<List<Object>> resultList = new ArrayList<>();
		if (search.getDatefrom() == null || search.getDateto() == null) {
			return resultList;
		}
		search.setDateto(DateUtil.addDay(search.getDateto(), 1));
		search.setCompanyId(user.getCompanyId());
		List<ReportReserveCheckGather> result = authorityReserveCheckGatherService.getTotalCount(search);
		for (ReportReserveCheckGather gather : result) {
			List<Object> objList = new ArrayList<>();
//			null != gather.getReservedate() ? gather.getReservedate():null;
			objList.add(gather.getReserveDate());
			objList.add(gather.getStadiumName());
			objList.add(gather.getVenueName());
			objList.add(gather.getProgramName());
			objList.add(gather.getReserveTime());
			objList.add(gather.getReserveGather());
			objList.add(gather.getCheckGather());
			resultList.add(objList);
		}
		return resultList;
	}
}

package com.llwh.dcenter.untrans.authority.impl;

import java.util.ArrayList;
import java.util.List;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.VoCopyUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.base.constant.SpecialRights;
import com.llwh.dcenter.constant.ReportConstant;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.model.ReportCheckRecord;
import com.llwh.dcenter.service.authority.AuthorityCheckRecordService;
import com.llwh.dcenter.untrans.authority.AuthorityCheckRecordUntransService;
import com.llwh.dcenter.untrans.common.CommonUntransService;
import com.llwh.dcenter.vo.report.CheckRecordVo;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AuthorityCheckRecordUntransServiceImpl implements AuthorityCheckRecordUntransService {
	@Autowired
	private AuthorityCheckRecordService authorityCheckRecordService;
	@Autowired
	private CommonUntransService commonUntransService;

	@Override
	public ResultCode<Page<ReportCheckRecord>> getCounts(AuthUser user, Page<ReportCheckRecord> page, SearReportStandCheckDetailCount search) {
		if ((search.getCheckTimeFrom() == null || search.getCheckTimeTo() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不能为空");
		}
		List<Long> searchProgramIds = commonUntransService.getSponsorProgramByUserGroupId(user.getCompanyId(), user.getUserGroupId(), null);
		search.setCompanyId(user.getCompanyId());
		if(SpecialRights.isGroup(user.getDataLevel())){
			search.setCheckUserGroupId(user.getUserGroupId());
		}else if(SpecialRights.isUser(user.getDataLevel())){
			search.setTbsUserId(user.getUserId());
		} else if (SpecialRights.isSuper(user.getDataLevel())) {
			searchProgramIds = null;
		}
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		page.setOptimizeCountSql(false);
		Page<ReportCheckRecord> pageResult = authorityCheckRecordService.getTotalCount(page, search, searchProgramIds);
		return ResultCode.getSuccessReturn(pageResult);
	}

	@Override
	public List<List<Object>> getTotalCount(AuthUser user, SearReportStandCheckDetailCount search) {
		List<List<Object>> resultList = new ArrayList<>();
		if ((search.getCheckTimeFrom() == null || search.getCheckTimeTo() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			return resultList;
		}
		search.setCompanyId(user.getCompanyId());
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		List<ReportCheckRecord> tempList = authorityCheckRecordService.getTotalCount(search);
		for (ReportCheckRecord detail : tempList) {
			List<Object> objList = new ArrayList<>();
			objList.add(ReportConstant.reserveTypeMap.get(detail.getReserveType()));
			objList.add(detail.getUuid());
			objList.add(detail.getDeviceId());
			objList.add(detail.getCheckCount());
			objList.add(detail.getCheckDate());
			objList.add(detail.getCheckTime());
			objList.add(detail.getCheckUserGroupName());
			objList.add(detail.getTbsUserName());
			objList.add(detail.getProgramId());
			objList.add(detail.getProgramCode());
			objList.add(detail.getProgramName());
			objList.add(detail.getStadiumName());
			objList.add(detail.getVenueName());
			objList.add(detail.getPlayTime());
			objList.add(detail.getTicketPrice());
			resultList.add(objList);
		}
		return resultList;
	}

	@Override
	public ResultCode<CheckRecordVo> getTotals(AuthUser user, SearReportStandCheckDetailCount search){
		CheckRecordVo result = new CheckRecordVo();
		if ((search.getCheckTimeFrom() == null || search.getCheckTimeTo() == null) &&
				(search.getPlayStartTime() == null || search.getPlayEndTime() == null)) {
			initNum(result);
			return ResultCode.getSuccessReturn(result);
		}
		search.setCompanyId(user.getCompanyId());
		if (StringUtils.isNotBlank(search.getVenueName())) {
			search.setVenueName("%" + search.getVenueName() + "%");
		}
		if (StringUtils.isNotBlank(search.getStadiumName())) {
			search.setStadiumName("%" + search.getStadiumName() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramCode())) {
			search.setProgramCode("%" + search.getProgramCode() + "%");
		}
		if (StringUtils.isNotBlank(search.getProgramName())) {
			search.setProgramName("%" + search.getProgramName() + "%");
		}
		CheckRecordVo tmp = authorityCheckRecordService.getTotals(search);
		if(ObjectUtils.isEmpty(tmp)){
			initNum(result);
		}else{
			VoCopyUtil.copyFromObj(result,tmp);
			initNum(result);
		}
		return ResultCode.getSuccessReturn(result);
	}

	private void initNum(CheckRecordVo result){
		if(null == result.getCheckCount()){
			result.setCheckCount(0);
		}
		if(null == result.getCount()){
			result.setCount(0);
		}
	}
	/*
	private void conbine(List<ReportCheckRecord> resultList, List<ReportCheckRecord> showCheckList, List<ReportCheckRecord> ticketCheckList,
	                     List<ReportCheckRecord> showExchangeList){
		Timestamp now = DateUtil.getCurFullTimestamp();
		if(CollectionUtils.isNotEmpty(showCheckList)){
			List<String> uuids = BeanUtil.getBeanPropertyList(showCheckList,"uuid",true);
			List<ReportCheckRecord> showInfoList = authorityCheckRecordService.getShowInfos(uuids);
			if(CollectionUtils.isNotEmpty(showInfoList)){
				for(ReportCheckRecord check : showCheckList){
					for(ReportCheckRecord info : showInfoList){
						if(info.getUuid().equals(check.getUuid())){
							check.setStadiumId(info.getStadiumId());
							check.setStadiumName(info.getStadiumName());
							check.setVenueId(info.getVenueId());
							check.setVenueName(info.getVenueName());
							check.setProgramId(info.getProgramId());
							check.setProgramCode(info.getProgramCode());
							check.setProgramName(info.getProgramName());
							check.setShowId(info.getShowId());
							check.setShowName(info.getShowName());
							check.setPlayTime(info.getPlayTime());
							check.setTicketTypeId(info.getTicketTypeId());
							check.setTicketPrice(info.getTicketPrice());
							check.setSaleUserId(info.getSaleUserId());
							check.setUserGroupId(info.getUserGroupId());
							check.setUpdatetime(now);
							resultList.add(check);
						}
					}
				}
			}
		}
		if(CollectionUtils.isNotEmpty(ticketCheckList)){
			List<String> uuids = BeanUtil.getBeanPropertyList(ticketCheckList,"uuid",true);
			List<ReportCheckRecord> ticketInfoList = authorityCheckRecordService.getTicketInfos(uuids);
			if(CollectionUtils.isNotEmpty(ticketInfoList)){
				for(ReportCheckRecord check : ticketCheckList){
					for(ReportCheckRecord info : ticketInfoList){
						if(info.getUuid().equals(check.getUuid())){
							check.setStadiumId(info.getStadiumId());
							check.setStadiumName(info.getStadiumName());
							check.setVenueId(info.getVenueId());
							check.setVenueName(info.getVenueName());
							check.setProgramId(info.getProgramId());
							check.setProgramCode(info.getProgramCode());
							check.setProgramName(info.getProgramName());
							check.setShowId(info.getShowId());
							check.setShowName(info.getShowName());
							check.setPlayTime(info.getPlayTime());
							check.setTicketTypeId(info.getTicketTypeId());
							check.setTicketPrice(info.getTicketPrice());
							check.setSaleUserId(info.getSaleUserId());
							check.setUserGroupId(info.getUserGroupId());
							check.setUpdatetime(now);
							resultList.add(check);
						}
					}
				}
			}
		}
		if(CollectionUtils.isNotEmpty(showExchangeList)){
			List<String> uuids = BeanUtil.getBeanPropertyList(showExchangeList,"uuid",true);
			List<ReportCheckRecord> showInfoList = authorityCheckRecordService.getShowInfos(uuids);
			if(CollectionUtils.isNotEmpty(showInfoList)){
				for(ReportCheckRecord check : showExchangeList){
					for(ReportCheckRecord info : showInfoList){
						if(info.getUuid().equals(check.getUuid())){
							check.setStadiumId(info.getStadiumId());
							check.setStadiumName(info.getStadiumName());
							check.setVenueId(info.getVenueId());
							check.setVenueName(info.getVenueName());
							check.setProgramId(info.getProgramId());
							check.setProgramCode(info.getProgramCode());
							check.setProgramName(info.getProgramName());
							check.setShowId(info.getShowId());
							check.setShowName(info.getShowName());
							check.setPlayTime(info.getPlayTime());
							check.setTicketTypeId(info.getTicketTypeId());
							check.setTicketPrice(info.getTicketPrice());
							check.setSaleUserId(info.getSaleUserId());
							check.setUserGroupId(info.getUserGroupId());
							check.setUpdatetime(now);
							resultList.add(check);
						}
					}
				}
			}
		}
	}
		*/
}

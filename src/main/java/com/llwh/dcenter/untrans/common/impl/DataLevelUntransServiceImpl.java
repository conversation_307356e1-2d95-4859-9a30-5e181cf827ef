package com.llwh.dcenter.untrans.common.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.util.BeanUtil;

import com.llwh.base.constant.SpecialRights;
import com.llwh.dcenter.enums.CardTypeEnum;
import com.llwh.dcenter.helper.DataLevelSearch;
import com.llwh.dcenter.mapper.common.CommonMapper;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;
import com.llwh.dcenter.vo.common.GroupCardTypeRelationVo;
import com.llwh.dcenter.vo.common.StadiumVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DataLevelUntransServiceImpl implements DataLevelUntransService {
	@Autowired
	private CommonMapper commonMapper;

	@Override
	public List<Long> userBaseDataAllowStadiumIds(AuthUser authUser) {
		String dataLevel = authUser.getDataLevel();
		if (SpecialRights.isSuper(dataLevel)) {
			List<StadiumVo> stadiumList = commonMapper.getStadium(authUser.getCompanyId());
			return BeanUtil.getBeanPropertyList(stadiumList, "id", true);
		}
		if (SpecialRights.isStadium(dataLevel) || SpecialRights.isGroup(dataLevel) || SpecialRights.isPersonal(dataLevel)) {
			return commonMapper.getUserGroupStadiumIds(authUser.getUserGroupId());
		}
		return new ArrayList<>(0);
	}

	@Override
	public List<Long> userStadiumDataLevelAllowStadiumIds(AuthUser authUser) {
		String dataLevel = authUser.getDataLevel();
		if (SpecialRights.isStadium(dataLevel)) {
			List<Long> stadiumIds = commonMapper.getUserGroupStadiumIds(authUser.getUserGroupId());
			return new ArrayList<>(stadiumIds);
		}
		return new ArrayList<>(0);
	}

	@Override
	public DataLevelSearch getDataLevelSearch(DataLevelSearch search, AuthUser authUser, boolean gather) {
		return getDataLevelSearch(search, authUser, gather, false);
	}

	@Override
	public DataLevelSearch getDataLevelSearch(DataLevelSearch search, AuthUser authUser, boolean gather, boolean haveSponsor) {
		String dataLevel = authUser.getDataLevel();
		if (SpecialRights.isSuper(dataLevel)) {
			return search;
		}
		if (haveSponsor) {
			List<Long> authorityProgramIds = commonMapper.getSponsorProgramByUserGroupId(authUser.getCompanyId(), authUser.getUserGroupId(), null);
			search.setAuthorityProgramIds(authorityProgramIds);
		}
		if (SpecialRights.isStadium(dataLevel)) {
			List<Long> stadiumIds = userStadiumDataLevelAllowStadiumIds(authUser);
			search.setAuthorityStadiumIds(stadiumIds);
		} else if (SpecialRights.isGroup(dataLevel)) {
			search.setAuthorityUserGroupId(authUser.getUserGroupId());
		} else if (SpecialRights.isPersonal(dataLevel)) {
			if (gather) {
				Long userGroupId = authUser.getUserGroupId();
				if (userGroupId == null) {
					userGroupId = 0L;
				}
				search.setAuthorityUserGroupId(userGroupId);
			} else {
				search.setAuthorityUserId(authUser.getTbsUserId());
			}
		}
		return search;
	}

	@Override
	public List<String> userAllowCardIdList(AuthUser authUser, String cardType) {
		List<GroupCardTypeRelationVo> relationVoList = commonMapper.getAllowCardIdList(authUser.getCompanyId(), authUser.getUserGroupId());
		if (CollectionUtils.isEmpty(relationVoList)) {
			return new ArrayList<>(0);
		}
		List<String> cardIds = relationVoList.stream()
				.filter(groupCardTypeRelationVo -> StringUtils.equals(groupCardTypeRelationVo.getCardType(), cardType))
				.map(GroupCardTypeRelationVo::getCardTypeId)
				.collect(Collectors.toList());
		if (StringUtils.equals(cardType, CardTypeEnum.TIMES.getCode()) || StringUtils.equals(cardType, CardTypeEnum.RIGHTS.getCode())) {
			List<String> membershipCardIdList = commonMapper.getMembershipCardIdList(authUser.getCompanyId(), authUser.getUserGroupId());
			if (CollectionUtils.isNotEmpty(membershipCardIdList)) {
				cardIds.addAll(membershipCardIdList);
			}
		}
		if (StringUtils.equals(cardType, CardTypeEnum.BALANCE.getCode())) {
			List<String> balancardIdList = commonMapper.getBalanceCardIdList(authUser.getCompanyId(), authUser.getUserGroupId());
			if (CollectionUtils.isNotEmpty(balancardIdList)) {
				cardIds.addAll(balancardIdList);
			}
		}
		if (CollectionUtils.isEmpty(cardIds)) {
			//防止过滤数据时为空不限制，给个默认值
			cardIds.add("gllx_" + cardType);
		}
		return cardIds.stream().distinct().collect(Collectors.toList());
	}

}

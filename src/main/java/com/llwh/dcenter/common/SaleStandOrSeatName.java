package com.llwh.dcenter.common;

import java.io.Serializable;
import java.sql.Timestamp;

import cn.fancylab.model.BaseObject;

/**
 * 售卖汇总名称基类
 */
public class SaleStandOrSeatName extends BaseObject {


	private static final long serialVersionUID = 693464521312678419L;
	/**
	 * 场馆ID
	 */
	private Long stadiumId;
	/**
	 * 场馆名称
	 */
	private String stadiumName;
	/**
	 * 场地ID
	 */
	private Long venueId;
	/**
	 * 场地名称
	 */
	private String venueName;
	/**
	 * 项目ID
	 */
	private Long programId;
	/**
	 * 项目名称
	 */
	private String programName;
	/**
	 * 场次时间
	 */
	private Timestamp playTime;
	private Long showId;
	/**
	 * 场次名称
	 */
	private String showName;
	/**
	 * 票价
	 */
	private Double ticketPrice;
	/**
	 * 下单用户组ID
	 */
	private Long userGroupId;
	/**
	 * 下单用户组名称
	 */
	private String userGroupName;
	/**
	 * 项目编码
	 */
	private String programCode;

	/**
	 * 项目分类一级
	 */
	private String category;
	/**
	 * 项目分类二级
	 */
	private String smallCategory;
	/**
	 * 城市编码
	 */
	private transient String cityCode;
	/**
	 * 城市
	 */
	private String cityName;
	/**
	 * 出票类型
	 */
	private String sellType;
	/**
	 * 结算金额
	 */
	private Double settlementAmount;

	public String getStadiumName() {
		return stadiumName;
	}

	public void setStadiumName(String stadiumName) {
		this.stadiumName = stadiumName;
	}

	public String getVenueName() {
		return venueName;
	}

	public void setVenueName(String venueName) {
		this.venueName = venueName;
	}

	public String getProgramName() {
		return programName;
	}

	public void setProgramName(String programName) {
		this.programName = programName;
	}

	public Timestamp getPlayTime() {
		return playTime;
	}

	public void setPlayTime(Timestamp playTime) {
		this.playTime = playTime;
	}

	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	public Double getTicketPrice() {
		return ticketPrice;
	}

	public void setTicketPrice(Double ticketPrice) {
		this.ticketPrice = ticketPrice;
	}

	public String getUserGroupName() {
		return userGroupName;
	}

	public void setUserGroupName(String userGroupName) {
		this.userGroupName = userGroupName;
	}

	public String getProgramCode() {
		return programCode;
	}

	public void setProgramCode(String programCode) {
		this.programCode = programCode;
	}

	public Long getStadiumId() {
		return stadiumId;
	}

	public void setStadiumId(Long stadiumId) {
		this.stadiumId = stadiumId;
	}

	public Long getVenueId() {
		return venueId;
	}

	public void setVenueId(Long venueId) {
		this.venueId = venueId;
	}

	public Long getProgramId() {
		return programId;
	}

	public void setProgramId(Long programId) {
		this.programId = programId;
	}

	public Long getShowId() {
		return showId;
	}

	public void setShowId(Long showId) {
		this.showId = showId;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getSmallCategory() {
		return smallCategory;
	}

	public void setSmallCategory(String smallCategory) {
		this.smallCategory = smallCategory;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getSellType() {
		return sellType;
	}

	public void setSellType(String sellType) {
		this.sellType = sellType;
	}

	public Double getSettlementAmount() {
		return settlementAmount;
	}

	public void setSettlementAmount(Double settlementAmount) {
		this.settlementAmount = settlementAmount;
	}

	@Override
	public Serializable realId() {
		return null;
	}
}

package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;

import cn.fancylab.model.BaseObject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;


/**
 * 储值卡收支明细
 */
public class ReportBalanceDetail extends BaseObject {
	private static final long serialVersionUID = 6150936650220119836L;
	@TableId(value = "id", type = IdType.INPUT)
	private String id;
	/**
	 * 交易时间
	 */
	private Timestamp tradetime;
	/**
	 * 会员卡号
	 */
	private String cardno;
	/**
	 * 会员卡种ID
	 */
	private String membershipTypeId;
	/**
	 * 会员卡种名称
	 */
	private String membershipTypeName;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 收支分类
	 */
	private String tag;
	/**
	 * 收支类型
	 */
	private String type;

	/**
	 * 金额
	 */
	private Double amount;

	/**
	 * 赠送金额
	 */
	private Double giftAmount;

	/**
	 * 原始订单号
	 */
	private String originTradeNo;
	/**
	 * 唯一订单号
	 */
	private String serialNo;
	/**
	 * 描述
	 */
	private String description;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 更新时间
	 */
	private Timestamp updatetime;

	/**
	 * 企业ID
	 */
	private Long companyId;

	/**
	 * 会员id
	 */
	private Long memberId;

	/**
	 * 支付网关
	 */
	private String gatewayCode;

	/**
	 * 支付网关中文名
	 */
	private String gatewayName;

	@Override
	public Serializable realId() {
		return id;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Timestamp getTradetime() {
		return tradetime;
	}

	public void setTradetime(Timestamp tradetime) {
		this.tradetime = tradetime;
	}

	public String getCardno() {
		return cardno;
	}

	public void setCardno(String cardno) {
		this.cardno = cardno;
	}

	public String getMembershipTypeId() {
		return membershipTypeId;
	}

	public void setMembershipTypeId(String membershipTypeId) {
		this.membershipTypeId = membershipTypeId;
	}

	public String getMembershipTypeName() {
		return membershipTypeName;
	}

	public void setMembershipTypeName(String membershipTypeName) {
		this.membershipTypeName = membershipTypeName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public String getOriginTradeNo() {
		return originTradeNo;
	}

	public void setOriginTradeNo(String originTradeNo) {
		this.originTradeNo = originTradeNo;
	}

	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Double getGiftAmount() {
		return giftAmount;
	}

	public void setGiftAmount(Double giftAmount) {
		this.giftAmount = giftAmount;
	}

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}

	public String getGatewayCode() {
		return gatewayCode;
	}

	public void setGatewayCode(String gatewayCode) {
		this.gatewayCode = gatewayCode;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}
}

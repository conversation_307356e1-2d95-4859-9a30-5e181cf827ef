package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;

import cn.fancylab.model.BaseObject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 会员卡销售明细表
 */
public class ReportMemberSaleDetail extends BaseObject {

	private static final long serialVersionUID = 2669056332878424860L;

	@TableId(value = "id", type = IdType.INPUT)
	private String id;
	/**
	 * 销售订单号
	 */
	private String tradeNo;
	/**
	 * 支付时间
	 */
	private Timestamp paidtime;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 会员卡卡种
	 */
	private String memberCardType;
	/**
	 * 原价
	 */
	private Double price;
	/**
	 * 折扣
	 */
	private Double discount;
	/**
	 * 实收金额
	 */
	private Double paidAmount;
	/**
	 * 退款金额
	 */
	private Double refundAmount;
	/**
	 * 实收金额
	 */
	private Timestamp refundtime;
	/**
	 * 姓名
	 */
	private String realName;
	/**
	 * 证件类型
	 */
	private String certificateType;
	/**
	 * 证件号码
	 */
	private String certificateNo;
	/**
	 * 支付方式
	 */
	private String paymethod;
	/**
	 * 下单用户组名称
	 */
	private String userGroupName;
	/**
	 * 下单用户名称
	 */
	private String userName;
	/**
	 * 用户组ID
	 */
	private Long userGroupId;
	/**
	 * 用户ID/tbsUserId
	 */
	private Long tbsUserId;
	/**
	 * 会员卡卡种ID
	 */
	private String membershipCardId;
	/**
	 * 原订单支付状态
	 */
	private String payStatus;
	/**
	 * 卡状态
	 */
	private String membershipStatus;
	private String cardno;
	private String payType;
	private Long companyId;
	private Timestamp updatetime;
	private String gatewayCode;
	private String platform;
	private String merchantCode;
	/**
	 * 下单时间
	 */
	private Timestamp addtime;
	private Long memberId;
	private String memberName;
	/**
	 * buy_normal 正常购卡  buy_renew 续卡
	 */
	private String buyType;
	/**
	 * 分销码
	 */
	private String origin;
	/**
	 * 总次数
	 */
	private Integer totalnum;
	/**
	 * 来源
	 */
	private String source;
	/**
	 * 渠道
	 */
	private String sourceChannel;
	/**
	 * 卡种使用方式，次卡/兑换卡/权益卡
	 */
	private String cardType;
	/**
	 * 权益卡关联等级
	 */
	private Long memberLevelId;
	private String memberLevelName;
	/**
	 * 优惠券活动
	 */
	private Long couponPromotionId;
	private String couponPromotionName;

	/**
	 * 订单类型
	 */
	private String orderType;

	/**
	 * 储值金额
	 */
	private Double storedAmount;

	/**
	 * 赠送金额
	 */
	private Double giftAmount;
	/**
	 * 会员卡有效期开始时间
	 */
	private transient Timestamp starttime;
	/**
	 * 会员卡有效期结束时间
	 */
	private transient Timestamp endtime;

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public Timestamp getPaidtime() {
		return paidtime;
	}

	public void setPaidtime(Timestamp paidtime) {
		this.paidtime = paidtime;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getMemberCardType() {
		return memberCardType;
	}

	public void setMemberCardType(String memberCardType) {
		this.memberCardType = memberCardType;
	}

	public Double getPrice() {
		return price;
	}

	public void setPrice(Double price) {
		this.price = price;
	}

	public Double getDiscount() {
		return discount;
	}

	public void setDiscount(Double discount) {
		this.discount = discount;
	}

	public Double getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(Double paidAmount) {
		this.paidAmount = paidAmount;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(String certificateType) {
		this.certificateType = certificateType;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String getPaymethod() {
		return paymethod;
	}

	public void setPaymethod(String paymethod) {
		this.paymethod = paymethod;
	}

	public String getUserGroupName() {
		return userGroupName;
	}

	public void setUserGroupName(String userGroupName) {
		this.userGroupName = userGroupName;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public String getMembershipCardId() {
		return membershipCardId;
	}

	public void setMembershipCardId(String membershipCardId) {
		this.membershipCardId = membershipCardId;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public Long getTbsUserId() {
		return tbsUserId;
	}

	public void setTbsUserId(Long tbsUserId) {
		this.tbsUserId = tbsUserId;
	}

	public Double getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(Double refundAmount) {
		this.refundAmount = refundAmount;
	}

	public Timestamp getRefundtime() {
		return refundtime;
	}

	public void setRefundtime(Timestamp refundtime) {
		this.refundtime = refundtime;
	}

	public String getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(String payStatus) {
		this.payStatus = payStatus;
	}

	public String getMembershipStatus() {
		return membershipStatus;
	}

	public void setMembershipStatus(String membershipStatus) {
		this.membershipStatus = membershipStatus;
	}

	public String getCardno() {
		return cardno;
	}

	public void setCardno(String cardno) {
		this.cardno = cardno;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getGatewayCode() {
		return gatewayCode;
	}

	public void setGatewayCode(String gatewayCode) {
		this.gatewayCode = gatewayCode;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public String getMerchantCode() {
		return merchantCode;
	}

	public void setMerchantCode(String merchantCode) {
		this.merchantCode = merchantCode;
	}

	public Timestamp getAddtime() {
		return addtime;
	}

	public void setAddtime(Timestamp addtime) {
		this.addtime = addtime;
	}

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public String getBuyType() {
		return buyType;
	}

	public void setBuyType(String buyType) {
		this.buyType = buyType;
	}

	public String getOrigin() {
		return origin;
	}

	public void setOrigin(String origin) {
		this.origin = origin;
	}

	public Integer getTotalnum() {
		return totalnum;
	}

	public void setTotalnum(Integer totalnum) {
		this.totalnum = totalnum;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getSourceChannel() {
		return sourceChannel;
	}

	public void setSourceChannel(String sourceChannel) {
		this.sourceChannel = sourceChannel;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public Long getMemberLevelId() {
		return memberLevelId;
	}

	public void setMemberLevelId(Long memberLevelId) {
		this.memberLevelId = memberLevelId;
	}

	public String getMemberLevelName() {
		return memberLevelName;
	}

	public void setMemberLevelName(String memberLevelName) {
		this.memberLevelName = memberLevelName;
	}

	public Long getCouponPromotionId() {
		return couponPromotionId;
	}

	public void setCouponPromotionId(Long couponPromotionId) {
		this.couponPromotionId = couponPromotionId;
	}

	public String getCouponPromotionName() {
		return couponPromotionName;
	}

	public void setCouponPromotionName(String couponPromotionName) {
		this.couponPromotionName = couponPromotionName;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public Double getStoredAmount() {
		return storedAmount;
	}

	public void setStoredAmount(Double storedAmount) {
		this.storedAmount = storedAmount;
	}

	public Double getGiftAmount() {
		return giftAmount;
	}

	public void setGiftAmount(Double giftAmount) {
		this.giftAmount = giftAmount;
	}

	public Timestamp getStarttime() {
		return starttime;
	}

	public void setStarttime(Timestamp starttime) {
		this.starttime = starttime;
	}

	public Timestamp getEndtime() {
		return endtime;
	}

	public void setEndtime(Timestamp endtime) {
		this.endtime = endtime;
	}

	@Override
	public Serializable realId() {
		return id;
	}
}

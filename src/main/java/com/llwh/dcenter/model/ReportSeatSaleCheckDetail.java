package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;

import cn.fancylab.model.BaseObject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 座票销售及入场明细表
 */
public class ReportSeatSaleCheckDetail extends BaseObject {

	private static final long serialVersionUID = -8186819468589834936L;
	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.INPUT)
	private String id;
	private String tradeNo;
	private String mobile;
	private Timestamp tickettime;
	private Long programId;
	private String programName;
	private Long stadiumId;
	private String stadiumName;
	private Long venueId;
	private String venueName;
	private Long showId;
	private String showName;
	private Timestamp playTime;
	private Timestamp playEndTime;
	private Timestamp externalTime;
	private Timestamp externalEndTime;
	/**
	 * 票价ID
	 */
	private Long ticketPriceId;
	private Double ticketPrice;
	/**
	 * 票价描述
	 */
	private String ticketDescription;
	/**
	 * 票价备注
	 */
	private String ticketRemark;
	/**
	 * 区排座号
	 */
	private String venueAreaName;
	private Long venueAreaId;
	private String rowNo;
	private String colNo;
	private String uuid;
	private String realName;
	private String certificateType;
	private String certificateNo;
	private Double amount;
	private Double discount;
	private Double paidAmount;
	/**
	 * 出票/退票，pay/refund
	 */
	private String payType;
	private String paymethod;
	/**
	 * 物流方式
	 */
	private String logisticsMode;
	/**
	 * 物流地址
	 */
	private String logisticsAddress;
	/**
	 * 票面附加信息
	 */
	private String ticketMessage;
	/**
	 * 订单关联信息
	 */
	private String orderRelatedMessage;
	/**
	 * 订单备注
	 */
	private String description;
	/**
	 * 下单用户组
	 */
	private String userGroupName;
	/**
	 * 下单用户账号
	 */
	private String userName;
	/**
	 * 下单用户名称
	 */
	private String nickName;
	private Long addUserId;
	/**
	 * 入场时间
	 */
	private Timestamp checkTime;
	/**
	 * 企业ID
	 */
	private Long companyId;
	/**
	 * 更新时间
	 */
	private Timestamp updatetime;

	/**
	 * 外部订单号
	 */
	private String outTradeNo;
	/**
	 * 售票渠道
	 */
	private String channel;
	/**
	 * 售票用户组ID
	 */
	private Long userGroupId;
	/**
	 * 手机号
	 */
	private String contactMobile;
	/**
	 * 支付流水
	 */
	private String payseqno;
	private String paybank;
	/**
	 * 购票用户
	 */
	private String ticketUser;
	/**
	 * 场馆体量
	 */
	private String volume;
	/**
	 * OA项目
	 */
	private String programCode;
	/**
	 * 订单支付状态
	 */
	private String orderStatus;

	/**
	 * 数量
	 */
	private Integer quantity;

	/**
	 * 出票类型
	 */
	private String sellType;
	/**
	 * 项目分类一级
	 */
	private String category;
	/**
	 * 项目分类二级
	 */
	private String smallCategory;

	private transient String additionInfo;

	private Double settlementAmount;

	/**
	 * 打印次数
	 */
	private Integer printNum;

	/**
	 * 打印时间
	 */
	private Timestamp printTime;

	/**
	 * 会员ID
	 */
	private Long memberId;

	/**
	 * 会员等级卡号
	 */
	@Column(updatable = false)
	private String cardNo;

	/**
	 * 大客户分类
	 */
	private String customerCategory; // keyAccount

	/**
	 * 大客户企业名称
	 */
	private String customerUnitName;
	/**
	 * 大客户ID
	 */
	private Long customerId;
	/**
	 * 来源
	 */
	private String platform;

	private transient Timestamp programStartTime;

	private transient Timestamp programEndTime;
	@Column(updatable = false)
	private Long memberLevelId;
	@Column(updatable = false)
	private String memberLevelName;
	@Column(updatable = false)
	private String memberStatus;
	private Timestamp paidtime;
	private String cityCode;
	/**
	 * 物流方式
	 */
	private String transport;
	/**
	 * 套票订单号
	 */
	private String packTradeNo;
	/**
	 * 座位属性
	 */
	private String seatAttr;
	/**
	 * 主办类型
	 */
	private String organizerType;
	/**
	 * 下单时间
	 */
	private Timestamp ordertime;

	private String gatewayCode;
	/**
	 * 分销码
	 */
	private String origin;
	private String merchantCode;
	private String barcode;

	/**
	 * 出票来源
	 */
	private String reserveNoName;
	/**
	 * 保留类型
	 */
	private transient String holdFlag;
	private String holdTypeName;
	/**
	 * 票价名称
	 */
	private String ticketName;
	/**
	 * 原始订单号，退单对应的订单号
	 */
	private String originTradeNo;
	/**
	 * 订单明细-票状态，N无效、Y有效、T退票、U已使用
	 */
	private String status;
	/**
	 * 微信优惠券批次号
	 */
	private String promotionStockIds;

	public String getHoldTypeName() {
		return holdTypeName;
	}

	public void setHoldTypeName(String holdTypeName) {
		this.holdTypeName = holdTypeName;
	}

	public String getReserveNoName() {
		return reserveNoName;
	}

	public void setReserveNoName(String reserveNoName) {
		this.reserveNoName = reserveNoName;
	}

	public String getHoldFlag() {
		return holdFlag;
	}

	public void setHoldFlag(String holdFlag) {
		this.holdFlag = holdFlag;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public Timestamp getTickettime() {
		return tickettime;
	}

	public void setTickettime(Timestamp tickettime) {
		this.tickettime = tickettime;
	}

	public Long getProgramId() {
		return programId;
	}

	public void setProgramId(Long programId) {
		this.programId = programId;
	}

	public String getProgramName() {
		return programName;
	}

	public void setProgramName(String programName) {
		this.programName = programName;
	}

	public Long getStadiumId() {
		return stadiumId;
	}

	public void setStadiumId(Long stadiumId) {
		this.stadiumId = stadiumId;
	}

	public String getStadiumName() {
		return stadiumName;
	}

	public void setStadiumName(String stadiumName) {
		this.stadiumName = stadiumName;
	}

	public Long getVenueId() {
		return venueId;
	}

	public void setVenueId(Long venueId) {
		this.venueId = venueId;
	}

	public String getVenueName() {
		return venueName;
	}

	public void setVenueName(String venueName) {
		this.venueName = venueName;
	}

	public Long getShowId() {
		return showId;
	}

	public void setShowId(Long showId) {
		this.showId = showId;
	}

	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	public Timestamp getPlayTime() {
		return playTime;
	}

	public void setPlayTime(Timestamp playTime) {
		this.playTime = playTime;
	}

	public Long getTicketPriceId() {
		return ticketPriceId;
	}

	public void setTicketPriceId(Long ticketPriceId) {
		this.ticketPriceId = ticketPriceId;
	}

	public Double getTicketPrice() {
		return ticketPrice;
	}

	public void setTicketPrice(Double ticketPrice) {
		this.ticketPrice = ticketPrice;
	}

	public String getTicketDescription() {
		return ticketDescription;
	}

	public void setTicketDescription(String ticketDescription) {
		this.ticketDescription = ticketDescription;
	}

	public String getTicketRemark() {
		return ticketRemark;
	}

	public void setTicketRemark(String ticketRemark) {
		this.ticketRemark = ticketRemark;
	}

	public String getVenueAreaName() {
		return venueAreaName;
	}

	public void setVenueAreaName(String venueAreaName) {
		this.venueAreaName = venueAreaName;
	}

	public String getRowNo() {
		return rowNo;
	}

	public void setRowNo(String rowNo) {
		this.rowNo = rowNo;
	}

	public String getColNo() {
		return colNo;
	}

	public void setColNo(String colNo) {
		this.colNo = colNo;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(String certificateType) {
		this.certificateType = certificateType;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public Double getDiscount() {
		return discount;
	}

	public void setDiscount(Double discount) {
		this.discount = discount;
	}

	public Double getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(Double paidAmount) {
		this.paidAmount = paidAmount;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getPaymethod() {
		return paymethod;
	}

	public void setPaymethod(String paymethod) {
		this.paymethod = paymethod;
	}

	public String getLogisticsMode() {
		return logisticsMode;
	}

	public void setLogisticsMode(String logisticsMode) {
		this.logisticsMode = logisticsMode;
	}

	public String getLogisticsAddress() {
		return logisticsAddress;
	}

	public void setLogisticsAddress(String logisticsAddress) {
		this.logisticsAddress = logisticsAddress;
	}

	public String getTicketMessage() {
		return ticketMessage;
	}

	public void setTicketMessage(String ticketMessage) {
		this.ticketMessage = ticketMessage;
	}

	public String getOrderRelatedMessage() {
		return orderRelatedMessage;
	}

	public void setOrderRelatedMessage(String orderRelatedMessage) {
		this.orderRelatedMessage = orderRelatedMessage;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getUserGroupName() {
		return userGroupName;
	}

	public void setUserGroupName(String userGroupName) {
		this.userGroupName = userGroupName;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Timestamp getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(Timestamp checkTime) {
		this.checkTime = checkTime;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public String getContactMobile() {
		return contactMobile;
	}

	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}

	public String getTicketUser() {
		return ticketUser;
	}

	public void setTicketUser(String ticketUser) {
		this.ticketUser = ticketUser;
	}

	public String getPayseqno() {
		return payseqno;
	}

	public void setPayseqno(String payseqno) {
		this.payseqno = payseqno;
	}

	public String getVolume() {
		return volume;
	}

	public void setVolume(String volume) {
		this.volume = volume;
	}

	public String getProgramCode() {
		return programCode;
	}

	public void setProgramCode(String programCode) {
		this.programCode = programCode;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public String getSellType() {
		return sellType;
	}

	public void setSellType(String sellType) {
		this.sellType = sellType;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getSmallCategory() {
		return smallCategory;
	}

	public void setSmallCategory(String smallCategory) {
		this.smallCategory = smallCategory;
	}

	public String getAdditionInfo() {
		return additionInfo;
	}

	public void setAdditionInfo(String additionInfo) {
		this.additionInfo = additionInfo;
	}

	public Double getSettlementAmount() {
		return settlementAmount;
	}

	public void setSettlementAmount(Double settlementAmount) {
		this.settlementAmount = settlementAmount;
	}

	@Override
	public Serializable realId() {
		return id;
	}

	public Integer getPrintNum() {
		return printNum;
	}

	public void setPrintNum(Integer printNum) {
		this.printNum = printNum;
	}

	public Timestamp getPrintTime() {
		return printTime;
	}

	public void setPrintTime(Timestamp printTime) {
		this.printTime = printTime;
	}

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}


	public String getCustomerUnitName() {
		return customerUnitName;
	}

	public void setCustomerUnitName(String customerUnitName) {
		this.customerUnitName = customerUnitName;
	}

	public String getCustomerCategory() {
		return customerCategory;
	}

	public void setCustomerCategory(String customerCategory) {
		this.customerCategory = customerCategory;
	}

	public Long getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public Timestamp getProgramStartTime() {
		return programStartTime;
	}

	public void setProgramStartTime(Timestamp programStartTime) {
		this.programStartTime = programStartTime;
	}

	public Timestamp getProgramEndTime() {
		return programEndTime;
	}

	public void setProgramEndTime(Timestamp programEndTime) {
		this.programEndTime = programEndTime;
	}

	public Long getMemberLevelId() {
		return memberLevelId;
	}

	public void setMemberLevelId(Long memberLevelId) {
		this.memberLevelId = memberLevelId;
	}

	public String getMemberLevelName() {
		return memberLevelName;
	}

	public void setMemberLevelName(String memberLevelName) {
		this.memberLevelName = memberLevelName;
	}

	public String getMemberStatus() {
		return memberStatus;
	}

	public void setMemberStatus(String memberStatus) {
		this.memberStatus = memberStatus;
	}

	public Timestamp getPaidtime() {
		return paidtime;
	}

	public void setPaidtime(Timestamp paidtime) {
		this.paidtime = paidtime;
	}

	public Long getAddUserId() {
		return addUserId;
	}

	public void setAddUserId(Long addUserId) {
		this.addUserId = addUserId;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getTransport() {
		return transport;
	}

	public void setTransport(String transport) {
		this.transport = transport;
	}

	public String getPackTradeNo() {
		return packTradeNo;
	}

	public void setPackTradeNo(String packTradeNo) {
		this.packTradeNo = packTradeNo;
	}

	public String getSeatAttr() {
		return seatAttr;
	}

	public void setSeatAttr(String seatAttr) {
		this.seatAttr = seatAttr;
	}

	public String getOrganizerType() {
		return organizerType;
	}

	public void setOrganizerType(String organizerType) {
		this.organizerType = organizerType;
	}

	public Timestamp getOrdertime() {
		return ordertime;
	}

	public void setOrdertime(Timestamp ordertime) {
		this.ordertime = ordertime;
	}

	public String getGatewayCode() {
		return gatewayCode;
	}

	public void setGatewayCode(String gatewayCode) {
		this.gatewayCode = gatewayCode;
	}

	public String getOrigin() {
		return origin;
	}

	public void setOrigin(String origin) {
		this.origin = origin;
	}

	public String getMerchantCode() {
		return merchantCode;
	}

	public void setMerchantCode(String merchantCode) {
		this.merchantCode = merchantCode;
	}

	public Timestamp getExternalTime() {
		return externalTime;
	}

	public void setExternalTime(Timestamp externalTime) {
		this.externalTime = externalTime;
	}

	public Timestamp getExternalEndTime() {
		return externalEndTime;
	}

	public void setExternalEndTime(Timestamp externalEndTime) {
		this.externalEndTime = externalEndTime;
	}

	public String getBarcode() {
		return barcode;
	}

	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}

	public Long getVenueAreaId() {
		return venueAreaId;
	}

	public void setVenueAreaId(Long venueAreaId) {
		this.venueAreaId = venueAreaId;
	}

	public String getPaybank() {
		return paybank;
	}

	public void setPaybank(String paybank) {
		this.paybank = paybank;
	}

	public Timestamp getPlayEndTime() {
		return playEndTime;
	}

	public void setPlayEndTime(Timestamp playEndTime) {
		this.playEndTime = playEndTime;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public String getTicketName() {
		return ticketName;
	}

	public void setTicketName(String ticketName) {
		this.ticketName = ticketName;
	}

	public String getOriginTradeNo() {
		return originTradeNo;
	}

	public void setOriginTradeNo(String originTradeNo) {
		this.originTradeNo = originTradeNo;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getPromotionStockIds() {
		return promotionStockIds;
	}

	public void setPromotionStockIds(String promotionStockIds) {
		this.promotionStockIds = promotionStockIds;
	}
}

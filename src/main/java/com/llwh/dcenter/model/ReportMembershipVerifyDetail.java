package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;

import cn.fancylab.model.BaseObject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 会员卡验证明细表
 */
public class ReportMembershipVerifyDetail extends BaseObject {

	private static final long serialVersionUID = -3798415064700358166L;
	@TableId(value = "id", type = IdType.INPUT)
	private Long id;
	/**
	 * 卡号
	 */
	private String cardNo;
	/**
	 * 验证时间
	 */
	private Timestamp verifyTime;
	/**
	 * 会员卡卡种
	 */
	private String membershipCardType;
	/**
	 * 会员卡卡种ID
	 */
	private String membershipCardId;
	/**
	 * 价格
	 */
	private Double price;
	/**
	 * 销售用户组
	 */
	private String userGroupName;
	/**
	 * 验证用户组ID
	 */
	private Long checkGroupId;
	/**
	 * 验证用户组
	 */
	private String checkGroupName;
	/**
	 * 销售用户组ID
	 */
	private Long userGroupId;
	/**
	 * 验证账号
	 */
	private String checkNo;
	/**
	 * 姓名
	 */
	private String realName;
	/**
	 * 证件类型
	 */
	private String certificateType;
	/**
	 * 证件号
	 */
	private String certificateNo;
	/**
	 * 更新时间
	 */
	private Timestamp updatetime;
	/**
	 * 企业ID
	 */
	private Long companyId;
	private Long tbsUserId;
	//membershipId
	private Long ticketTypeId;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public Timestamp getVerifyTime() {
		return verifyTime;
	}

	public void setVerifyTime(Timestamp verifyTime) {
		this.verifyTime = verifyTime;
	}

	public String getMembershipCardType() {
		return membershipCardType;
	}

	public void setMembershipCardType(String membershipCardType) {
		this.membershipCardType = membershipCardType;
	}

	public String getMembershipCardId() {
		return membershipCardId;
	}

	public void setMembershipCardId(String membershipCardId) {
		this.membershipCardId = membershipCardId;
	}

	public Double getPrice() {
		return price;
	}

	public void setPrice(Double price) {
		this.price = price;
	}

	public String getUserGroupName() {
		return userGroupName;
	}

	public void setUserGroupName(String userGroupName) {
		this.userGroupName = userGroupName;
	}

	public String getCheckGroupName() {
		return checkGroupName;
	}

	public void setCheckGroupName(String checkGroupName) {
		this.checkGroupName = checkGroupName;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public String getCheckNo() {
		return checkNo;
	}

	public void setCheckNo(String checkNo) {
		this.checkNo = checkNo;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(String certificateType) {
		this.certificateType = certificateType;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getTbsUserId() {
		return tbsUserId;
	}

	public void setTbsUserId(Long tbsUserId) {
		this.tbsUserId = tbsUserId;
	}

	public Long getTicketTypeId() {
		return ticketTypeId;
	}

	public void setTicketTypeId(Long ticketTypeId) {
		this.ticketTypeId = ticketTypeId;
	}

	public Long getCheckGroupId() {
		return checkGroupId;
	}

	public void setCheckGroupId(Long checkGroupId) {
		this.checkGroupId = checkGroupId;
	}

	@Override
	public Serializable realId() {
		return id;
	}
}

package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.llwh.dcenter.common.ReserveCheckName;

/**
 * 预约及入场汇总表
 */
public class ReportReserveCheckGather extends ReserveCheckName {

	private static final long serialVersionUID = -101619000482520687L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.INPUT)
	private String id;

	/**
	 * 预约合计
	 */
	private Long reserveGather;
	/**
	 * 入场合计
	 */
	private Long checkGather;
	/**
	 * 企业ID
	 */
	private Long companyId;
	/**
	 * 更新时间
	 */
	private Timestamp updatetime;
	private Long userGroupId;
	private Long userId;

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getReserveGather() {
		return reserveGather;
	}

	public void setReserveGather(Long reserveGather) {
		this.reserveGather = reserveGather;
	}

	public Long getCheckGather() {
		return checkGather;
	}

	public void setCheckGather(Long checkGather) {
		this.checkGather = checkGather;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	@Override
	public Serializable realId() {
		return id;
	}
}

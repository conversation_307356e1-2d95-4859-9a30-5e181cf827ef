package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.llwh.dcenter.common.SaleStandOrSeatName;

/**
 * 站票销售及入场汇总表（按项目）
 */
public class ReportSeatSaleGatherProgram extends SaleStandOrSeatName {

	private static final long serialVersionUID = 15006546210217L;

	@TableId(value = "id",type = IdType.INPUT)
	private String id;

	/**
	 * 票价ID
	 */
	private Long ticketPriceId;
	private Double ticketPrice;
	/**
	 * 票价描述
	 */
	private transient String ticketDescription;
	/**
	 * 票价备注
	 */
	private transient String ticketRemark;
	private Integer saleCount;
	private Double amount;
	private Double discount;
	private Double paidAmount;
	private Integer refundCount;
	private Double refundAmount;
	private Double refundDiscount;
	private Double refundPaidAmount;
	private Integer totalQuantity;
	private Double totalAmount;
	private Double totalDiscount;
	private Double totalPaidAmount;
	/**
	 * 出票/退票日期
	 */
	private Date ticketDate;
	private Long companyId;
	private Timestamp updatetime;


	/**
	 * 用户名
	 */
	private String userName;

	private String realName;

	private Long addUserId;
	private transient String gatewayCode;
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Long getAddUserId() {
		return addUserId;
	}

	public void setAddUserId(Long addUserId) {
		this.addUserId = addUserId;
	}


	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}


	public Integer getSaleCount() {
		return saleCount;
	}

	public void setSaleCount(Integer saleCount) {
		this.saleCount = saleCount;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public Double getDiscount() {
		return discount;
	}

	public void setDiscount(Double discount) {
		this.discount = discount;
	}

	public Double getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(Double paidAmount) {
		this.paidAmount = paidAmount;
	}

	public Integer getRefundCount() {
		return refundCount;
	}

	public void setRefundCount(Integer refundCount) {
		this.refundCount = refundCount;
	}

	public Double getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(Double refundAmount) {
		this.refundAmount = refundAmount;
	}

	public Double getRefundDiscount() {
		return refundDiscount;
	}

	public void setRefundDiscount(Double refundDiscount) {
		this.refundDiscount = refundDiscount;
	}

	public Double getRefundPaidAmount() {
		return refundPaidAmount;
	}

	public void setRefundPaidAmount(Double refundPaidAmount) {
		this.refundPaidAmount = refundPaidAmount;
	}

	public Integer getTotalQuantity() {
		return totalQuantity;
	}

	public void setTotalQuantity(Integer totalQuantity) {
		this.totalQuantity = totalQuantity;
	}

	public Double getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(Double totalAmount) {
		this.totalAmount = totalAmount;
	}

	public Double getTotalDiscount() {
		return totalDiscount;
	}

	public void setTotalDiscount(Double totalDiscount) {
		this.totalDiscount = totalDiscount;
	}

	public Double getTotalPaidAmount() {
		return totalPaidAmount;
	}

	public void setTotalPaidAmount(Double totalPaidAmount) {
		this.totalPaidAmount = totalPaidAmount;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public Long getTicketPriceId() {
		return ticketPriceId;
	}

	public void setTicketPriceId(Long ticketPriceId) {
		this.ticketPriceId = ticketPriceId;
	}

	@Override
	public Double getTicketPrice() {
		return ticketPrice;
	}

	@Override
	public void setTicketPrice(Double ticketPrice) {
		this.ticketPrice = ticketPrice;
	}

	public Date getTicketDate() {
		return ticketDate;
	}

	public void setTicketDate(Date ticketDate) {
		this.ticketDate = ticketDate;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getTicketDescription() {
		return ticketDescription;
	}

	public void setTicketDescription(String ticketDescription) {
		this.ticketDescription = ticketDescription;
	}

	public String getTicketRemark() {
		return ticketRemark;
	}

	public void setTicketRemark(String ticketRemark) {
		this.ticketRemark = ticketRemark;
	}

	public String getGatewayCode() {
		return gatewayCode;
	}

	public void setGatewayCode(String gatewayCode) {
		this.gatewayCode = gatewayCode;
	}

	@Override
	public Serializable realId() {
		return id;
	}
}

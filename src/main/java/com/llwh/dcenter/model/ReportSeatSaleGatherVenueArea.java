package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;

import cn.fancylab.model.BaseObject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 选座订单明细报表（区域票种细分）
 */
public class ReportSeatSaleGatherVenueArea extends BaseObject {

	private static final long serialVersionUID = 7659322329253062682L;
	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.INPUT)
	private String id;

	private String platform;
	private String tradeNo;
	private String mobile;
	private Long memberId;
	private Timestamp tickettime;
	private Timestamp paidtime;
	private Timestamp programStartTime;
	private Timestamp programEndTime;
	private Long venueAreaId;
	private String venueAreaName;
	private Long ticketPriceId;
	private String ticketPriceName;
	private String rowNos;
	private String colNos;
	private String paymethod;
	private String payseqno;
	private String ticketMessage;
	private String orderRelatedMessage;
	private String description;
	private String payType;
	private Long addUserId;
	private String userName;
	private String userGroupName;
	private String outTradeNo;
	private String contactMobile;
	private String contactName;
	private String deliveryStatus;
	private String deliveryCompany;
	private String deliveryCompanyCode;
	private String deliverySn;
	private String deliveryAddress;
	private Integer saleCount;
	private Double amount;
	private Double discount;
	private Double paidAmount;
	private Integer refundCount;
	private Double refundAmount;
	private Double refundDiscount;
	private Double refundPaidAmount;
	private Integer totalQuantity;
	private Double totalAmount;
	private Double totalDiscount;
	private Double totalPaidAmount;
	private String refundRowNos;
	private String refundColNos;
	private Long companyId;
	private Timestamp updatetime;

	private String realname;
	private Long stadiumId;
	/**
	 * 场馆名称
	 */
	private String stadiumName;
	/**
	 * 场地ID
	 */
	private Long venueId;
	/**
	 * 场地名称
	 */
	private String venueName;
	/**
	 * 项目ID
	 */
	private Long programId;
	/**
	 * 项目名称
	 */
	private String programName;
	/**
	 * 场次时间
	 */
	private Timestamp playTime;
	private Long showId;
	/**
	 * 场次名称
	 */
	private String showName;
	/**
	 * 票价
	 */
	private Double ticketPrice;
	/**
	 * 下单用户组ID
	 */
	private Long userGroupId;
	/**
	 * 项目编码
	 */
	private String programCode;
	/**
	 * 出票类型
	 */
	private String sellType;
	/**
	 * 结算金额
	 */
	private Double settlementAmount;
	private transient String additionInfo;
	private transient String seatStr;
	private String ticketUser;
	@Column(updatable = false)
	private Long memberLevelId;
	@Column(updatable = false)
	private String memberLevelName;

	public String getTicketUser() {
		return ticketUser;
	}

	public void setTicketUser(String ticketUser) {
		this.ticketUser = ticketUser;
	}

	public String getSeatStr() {
		return seatStr;
	}

	public void setSeatStr(String seatStr) {
		this.seatStr = seatStr;
	}

	public String getRealname() {
		return realname;
	}

	public void setRealname(String realname) {
		this.realname = realname;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}

	public Timestamp getTickettime() {
		return tickettime;
	}

	public void setTickettime(Timestamp tickettime) {
		this.tickettime = tickettime;
	}

	public Timestamp getPaidtime() {
		return paidtime;
	}

	public void setPaidtime(Timestamp paidtime) {
		this.paidtime = paidtime;
	}

	public Timestamp getProgramStartTime() {
		return programStartTime;
	}

	public void setProgramStartTime(Timestamp programStartTime) {
		this.programStartTime = programStartTime;
	}

	public Timestamp getProgramEndTime() {
		return programEndTime;
	}

	public void setProgramEndTime(Timestamp programEndTime) {
		this.programEndTime = programEndTime;
	}

	public Long getVenueAreaId() {
		return venueAreaId;
	}

	public void setVenueAreaId(Long venueAreaId) {
		this.venueAreaId = venueAreaId;
	}

	public String getVenueAreaName() {
		return venueAreaName;
	}

	public void setVenueAreaName(String venueAreaName) {
		this.venueAreaName = venueAreaName;
	}

	public Long getTicketPriceId() {
		return ticketPriceId;
	}

	public void setTicketPriceId(Long ticketPriceId) {
		this.ticketPriceId = ticketPriceId;
	}

	public String getTicketPriceName() {
		return ticketPriceName;
	}

	public void setTicketPriceName(String ticketPriceName) {
		this.ticketPriceName = ticketPriceName;
	}

	public String getRowNos() {
		return rowNos;
	}

	public void setRowNos(String rowNos) {
		this.rowNos = rowNos;
	}

	public String getColNos() {
		return colNos;
	}

	public void setColNos(String colNos) {
		this.colNos = colNos;
	}

	public String getPaymethod() {
		return paymethod;
	}

	public void setPaymethod(String paymethod) {
		this.paymethod = paymethod;
	}

	public String getPayseqno() {
		return payseqno;
	}

	public void setPayseqno(String payseqno) {
		this.payseqno = payseqno;
	}

	public String getTicketMessage() {
		return ticketMessage;
	}

	public void setTicketMessage(String ticketMessage) {
		this.ticketMessage = ticketMessage;
	}

	public String getOrderRelatedMessage() {
		return orderRelatedMessage;
	}

	public void setOrderRelatedMessage(String orderRelatedMessage) {
		this.orderRelatedMessage = orderRelatedMessage;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	public String getContactMobile() {
		return contactMobile;
	}

	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getDeliveryStatus() {
		return deliveryStatus;
	}

	public void setDeliveryStatus(String deliveryStatus) {
		this.deliveryStatus = deliveryStatus;
	}

	public String getDeliveryCompany() {
		return deliveryCompany;
	}

	public void setDeliveryCompany(String deliveryCompany) {
		this.deliveryCompany = deliveryCompany;
	}

	public String getDeliveryCompanyCode() {
		return deliveryCompanyCode;
	}

	public void setDeliveryCompanyCode(String deliveryCompanyCode) {
		this.deliveryCompanyCode = deliveryCompanyCode;
	}

	public String getDeliverySn() {
		return deliverySn;
	}

	public void setDeliverySn(String deliverySn) {
		this.deliverySn = deliverySn;
	}

	public String getDeliveryAddress() {
		return deliveryAddress;
	}

	public void setDeliveryAddress(String deliveryAddress) {
		this.deliveryAddress = deliveryAddress;
	}

	public Integer getSaleCount() {
		return saleCount;
	}

	public void setSaleCount(Integer saleCount) {
		this.saleCount = saleCount;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public Double getDiscount() {
		return discount;
	}

	public void setDiscount(Double discount) {
		this.discount = discount;
	}

	public Double getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(Double paidAmount) {
		this.paidAmount = paidAmount;
	}

	public Integer getRefundCount() {
		return refundCount;
	}

	public void setRefundCount(Integer refundCount) {
		this.refundCount = refundCount;
	}

	public Double getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(Double refundAmount) {
		this.refundAmount = refundAmount;
	}

	public Double getRefundDiscount() {
		return refundDiscount;
	}

	public void setRefundDiscount(Double refundDiscount) {
		this.refundDiscount = refundDiscount;
	}

	public Double getRefundPaidAmount() {
		return refundPaidAmount;
	}

	public void setRefundPaidAmount(Double refundPaidAmount) {
		this.refundPaidAmount = refundPaidAmount;
	}

	public Integer getTotalQuantity() {
		return totalQuantity;
	}

	public void setTotalQuantity(Integer totalQuantity) {
		this.totalQuantity = totalQuantity;
	}

	public Double getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(Double totalAmount) {
		this.totalAmount = totalAmount;
	}

	public Double getTotalDiscount() {
		return totalDiscount;
	}

	public void setTotalDiscount(Double totalDiscount) {
		this.totalDiscount = totalDiscount;
	}

	public Double getTotalPaidAmount() {
		return totalPaidAmount;
	}

	public void setTotalPaidAmount(Double totalPaidAmount) {
		this.totalPaidAmount = totalPaidAmount;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public Long getAddUserId() {
		return addUserId;
	}

	public void setAddUserId(Long addUserId) {
		this.addUserId = addUserId;
	}

	public String getUserGroupName() {
		return userGroupName;
	}

	public void setUserGroupName(String userGroupName) {
		this.userGroupName = userGroupName;
	}

	public Long getStadiumId() {
		return stadiumId;
	}

	public void setStadiumId(Long stadiumId) {
		this.stadiumId = stadiumId;
	}

	public String getStadiumName() {
		return stadiumName;
	}

	public void setStadiumName(String stadiumName) {
		this.stadiumName = stadiumName;
	}

	public Long getVenueId() {
		return venueId;
	}

	public void setVenueId(Long venueId) {
		this.venueId = venueId;
	}

	public String getVenueName() {
		return venueName;
	}

	public void setVenueName(String venueName) {
		this.venueName = venueName;
	}

	public Long getProgramId() {
		return programId;
	}

	public void setProgramId(Long programId) {
		this.programId = programId;
	}

	public String getProgramName() {
		return programName;
	}

	public void setProgramName(String programName) {
		this.programName = programName;
	}

	public Timestamp getPlayTime() {
		return playTime;
	}

	public void setPlayTime(Timestamp playTime) {
		this.playTime = playTime;
	}

	public Long getShowId() {
		return showId;
	}

	public void setShowId(Long showId) {
		this.showId = showId;
	}

	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	public Double getTicketPrice() {
		return ticketPrice;
	}

	public void setTicketPrice(Double ticketPrice) {
		this.ticketPrice = ticketPrice;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public String getProgramCode() {
		return programCode;
	}

	public void setProgramCode(String programCode) {
		this.programCode = programCode;
	}

	public String getSellType() {
		return sellType;
	}

	public void setSellType(String sellType) {
		this.sellType = sellType;
	}

	public Double getSettlementAmount() {
		return settlementAmount;
	}

	public void setSettlementAmount(Double settlementAmount) {
		this.settlementAmount = settlementAmount;
	}

	public String getAdditionInfo() {
		return additionInfo;
	}

	public void setAdditionInfo(String additionInfo) {
		this.additionInfo = additionInfo;
	}

	public String getRefundRowNos() {
		return refundRowNos;
	}

	public void setRefundRowNos(String refundRowNos) {
		this.refundRowNos = refundRowNos;
	}

	public String getRefundColNos() {
		return refundColNos;
	}

	public void setRefundColNos(String refundColNos) {
		this.refundColNos = refundColNos;
	}

	public Long getMemberLevelId() {
		return memberLevelId;
	}

	public void setMemberLevelId(Long memberLevelId) {
		this.memberLevelId = memberLevelId;
	}

	public String getMemberLevelName() {
		return memberLevelName;
	}

	public void setMemberLevelName(String memberLevelName) {
		this.memberLevelName = memberLevelName;
	}

	@Override
	public Serializable realId() {
		return id;
	}
}

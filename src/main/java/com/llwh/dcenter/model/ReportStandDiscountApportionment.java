package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;

import cn.fancylab.model.BaseObject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 站票折扣分摊表
 */
public class ReportStandDiscountApportionment extends BaseObject {

	private static final long serialVersionUID = -8186819468589834936L;
	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.INPUT)
	private String id;
	private Long discountId;
	/**
	 * 订单号
	 */
	private String tradeNo;
	private String uuid;
	/**
	 * 订单类型
	 */
	private String orderType;
	/**
	 * pay/refund
	 */
	private String payType;
	private String ruleName;
	/**
	 * 优惠分类(会员卡/会员/卡券/运营活动/积分/线下或外部)
	 */
	private String category;
	/**
	 * 关联ID(券批次ID/促销活动ID/等级优惠活动ID/积分分类/线下或外部）
	 */
	private Long categoryId;
	/**
	 * 类型：优惠券 ACP, points, promotion...
	 */
	private String cardtype;

	/**
	 * 卡/券号/
	 */
	private String cardNo;

	/**
	 * 积分
	 */
	private Integer pointValue;

	/**
	 * 状态：new（初始），lock(锁定），confirm（已确认）
	 */
	private String status;

	/**
	 * 折扣总金额
	 */
	private Double discountAmount;

	private Integer detailCount;
	/**
	 * 折扣说明
	 */
	private String description;
	/**
	 * Json: 其他信息
	 */
	private String otherinfo;
	private Long addUserId;
	private Long userGroupId;
	private Timestamp addtime;
	private Timestamp updatetime;
	private Long companyId;

	@Override
	public Serializable realId() {
		return this.id;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getDiscountId() {
		return discountId;
	}

	public void setDiscountId(Long discountId) {
		this.discountId = discountId;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getCardtype() {
		return cardtype;
	}

	public void setCardtype(String cardtype) {
		this.cardtype = cardtype;
	}

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public Integer getPointValue() {
		return pointValue;
	}

	public void setPointValue(Integer pointValue) {
		this.pointValue = pointValue;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Double getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(Double discountAmount) {
		this.discountAmount = discountAmount;
	}

	public Integer getDetailCount() {
		return detailCount;
	}

	public void setDetailCount(Integer detailCount) {
		this.detailCount = detailCount;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getOtherinfo() {
		return otherinfo;
	}

	public void setOtherinfo(String otherinfo) {
		this.otherinfo = otherinfo;
	}

	public Long getAddUserId() {
		return addUserId;
	}

	public void setAddUserId(Long addUserId) {
		this.addUserId = addUserId;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public Timestamp getAddtime() {
		return addtime;
	}

	public void setAddtime(Timestamp addtime) {
		this.addtime = addtime;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}
}

package com.llwh.dcenter.model;
import java.io.Serializable;
import java.sql.Timestamp;

import cn.fancylab.model.BaseObject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;


/**
 * 入场汇总表聚合表
 */
public class ReportTicketTransactionSummaryCombPay extends BaseObject {
	private static final long serialVersionUID = -7818375357650932599L;

	@TableId(value = "id", type = IdType.INPUT)
	private String id;

	/**
	 * 项目ID
	 */
	private Long programId;

	private String programName;

	private String tradeNo;


	private Long userGroupId;
	/**
	 * 用户组名称
	 */
	private String groupName;

	private Long addUserId;
	/**
	 * 用户名称
	 */
	private String addUserName;

	private Long scheduleId;
	/**
	 * 场次名称
	 */
	private String scheduleName;


	private Double paidAmount;


	/**
	 * 查询时间（下单时间和退款时间）
	 */
	private Timestamp time;

	/**
	 * 场次时间
	 */
	private Timestamp scheduleTime;
	/**
	 * 项目性质
	 */

	private String stadiumName;

	private Long stadiumId;

	private String venueName;

	private String venueId;

	/**
	 * 支付方式
	 */
	private String gatewayCode;

	private String merchantCode;
	private Long companyId;
	/**
	 * show 站票 schedule 座票
	 */
	private String ticketType;
	private String payType;
	private Timestamp updatetime;

	/**
	 * 来源
	 */
	private String source;
	/**
	 * 渠道
	 */
	private String sourceChannel;
	/**
	 * 数量，FIXME 该数量不对，待去掉，目前 BI 用的较多，暂时保留
	 */
	private Integer quantity;
	/**
	 * 销售类型
	 */
	private String sellType;
	private String sellTypeName;

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getSourceChannel() {
		return sourceChannel;
	}

	public void setSourceChannel(String sourceChannel) {
		this.sourceChannel = sourceChannel;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getProgramId() {
		return programId;
	}

	public void setProgramId(Long programId) {
		this.programId = programId;
	}

	public String getProgramName() {
		return programName;
	}

	public void setProgramName(String programName) {
		this.programName = programName;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Long getAddUserId() {
		return addUserId;
	}

	public void setAddUserId(Long addUserId) {
		this.addUserId = addUserId;
	}

	public String getAddUserName() {
		return addUserName;
	}

	public void setAddUserName(String addUserName) {
		this.addUserName = addUserName;
	}

	public Long getScheduleId() {
		return scheduleId;
	}

	public void setScheduleId(Long scheduleId) {
		this.scheduleId = scheduleId;
	}

	public String getScheduleName() {
		return scheduleName;
	}

	public void setScheduleName(String scheduleName) {
		this.scheduleName = scheduleName;
	}

	public Double getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(Double paidAmount) {
		this.paidAmount = paidAmount;
	}

	public Timestamp getTime() {
		return time;
	}

	public void setTime(Timestamp time) {
		this.time = time;
	}

	public Timestamp getScheduleTime() {
		return scheduleTime;
	}

	public void setScheduleTime(Timestamp scheduleTime) {
		this.scheduleTime = scheduleTime;
	}

	public String getStadiumName() {
		return stadiumName;
	}

	public void setStadiumName(String stadiumName) {
		this.stadiumName = stadiumName;
	}

	public Long getStadiumId() {
		return stadiumId;
	}

	public void setStadiumId(Long stadiumId) {
		this.stadiumId = stadiumId;
	}

	public String getVenueName() {
		return venueName;
	}

	public void setVenueName(String venueName) {
		this.venueName = venueName;
	}

	public String getVenueId() {
		return venueId;
	}

	public void setVenueId(String venueId) {
		this.venueId = venueId;
	}

	public String getGatewayCode() {
		return gatewayCode;
	}

	public void setGatewayCode(String gatewayCode) {
		this.gatewayCode = gatewayCode;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getTicketType() {
		return ticketType;
	}

	public void setTicketType(String ticketType) {
		this.ticketType = ticketType;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public String getMerchantCode() {
		return merchantCode;
	}

	public void setMerchantCode(String merchantCode) {
		this.merchantCode = merchantCode;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public String getSellType() {
		return sellType;
	}

	public void setSellType(String sellType) {
		this.sellType = sellType;
	}

	public String getSellTypeName() {
		return sellTypeName;
	}

	public void setSellTypeName(String sellTypeName) {
		this.sellTypeName = sellTypeName;
	}

	@Override
	public Serializable realId() {
		return id;
	}
}

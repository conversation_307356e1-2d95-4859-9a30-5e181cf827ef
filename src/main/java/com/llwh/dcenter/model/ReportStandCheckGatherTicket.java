package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.llwh.dcenter.common.CheckStandOrSeatName;

/**
 * 站票入场汇总表（按票）
 */
public class ReportStandCheckGatherTicket extends CheckStandOrSeatName {


	private static final long serialVersionUID = 6934346451323219L;

	@TableId(value = "id", type = IdType.INPUT)
	private String id;
	/**
	 * 核销日期
	 */
	private Date checkDate;
	/**
	 * 票种ID
	 */
	private Long ticketTypeId;
	/**
	 * 票种名称
	 */
	private String ticketTypeName;
	/**
	 * 入场人数
	 */
	private Integer checkCount;
	/**
	 * 票张数
	 */
	private Integer ticketNum;
	/**
	 * 企业ID
	 */
	private Long companyId;
	/**
	 * 更新时间
	 */
	private Timestamp updatetime;
	/**
	 * 核销用户ID/核销用户组ID
	 */
	private Long tbsUserId;
	/**
	 * 销售用户
	 */
	private Long saleUserId;
	/**
	 * 预约类型
	 */
	private String reserveType;


	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public Integer getCheckCount() {
		return checkCount;
	}

	public void setCheckCount(Integer checkCount) {
		this.checkCount = checkCount;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public Long getTbsUserId() {
		return tbsUserId;
	}

	public void setTbsUserId(Long tbsUserId) {
		this.tbsUserId = tbsUserId;
	}

	public Long getTicketTypeId() {
		return ticketTypeId;
	}

	public void setTicketTypeId(Long ticketTypeId) {
		this.ticketTypeId = ticketTypeId;
	}

	public String getTicketTypeName() {
		return ticketTypeName;
	}

	public void setTicketTypeName(String ticketTypeName) {
		this.ticketTypeName = ticketTypeName;
	}

	public Integer getTicketNum() {
		return ticketNum;
	}

	public void setTicketNum(Integer ticketNum) {
		this.ticketNum = ticketNum;
	}

	public Long getSaleUserId() {
		return saleUserId;
	}

	public void setSaleUserId(Long saleUserId) {
		this.saleUserId = saleUserId;
	}

	public String getReserveType() {
		return reserveType;
	}

	public void setReserveType(String reserveType) {
		this.reserveType = reserveType;
	}

	@Override
	public Serializable realId() {
		return id;
	}
}

package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;

import cn.fancylab.model.BaseObject;
import cn.fancylab.util.DateUtil;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;


public class DiscountApportionmentCheck extends BaseObject {

	private static final long serialVersionUID = 6934387456433219L;

	@TableId(value = "uuid", type = IdType.INPUT)
	private String uuid;
	private String orderType;
	/**
	 * 订单号
	 */
	private String tradeNo;

	/**
	 * 错误信息
	 */
	private String errMsg;

	private Long companyId;
	private Timestamp updatetime;

	public DiscountApportionmentCheck() {
	}

	public DiscountApportionmentCheck(String orderType, String tradeNo, String uuid, Long companyId) {
		this.orderType = orderType;
		this.tradeNo = tradeNo;
		this.uuid = uuid;
		this.companyId = companyId;
		this.updatetime = DateUtil.getCurFullTimestamp();
	}

	@Override
	public Serializable realId() {
		return uuid;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}
}

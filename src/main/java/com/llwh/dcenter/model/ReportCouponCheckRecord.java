package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;

import cn.fancylab.model.BaseObject;

import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 优惠券细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
public class ReportCouponCheckRecord extends BaseObject {
	private static final long serialVersionUID = 1181438452492350632L;
	@TableId
	private String id;

	private Timestamp checkTime;

	private String cardpass;

	private String cardno;

	private Long couponBatchId;

	private String couponBatchTitle;

	/**
	 * 核销用户ID
	 */
	private Long tbsUserId;

	/**
	 * 用户名称
	 */
	private String tbsUserName;

	/**
	 * 核销用户组ID
	 */
	private Long checkUserGroupId;

	/**
	 * 用户组名称
	 */
	private String checkUserGroupName;

	private Long merchantId;

	private String merchantUserName;
	private Long agencyId;
	private String agencyName;

	/**
	 * 注册人手机号
	 */
	private String mobile;

	/**
	 * 企业ID
	 */
	private Long companyId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Timestamp getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(Timestamp checkTime) {
		this.checkTime = checkTime;
	}

	public String getCardpass() {
		return cardpass;
	}

	public void setCardpass(String cardpass) {
		this.cardpass = cardpass;
	}

	public String getCardno() {
		return cardno;
	}

	public void setCardno(String cardno) {
		this.cardno = cardno;
	}

	public Long getCouponBatchId() {
		return couponBatchId;
	}

	public void setCouponBatchId(Long couponBatchId) {
		this.couponBatchId = couponBatchId;
	}

	public String getCouponBatchTitle() {
		return couponBatchTitle;
	}

	public void setCouponBatchTitle(String couponBatchTitle) {
		this.couponBatchTitle = couponBatchTitle;
	}

	public Long getTbsUserId() {
		return tbsUserId;
	}

	public void setTbsUserId(Long tbsUserId) {
		this.tbsUserId = tbsUserId;
	}

	public String getTbsUserName() {
		return tbsUserName;
	}

	public void setTbsUserName(String tbsUserName) {
		this.tbsUserName = tbsUserName;
	}

	public Long getCheckUserGroupId() {
		return checkUserGroupId;
	}

	public void setCheckUserGroupId(Long checkUserGroupId) {
		this.checkUserGroupId = checkUserGroupId;
	}

	public String getCheckUserGroupName() {
		return checkUserGroupName;
	}

	public void setCheckUserGroupName(String checkUserGroupName) {
		this.checkUserGroupName = checkUserGroupName;
	}

	public Long getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(Long merchantId) {
		this.merchantId = merchantId;
	}

	public String getMerchantUserName() {
		return merchantUserName;
	}

	public void setMerchantUserName(String merchantUserName) {
		this.merchantUserName = merchantUserName;
	}

	public Long getAgencyId() {
		return agencyId;
	}

	public void setAgencyId(Long agencyId) {
		this.agencyId = agencyId;
	}

	public String getAgencyName() {
		return agencyName;
	}

	public void setAgencyName(String agencyName) {
		this.agencyName = agencyName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	@Override
	public Serializable realId() {
		return id;
	}
}

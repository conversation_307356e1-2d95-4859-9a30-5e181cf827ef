package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;

import cn.fancylab.model.BaseObject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 商城仅退款单报表
 */
public class ReportMallOrderRefundOnly extends BaseObject {
	private static final long serialVersionUID = 83988017528465278L;
	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.INPUT)
	private String id;
	/**
	 * 企业ID
	 */
	private Long companyId;
	/**
	 * 更新时间
	 */
	private Timestamp updatetime;
	/**
	 * 退款申请时间
	 */
	@Column(updatable = false)
	private Timestamp refundAddtime;
	@Column(updatable = false)
	private Long agencyId;
	private String agencyName;
	@Column(updatable = false)
	private Long merchantId;
	private String merchantName;
	/**
	 * 退票流水号
	 */
	@Column(updatable = false)
	private String serialNo;
	/**
	 * 外部流水号
	 */
	@Column(updatable = false)
	private String outSerialNo;
	/**
	 * 关联订单号
	 */
	@Column(updatable = false)
	private String tradeNo;
	@Column(updatable = false)
	private String orderType;
	/**
	 * 标识退款途径，OrderFlag
	 */
	@Column(updatable = false)
	private String orderFlag;
	@Column(updatable = false)
	/**
	 * 申请用户组、用户
	 */
	private Long userGroupId;
	@Column(updatable = false)
	private Long addUserId;
	/**
	 * 审核用户组、用户、时间
	 */
	private Long auditGroupId; // 审核用户组
	private Long auditUserId; // 审核用户
	private Timestamp audittime; // 审核时间
	// ~~~~~~~~~~~~~~~~~~~~申请~~~~~~~~~~~~~~~~~~~~~~~~~~~
	/**
	 * 结算、退款金额
	 */
	@Column(updatable = false)
	private Double realPay;
	/**
	 * 会员退款理由，暂未使用
	 */
	private String reason;
	/**
	 * 后台页面退款理由及说明JSON
	 */
	private String refundReason;
	// ~~~~~~~~~~~~~~~~~~~~~~处理~~~~~~~~~~~~~~~~~~~~~~~~~~
	/**
	 * 退款成功时间
	 */
	private Timestamp refundtime;
	/**
	 * 退款支付方式
	 */
	private String gatewayCode;
	/**
	 * 退款支付流水号
	 */
	private String payRefundNo;
	/**
	 * 处理状态 RefundStatus
	 */
	private String refundStatus;
	/**
	 * 需要审核的原因标识
	 */
	private String needAuditReason;
	/**
	 * 拒绝备注
	 */
	private String rejectRemark;
	// ~~~~~~~~~~~~~~~~~~~~~~冗余~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	/**
	 * 原始订单支付时间
	 */
	@Column(updatable = false)
	private Double originPaidAmount;
	/**
	 * 原始订单支付时间
	 */
	@Column(updatable = false)
	private Timestamp originPaidtime;
	/**
	 * 原订单状态
	 */
	@Column(updatable = false)
	private String originStatus;
	/**
	 * 原订单支付流水
	 */
	@Column(updatable = false)
	private String originPayseqno;
	/**
	 * 原下单时间
	 */
	@Column(updatable = false)
	private Timestamp originAddtime;
	/**
	 * 是否归还库存，Y/N
	 */
	private String returnSku;
	private Long auditMerchantId;
	private String auditMerchantName;
	/**
	 * 会员，订单对应会员信息
	 */
	@Column(updatable = false)
	private Long memberId;
	private String memberName;
	/**
	 * 注册手机号
	 */
	private String mobile;
	/**
	 * 联系人
	 */
	private String contactName;
	private String contactMobile;

	private transient String userGroupName;
	private transient String addUserName;
	private transient String auditGroupName; // 审核用户组
	private transient String auditUserName; // 审核用户

	@Override
	public Serializable realId() {
		return this.id;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public Timestamp getRefundAddtime() {
		return refundAddtime;
	}

	public void setRefundAddtime(Timestamp refundAddtime) {
		this.refundAddtime = refundAddtime;
	}

	public Long getAgencyId() {
		return agencyId;
	}

	public void setAgencyId(Long agencyId) {
		this.agencyId = agencyId;
	}

	public String getAgencyName() {
		return agencyName;
	}

	public void setAgencyName(String agencyName) {
		this.agencyName = agencyName;
	}

	public Long getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(Long merchantId) {
		this.merchantId = merchantId;
	}

	public String getMerchantName() {
		return merchantName;
	}

	public void setMerchantName(String merchantName) {
		this.merchantName = merchantName;
	}

	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	public String getOutSerialNo() {
		return outSerialNo;
	}

	public void setOutSerialNo(String outSerialNo) {
		this.outSerialNo = outSerialNo;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getOrderFlag() {
		return orderFlag;
	}

	public void setOrderFlag(String orderFlag) {
		this.orderFlag = orderFlag;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public Long getAddUserId() {
		return addUserId;
	}

	public void setAddUserId(Long addUserId) {
		this.addUserId = addUserId;
	}

	public Long getAuditGroupId() {
		return auditGroupId;
	}

	public void setAuditGroupId(Long auditGroupId) {
		this.auditGroupId = auditGroupId;
	}

	public Long getAuditUserId() {
		return auditUserId;
	}

	public void setAuditUserId(Long auditUserId) {
		this.auditUserId = auditUserId;
	}

	public Timestamp getAudittime() {
		return audittime;
	}

	public void setAudittime(Timestamp audittime) {
		this.audittime = audittime;
	}

	public Double getRealPay() {
		return realPay;
	}

	public void setRealPay(Double realPay) {
		this.realPay = realPay;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getRefundReason() {
		return refundReason;
	}

	public void setRefundReason(String refundReason) {
		this.refundReason = refundReason;
	}

	public Timestamp getRefundtime() {
		return refundtime;
	}

	public void setRefundtime(Timestamp refundtime) {
		this.refundtime = refundtime;
	}

	public String getGatewayCode() {
		return gatewayCode;
	}

	public void setGatewayCode(String gatewayCode) {
		this.gatewayCode = gatewayCode;
	}

	public String getPayRefundNo() {
		return payRefundNo;
	}

	public void setPayRefundNo(String payRefundNo) {
		this.payRefundNo = payRefundNo;
	}

	public String getRefundStatus() {
		return refundStatus;
	}

	public void setRefundStatus(String refundStatus) {
		this.refundStatus = refundStatus;
	}

	public String getNeedAuditReason() {
		return needAuditReason;
	}

	public void setNeedAuditReason(String needAuditReason) {
		this.needAuditReason = needAuditReason;
	}

	public String getRejectRemark() {
		return rejectRemark;
	}

	public void setRejectRemark(String rejectRemark) {
		this.rejectRemark = rejectRemark;
	}

	public Double getOriginPaidAmount() {
		return originPaidAmount;
	}

	public void setOriginPaidAmount(Double originPaidAmount) {
		this.originPaidAmount = originPaidAmount;
	}

	public Timestamp getOriginPaidtime() {
		return originPaidtime;
	}

	public void setOriginPaidtime(Timestamp originPaidtime) {
		this.originPaidtime = originPaidtime;
	}

	public String getOriginStatus() {
		return originStatus;
	}

	public void setOriginStatus(String originStatus) {
		this.originStatus = originStatus;
	}

	public String getOriginPayseqno() {
		return originPayseqno;
	}

	public void setOriginPayseqno(String originPayseqno) {
		this.originPayseqno = originPayseqno;
	}

	public Timestamp getOriginAddtime() {
		return originAddtime;
	}

	public void setOriginAddtime(Timestamp originAddtime) {
		this.originAddtime = originAddtime;
	}

	public String getReturnSku() {
		return returnSku;
	}

	public void setReturnSku(String returnSku) {
		this.returnSku = returnSku;
	}

	public Long getAuditMerchantId() {
		return auditMerchantId;
	}

	public void setAuditMerchantId(Long auditMerchantId) {
		this.auditMerchantId = auditMerchantId;
	}

	public String getAuditMerchantName() {
		return auditMerchantName;
	}

	public void setAuditMerchantName(String auditMerchantName) {
		this.auditMerchantName = auditMerchantName;
	}

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactMobile() {
		return contactMobile;
	}

	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}

	public String getUserGroupName() {
		return userGroupName;
	}

	public void setUserGroupName(String userGroupName) {
		this.userGroupName = userGroupName;
	}

	public String getAddUserName() {
		return addUserName;
	}

	public void setAddUserName(String addUserName) {
		this.addUserName = addUserName;
	}

	public String getAuditGroupName() {
		return auditGroupName;
	}

	public void setAuditGroupName(String auditGroupName) {
		this.auditGroupName = auditGroupName;
	}

	public String getAuditUserName() {
		return auditUserName;
	}

	public void setAuditUserName(String auditUserName) {
		this.auditUserName = auditUserName;
	}
}

package com.llwh.dcenter.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import cn.fancylab.model.BaseObject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 预约及入场明细表
 */
public class ReportReserveCheckDetail extends BaseObject {

	private static final long serialVersionUID = -5544584779561528351L;
	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.INPUT)
	private Long id;
	/**
	 * 预约单号
	 */
	private String reserveNo;
	/**
	 * 预约项目
	 */
	private String programName;
	/**
	 * 预约日期
	 */
	private Date reservedate;
	/**
	 * 预约开始时间
	 */
	private Timestamp starttime;
	/**
	 * 预约结束时间
	 */
	private Timestamp endtime;
	/**
	 * 姓名
	 */
	private String realName;
	/**
	 * 证件类型
	 */
	private String certificateType;
	/**
	 * 证件号
	 */
	private String certificateNo;
	/**
	 * 预约手机号
	 */
	private String mobile;
	/**
	 * 下单时间
	 */
	private Timestamp addtime;
	/**
	 * 核销时间
	 */
	private Timestamp checkTime;
	/**
	 * 预约状态
	 */
	private String status;
	/**
	 * 更新时间
	 */
	private Timestamp updatetime;
	private Long reserveProgramId;
	private Long venueId;
	private Long stadiumId;
	private Long companyId;
	private Long userId;
	private Long userGroupId;
	/**
	 * 预约类型，reserve普通预约、pass通票预约、membership会员卡通票预约
	 */
	private String reserveType;
	/**
	 * 可入场人数-站票通票预约时为票种可入场人数，其他情况为1
	 */
	private Integer entrynum;
	/**
	 * 场馆名称
	 */
	private String stadiumName;
	/**
	 * 场地名称
	 */
	private String venueName;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getReserveNo() {
		return reserveNo;
	}

	public void setReserveNo(String reserveNo) {
		this.reserveNo = reserveNo;
	}

	public String getProgramName() {
		return programName;
	}

	public void setProgramName(String programName) {
		this.programName = programName;
	}

	public Date getReservedate() {
		return reservedate;
	}

	public void setReservedate(Date reservedate) {
		this.reservedate = reservedate;
	}


	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(String certificateType) {
		this.certificateType = certificateType;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Timestamp getStarttime() {
		return starttime;
	}

	public void setStarttime(Timestamp starttime) {
		this.starttime = starttime;
	}

	public Timestamp getEndtime() {
		return endtime;
	}

	public void setEndtime(Timestamp endtime) {
		this.endtime = endtime;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public Timestamp getAddtime() {
		return addtime;
	}

	public void setAddtime(Timestamp addtime) {
		this.addtime = addtime;
	}

	public Timestamp getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(Timestamp checkTime) {
		this.checkTime = checkTime;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public Long getReserveProgramId() {
		return reserveProgramId;
	}

	public void setReserveProgramId(Long reserveProgramId) {
		this.reserveProgramId = reserveProgramId;
	}

	public Long getVenueId() {
		return venueId;
	}

	public void setVenueId(Long venueId) {
		this.venueId = venueId;
	}

	public Long getStadiumId() {
		return stadiumId;
	}

	public void setStadiumId(Long stadiumId) {
		this.stadiumId = stadiumId;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getUserGroupId() {
		return userGroupId;
	}

	public void setUserGroupId(Long userGroupId) {
		this.userGroupId = userGroupId;
	}

	public String getReserveType() {
		return reserveType;
	}

	public void setReserveType(String reserveType) {
		this.reserveType = reserveType;
	}

	public Integer getEntrynum() {
		return entrynum;
	}

	public void setEntrynum(Integer entrynum) {
		this.entrynum = entrynum;
	}

	public String getStadiumName() {
		return stadiumName;
	}

	public void setStadiumName(String stadiumName) {
		this.stadiumName = stadiumName;
	}

	public String getVenueName() {
		return venueName;
	}

	public void setVenueName(String venueName) {
		this.venueName = venueName;
	}

	@Override
	public Serializable realId() {
		return id;
	}
}

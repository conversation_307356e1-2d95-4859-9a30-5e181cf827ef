package com.llwh.dcenter.vo.common;

import java.io.Serializable;

public class PaymentGatewayVo implements Serializable {
	private static final long serialVersionUID = -6425542236803164004L;

	private Long id;
	private String gatewayName;
	private String gatewayCode;
	private String merchantCode;
	private String paybank;
	private String redirectUri;
	private String status;
	private Integer sortnum;
	private String paySource;
	private String weixinSupport; // 微信支持

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public String getGatewayCode() {
		return gatewayCode;
	}

	public void setGatewayCode(String gatewayCode) {
		this.gatewayCode = gatewayCode;
	}

	public String getMerchantCode() {
		return merchantCode;
	}

	public void setMerchantCode(String merchantCode) {
		this.merchantCode = merchantCode;
	}

	public String getPaybank() {
		return paybank;
	}

	public void setPaybank(String paybank) {
		this.paybank = paybank;
	}

	public String getRedirectUri() {
		return redirectUri;
	}

	public void setRedirectUri(String redirectUri) {
		this.redirectUri = redirectUri;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getSortnum() {
		return sortnum;
	}

	public void setSortnum(Integer sortnum) {
		this.sortnum = sortnum;
	}

	public String getPaySource() {
		return paySource;
	}

	public void setPaySource(String paySource) {
		this.paySource = paySource;
	}

	public String getWeixinSupport() {
		return weixinSupport;
	}

	public void setWeixinSupport(String weixinSupport) {
		this.weixinSupport = weixinSupport;
	}
}

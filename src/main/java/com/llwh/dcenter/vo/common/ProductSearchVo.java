package com.llwh.dcenter.vo.common;

import java.io.Serializable;

import cn.fancylab.api.vo.BaseVo;

public class ProductSearchVo extends BaseVo {
	private static final long serialVersionUID = -186863256570255582L;
	private Long id;
	private String productName;
	private String search;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSearch() {
		return search;
	}

	public void setSearch(String search) {
		this.search = search;
	}

	@Override
	public Serializable realId() {
		return this.id;
	}
}

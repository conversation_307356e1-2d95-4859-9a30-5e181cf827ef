package com.llwh.dcenter.vo.report;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;



public class ReportMemberinfoStatisticsVo {
    @TableId(value = "id", type = IdType.INPUT)
    private String id;
    /**
     * 会员ID
     */
    private Long memberId;
    /**
     * 会员名称
     */
    private String name;
    /**
     * 会员等级信息
     */
    private Long memberLevelId;
    /**
     * 会员等级名称
     */
    private String memberLevelName;
    /**
     * 有效期开始时间
     */
    private Timestamp beginTime;
    /**
     * 有效期结束时间
     */
    private Timestamp endTime;
    /**
     * 有效成长值
     */
    private Long effectiveGrowthValue;
    /**
     * 积分
     */
    private Integer point;

    private Timestamp addtime;

    private Timestamp updatetime;
    /**
     * 姓名
     */
    private String realname;
    /**
     * 性别 1男 2女 0未知
     */
    private String gender;
    /**
     * 出生年月日(yyyy-MM-dd)
     */
    private Timestamp birthday;
    /**
     * 生日（MMdd）
     */
    private String birthDate;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 会员卡号
     */
    private String cardNo;
    /**
     * '状态： Y：表示已开通会员，P：表示临时暂停状态（各类积分、成长值正常记录）N：未开通会员，memberLevelId为0时的状态'
     */
    private String status;
    /**
     * 会员激活时间
     */
    private Timestamp activeTime;

    private String dynamicJsonValue;

    private String address;

    private String postcard;

    private String telephone;
    /**
     * 演出形式
     */
    private String favorite;
    /**
     * 会员活动
     */
    private String memberActivitieType;
    /**
     * 合理票价
     */
    private String preferredPrice;
    /**
     * 消费总金额
     */
    private Double expendTotalAmount;
    /**
     * 购买场次数
     */
    private Integer buyScheduleCount;
    /**
     * 购票总张数
     */
    private Integer buyCount;


    /**
     * 条件查询消费金额
     */
    private Double searchExpendTotalAmount;

    public String getPostcard() {
        return postcard;
    }

    public void setPostcard(String postcard) {
        this.postcard = postcard;
    }

    /**
     * 企业ID
     */
    private Integer companyId;

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getSearchExpendTotalAmount() {
        return searchExpendTotalAmount;
    }

    public void setSearchExpendTotalAmount(Double searchExpendTotalAmount) {
        this.searchExpendTotalAmount = searchExpendTotalAmount;
    }


    public String getFavorite() {
        return favorite;
    }

    public void setFavorite(String favorite) {
        this.favorite = favorite;
    }

    public String getMemberActivitieType() {
        return memberActivitieType;
    }

    public void setMemberActivitieType(String memberActivitieType) {
        this.memberActivitieType = memberActivitieType;
    }

    public String getPreferredPrice() {
        return preferredPrice;
    }

    public void setPreferredPrice(String preferredPrice) {
        this.preferredPrice = preferredPrice;
    }

    public Double getExpendTotalAmount() {
        return expendTotalAmount;
    }

    public void setExpendTotalAmount(Double expendTotalAmount) {
        this.expendTotalAmount = expendTotalAmount;
    }

    public Integer getBuyScheduleCount() {
        return buyScheduleCount;
    }

    public void setBuyScheduleCount(Integer buyScheduleCount) {
        this.buyScheduleCount = buyScheduleCount;
    }

    public Integer getBuyCount() {
        return buyCount;
    }

    public void setBuyCount(Integer buyCount) {
        this.buyCount = buyCount;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getMemberLevelId() {
        return memberLevelId;
    }

    public void setMemberLevelId(Long memberLevelId) {
        this.memberLevelId = memberLevelId;
    }

    public String getMemberLevelName() {
        return memberLevelName;
    }

    public void setMemberLevelName(String memberLevelName) {
        this.memberLevelName = memberLevelName;
    }

    public Timestamp getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Timestamp beginTime) {
        this.beginTime = beginTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public Long getEffectiveGrowthValue() {
        return effectiveGrowthValue;
    }

    public void setEffectiveGrowthValue(Long effectiveGrowthValue) {
        this.effectiveGrowthValue = effectiveGrowthValue;
    }

    public Integer getPoint() {
        return point;
    }

    public void setPoint(Integer point) {
        this.point = point;
    }

    public Timestamp getAddtime() {
        return addtime;
    }

    public void setAddtime(Timestamp addtime) {
        this.addtime = addtime;
    }

    public Timestamp getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Timestamp updatetime) {
        this.updatetime = updatetime;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Timestamp getBirthday() {
        return birthday;
    }

    public void setBirthday(Timestamp birthday) {
        this.birthday = birthday;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Timestamp getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(Timestamp activeTime) {
        this.activeTime = activeTime;
    }

    public String getDynamicJsonValue() {
        return dynamicJsonValue;
    }

    public void setDynamicJsonValue(String dynamicJsonValue) {
        this.dynamicJsonValue = dynamicJsonValue;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }
}

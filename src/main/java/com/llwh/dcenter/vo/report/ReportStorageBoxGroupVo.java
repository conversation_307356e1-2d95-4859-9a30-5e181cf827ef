package com.llwh.dcenter.vo.report;

import java.io.Serializable;
import java.sql.Date;

import cn.fancylab.api.vo.BaseVo;

public class ReportStorageBoxGroupVo extends BaseVo {
    private static final long serialVersionUID = 6403762212564183085L;
    /**
     * 支付日期
     */
    private Date paidDate;
    /**
     * 设备id
     */
    private Long storageBoxId;
    /**
     * 格口类型id
     */
    private Long cellTypeId;
    /**
     * 寄存柜类型（储物柜；存车柜）
     */
    private String storageBoxType;
    /**
     * 总支付订单数量
     */
    private Integer payQuantity;
    /**
     * 订单总金额
     */
    private Double payAmount;
    /**
     * 订单优惠总金额
     */
    private Double payDiscount;
    /**
     * 订单实收金额
     */
    private Double payPaidAmount;
    /**
     * 退款订单数量
     */
    private Integer refundQuantity;
    /**
     * 退款订单总金额
     */
    private Double refundAmount;
    /**
     * 退款订单优惠总金额
     */
    private Double refundDiscount;
    /**
     * 退款订单实收金额
     */
    private Double refundPaidAmount;
    /**
     * 订单数量
     */
    private Integer quantity;
    /**
     * 订单总金额
     */
	private Double amount;
    /**
     * 订单优惠金额
     */
    private Double discount;
    /**
     * 订单应收金额
     */
    private Double paidAmount;


    @Override
    public Serializable realId() {
        return null;
    }

    public Date getPaidDate() {
        return paidDate;
    }

    public void setPaidDate(Date paidDate) {
        this.paidDate = paidDate;
    }

    public Long getStorageBoxId() {
        return storageBoxId;
    }

    public void setStorageBoxId(Long storageBoxId) {
        this.storageBoxId = storageBoxId;
    }

    public Long getCellTypeId() {
        return cellTypeId;
    }

    public void setCellTypeId(Long cellTypeId) {
        this.cellTypeId = cellTypeId;
    }

    public String getStorageBoxType() {
        return storageBoxType;
    }

    public void setStorageBoxType(String storageBoxType) {
        this.storageBoxType = storageBoxType;
    }

    public Integer getPayQuantity() {
        return payQuantity;
    }

    public void setPayQuantity(Integer payQuantity) {
        this.payQuantity = payQuantity;
    }

    public Double getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(Double payAmount) {
        this.payAmount = payAmount;
    }

    public Double getPayDiscount() {
        return payDiscount;
    }

    public void setPayDiscount(Double payDiscount) {
        this.payDiscount = payDiscount;
    }

    public Double getPayPaidAmount() {
        return payPaidAmount;
    }

    public void setPayPaidAmount(Double payPaidAmount) {
        this.payPaidAmount = payPaidAmount;
    }

    public Integer getRefundQuantity() {
        return refundQuantity;
    }

    public void setRefundQuantity(Integer refundQuantity) {
        this.refundQuantity = refundQuantity;
    }

    public Double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Double getRefundDiscount() {
        return refundDiscount;
    }

    public void setRefundDiscount(Double refundDiscount) {
        this.refundDiscount = refundDiscount;
    }

    public Double getRefundPaidAmount() {
        return refundPaidAmount;
    }

    public void setRefundPaidAmount(Double refundPaidAmount) {
        this.refundPaidAmount = refundPaidAmount;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Double getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Double paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

}

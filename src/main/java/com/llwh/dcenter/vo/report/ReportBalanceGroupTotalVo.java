package com.llwh.dcenter.vo.report;

import java.sql.Date;

import cn.fancylab.api.vo.BaseVo;

public class ReportBalanceGroupTotalVo extends BaseVo {
    private static final long serialVersionUID = 506071654687194965L;
	/**
     * 会员卡种ID
     */
    private String membershipTypeId;
    /**
     * 交易日期
     */
    private Date tradeDate;
    /**
     * 项目ID
     */
    private Long programId;
    /**
     * 场次ID
     */
    private Long showId;
    /**
     * 场次ID
     */
    private String showName;
    private Double refundAmount;
	private Double spendAmount;
    private Double sumAmount;

    public String getMembershipTypeId() {
        return membershipTypeId;
    }

    public void setMembershipTypeId(String membershipTypeId) {
        this.membershipTypeId = membershipTypeId;
    }

    public Date getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(Date tradeDate) {
        this.tradeDate = tradeDate;
    }

    public Long getProgramId() {
        return programId;
    }

    public void setProgramId(Long programId) {
        this.programId = programId;
    }

    public Long getShowId() {
        return showId;
    }

    public void setShowId(Long showId) {
        this.showId = showId;
    }

    public Double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Double getSpendAmount() {
        return spendAmount;
    }

    public void setSpendAmount(Double spendAmount) {
        this.spendAmount = spendAmount;
    }

    public Double getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(Double sumAmount) {
        this.sumAmount = sumAmount;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }
}

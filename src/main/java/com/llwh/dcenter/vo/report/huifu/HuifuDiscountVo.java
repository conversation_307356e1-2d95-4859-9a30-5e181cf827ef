package com.llwh.dcenter.vo.report.huifu;

import cn.fancylab.api.vo.BaseVo;

public class HuifuDiscountVo extends BaseVo {
	private static final long serialVersionUID = -15234233092223989L;
	private String tradeNo;
	private Double discountAmount;
	private String category;
	private String platformSubsidy;

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public Double getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(Double discountAmount) {
		this.discountAmount = discountAmount;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getPlatformSubsidy() {
		return platformSubsidy;
	}

	public void setPlatformSubsidy(String platformSubsidy) {
		this.platformSubsidy = platformSubsidy;
	}
}

package com.llwh.dcenter.vo.report;

import java.io.Serializable;

import cn.fancylab.api.vo.BaseVo;

/**
 * 今日票价统计
 */
public class ReportTodaySaleVo extends BaseVo {

	private static final long serialVersionUID = 998777656432862L;

	private Long ticketPriceId;
	private Double ticketPrice;
	private Integer saleCount;
	private Double saleAmount;

	public Long getTicketPriceId() {
		return ticketPriceId;
	}

	public void setTicketPriceId(Long ticketPriceId) {
		this.ticketPriceId = ticketPriceId;
	}

	public Double getTicketPrice() {
		return ticketPrice;
	}

	public void setTicketPrice(Double ticketPrice) {
		this.ticketPrice = ticketPrice;
	}

	public Integer getSaleCount() {
		return saleCount;
	}

	public void setSaleCount(Integer saleCount) {
		this.saleCount = saleCount;
	}

	public Double getSaleAmount() {
		return saleAmount;
	}

	public void setSaleAmount(Double saleAmount) {
		this.saleAmount = saleAmount;
	}

	@Override
	public Serializable realId() {
		return ticketPriceId;
	}
}

package com.llwh.dcenter.vo.report;


/**
 * 会员等级卡号
 */
public class ReportMemberLevelCardNoVo  {


	private String cardNo;
	private Long memberId;

	private Long companyId;
	private Long memberLevelId;
	private String memberLevelName;
	private String realname;
	private Integer growthValue;
	private String status;
	private Long membershipLevelId;
	private String membershipTypeName;
	private String membershipLevelName;
	private Integer membershipGrowthValue;
	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getMemberLevelId() {
		return memberLevelId;
	}

	public void setMemberLevelId(Long memberLevelId) {
		this.memberLevelId = memberLevelId;
	}

	public String getMemberLevelName() {
		return memberLevelName;
	}

	public void setMemberLevelName(String memberLevelName) {
		this.memberLevelName = memberLevelName;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Long getMembershipLevelId() {
		return membershipLevelId;
	}

	public void setMembershipLevelId(Long membershipLevelId) {
		this.membershipLevelId = membershipLevelId;
	}

	public String getMembershipTypeName() {
		return membershipTypeName;
	}

	public void setMembershipTypeName(String membershipTypeName) {
		this.membershipTypeName = membershipTypeName;
	}

	public Integer getGrowthValue() {
		return growthValue;
	}

	public void setGrowthValue(Integer growthValue) {
		this.growthValue = growthValue;
	}

	public String getMembershipLevelName() {
		return membershipLevelName;
	}

	public void setMembershipLevelName(String membershipLevelName) {
		this.membershipLevelName = membershipLevelName;
	}

	public Integer getMembershipGrowthValue() {
		return membershipGrowthValue;
	}

	public void setMembershipGrowthValue(Integer membershipGrowthValue) {
		this.membershipGrowthValue = membershipGrowthValue;
	}

	public String getRealname() {
		return realname;
	}

	public void setRealname(String realname) {
		this.realname = realname;
	}
}

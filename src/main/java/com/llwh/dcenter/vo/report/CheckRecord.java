package com.llwh.dcenter.vo.report;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

public class CheckRecord implements Serializable {
	private static final long serialVersionUID = 256532782044305775L;
	private Long id;
	private String uuid;
	private String deviceId;
	private String certificateMd5;
	private String reserveType;
	private Date checkDate;
	private Timestamp checkTime;
	private String photoId;
	private Integer checkCount;
	private Integer recheckCount;
	private Timestamp recheckTime;
	private Timestamp updatetime;
	private String source;
	private Long ticketTypeId;
	private Long tbsUserId;
	private Long companyId;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public String getCertificateMd5() {
		return certificateMd5;
	}

	public void setCertificateMd5(String certificateMd5) {
		this.certificateMd5 = certificateMd5;
	}

	public String getReserveType() {
		return reserveType;
	}

	public void setReserveType(String reserveType) {
		this.reserveType = reserveType;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public Timestamp getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(Timestamp checkTime) {
		this.checkTime = checkTime;
	}

	public String getPhotoId() {
		return photoId;
	}

	public void setPhotoId(String photoId) {
		this.photoId = photoId;
	}

	public Integer getCheckCount() {
		return checkCount;
	}

	public void setCheckCount(Integer checkCount) {
		this.checkCount = checkCount;
	}

	public Integer getRecheckCount() {
		return recheckCount;
	}

	public void setRecheckCount(Integer recheckCount) {
		this.recheckCount = recheckCount;
	}

	public Timestamp getRecheckTime() {
		return recheckTime;
	}

	public void setRecheckTime(Timestamp recheckTime) {
		this.recheckTime = recheckTime;
	}

	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public Long getTicketTypeId() {
		return ticketTypeId;
	}

	public void setTicketTypeId(Long ticketTypeId) {
		this.ticketTypeId = ticketTypeId;
	}

	public Long getTbsUserId() {
		return tbsUserId;
	}

	public void setTbsUserId(Long tbsUserId) {
		this.tbsUserId = tbsUserId;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

}

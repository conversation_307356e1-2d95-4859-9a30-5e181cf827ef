package com.llwh.dcenter.vo.report.stand;

import cn.fancylab.api.vo.BaseVo;

/**
 * <AUTHOR>
 */
public class ReportProgramDiscountCountVo extends BaseVo {
	private static final long serialVersionUID = 7340509221769293449L;
	/**
	 * 销售订单号
	 */
	private String tradeNo;
	/**
	 * 票号
	 */
	private String uuid;
	/**
	 * 项目ID
	 */
	private Long programId;
	/**
	 * 项目名称
	 */
	private String programName;
	/**
	 * 票面金额
	 */
	private Double ticketPrice;
	/**
	 * 同价套票
	 */
	private Double samepriceDiscount;
	/**
	 * 会员折扣
	 */
	private Double membershipDiscount;
	/**
	 * 积分抵现
	 */
	private Double pointsDiscount;
	/**
	 * 促销活动
	 */
	private Double promotionDiscount;
	/**
	 * 优惠券
	 */
	private Double couponDiscount;
	/**
	 * 手工折扣
	 */
	private Double ruleDiscount;
	/**
	 * 跨场套票/预售票（跨场套票优惠）
	 */
	private Double packDiscount;
	/**
	 * 次卡兑换优惠
	 */
	private Double multicardDiscount;
	/**
	 * 储值卡抵现
	 */
	private Double balanceDiscount;
	/**
	 * 合计支付金额
	 */
	private Double totalPaidAmount;

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public Long getProgramId() {
		return programId;
	}

	public void setProgramId(Long programId) {
		this.programId = programId;
	}

	public String getProgramName() {
		return programName;
	}

	public void setProgramName(String programName) {
		this.programName = programName;
	}

	public Double getTicketPrice() {
		return ticketPrice;
	}

	public void setTicketPrice(Double ticketPrice) {
		this.ticketPrice = ticketPrice;
	}

	public Double getSamepriceDiscount() {
		return samepriceDiscount;
	}

	public void setSamepriceDiscount(Double samepriceDiscount) {
		this.samepriceDiscount = samepriceDiscount;
	}

	public Double getMembershipDiscount() {
		return membershipDiscount;
	}

	public void setMembershipDiscount(Double membershipDiscount) {
		this.membershipDiscount = membershipDiscount;
	}

	public Double getPointsDiscount() {
		return pointsDiscount;
	}

	public void setPointsDiscount(Double pointsDiscount) {
		this.pointsDiscount = pointsDiscount;
	}

	public Double getPromotionDiscount() {
		return promotionDiscount;
	}

	public void setPromotionDiscount(Double promotionDiscount) {
		this.promotionDiscount = promotionDiscount;
	}

	public Double getCouponDiscount() {
		return couponDiscount;
	}

	public void setCouponDiscount(Double couponDiscount) {
		this.couponDiscount = couponDiscount;
	}

	public Double getRuleDiscount() {
		return ruleDiscount;
	}

	public void setRuleDiscount(Double ruleDiscount) {
		this.ruleDiscount = ruleDiscount;
	}

	public Double getPackDiscount() {
		return packDiscount;
	}

	public void setPackDiscount(Double packDiscount) {
		this.packDiscount = packDiscount;
	}

	public Double getMulticardDiscount() {
		return multicardDiscount;
	}

	public void setMulticardDiscount(Double multicardDiscount) {
		this.multicardDiscount = multicardDiscount;
	}

	public Double getBalanceDiscount() {
		return balanceDiscount;
	}

	public void setBalanceDiscount(Double balanceDiscount) {
		this.balanceDiscount = balanceDiscount;
	}

	public Double getTotalPaidAmount() {
		return totalPaidAmount;
	}

	public void setTotalPaidAmount(Double totalPaidAmount) {
		this.totalPaidAmount = totalPaidAmount;
	}
}

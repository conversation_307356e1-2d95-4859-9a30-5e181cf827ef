package com.llwh.dcenter.vo.report;

import java.io.Serializable;

import cn.fancylab.api.vo.BaseVo;

public class ApportionmentCheckVo extends BaseVo {
    private static final long serialVersionUID = 2986276060088712887L;
	private String tradeNo;
    private String uuid;
    private String orderType;
    private Double discount;
    private Long companyId;



    @Override
    public Serializable realId() {
        return null;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}

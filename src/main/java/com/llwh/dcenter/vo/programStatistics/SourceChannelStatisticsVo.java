package com.llwh.dcenter.vo.programStatistics;

import java.util.List;
import java.util.Map;

import cn.fancylab.api.vo.BaseVo;

public class SourceChannelStatisticsVo<T extends BaseVo> extends BaseVo {
	private static final long serialVersionUID = -96545123126860L;
	private String source;
	private String sourceChannel;
	private List<T> dataList;
	/**
	 * 合计数据
	 */
	private Map sumMap;

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getSourceChannel() {
		return sourceChannel;
	}

	public void setSourceChannel(String sourceChannel) {
		this.sourceChannel = sourceChannel;
	}

	public List<T> getDataList() {
		return dataList;
	}

	public void setDataList(List<T> dataList) {
		this.dataList = dataList;
	}

	public Map getSumMap() {
		return sumMap;
	}

	public void setSumMap(Map sumMap) {
		this.sumMap = sumMap;
	}
}

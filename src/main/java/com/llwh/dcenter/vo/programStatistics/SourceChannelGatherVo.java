package com.llwh.dcenter.vo.programStatistics;

import java.util.List;
import java.util.Map;

import cn.fancylab.api.vo.BaseVo;

public class SourceChannelGatherVo extends BaseVo {
	private static final long serialVersionUID = -9654986546860L;
	private String source;
	private String sourceChannel;
	private String sellType;
	private String programName;
	private String showName;
	private List<Map> ticketPriceMaps;


	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getSourceChannel() {
		return sourceChannel;
	}

	public void setSourceChannel(String sourceChannel) {
		this.sourceChannel = sourceChannel;
	}

	public String getSellType() {
		return sellType;
	}

	public void setSellType(String sellType) {
		this.sellType = sellType;
	}

	public String getProgramName() {
		return programName;
	}

	public void setProgramName(String programName) {
		this.programName = programName;
	}

	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	public List<Map> getTicketPriceMaps() {
		return ticketPriceMaps;
	}

	public void setTicketPriceMaps(List<Map> ticketPriceMaps) {
		this.ticketPriceMaps = ticketPriceMaps;
	}
}

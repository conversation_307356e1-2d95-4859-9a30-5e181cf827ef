package com.llwh.dcenter.vo.wlj;

import java.sql.Timestamp;

public class WljProgramVo {
	private Long id;
	private String programName;
	private String fullCnName;
	private String programCode;
	private String area;

	private Timestamp updatetime;

	/**
	 * 方形海报
	 */
	private String extraPoster;
	private String horizontalPoster;
	private String verticalPoster;

	/**
	 * 项目详情
	 */
	private String programInfo;

	/**
	 * 演员列表
	 */
	private String castList;

	/**
	 * 批文号
	 */
	private String approvalNum;

	/**
	 * 场馆名称
	 */
	private String stadiumName;

	/**
	 * 场馆地址
	 */
	private String stadiumAddress;

	private String showIdCode;

	/**
	 * 演出类型
	 */
	private String showType;
	/**
	 * 是否支持座位
	 */
	private String supportSeat;
	private Long companyId;

	public String getShowIdCode() {
		return showIdCode;
	}

	public void setShowIdCode(String showIdCode) {
		this.showIdCode = showIdCode;
	}

	public String getStadiumName() {
		return stadiumName;
	}

	public void setStadiumName(String stadiumName) {
		this.stadiumName = stadiumName;
	}

	public String getStadiumAddress() {
		return stadiumAddress;
	}

	public void setStadiumAddress(String stadiumAddress) {
		this.stadiumAddress = stadiumAddress;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getProgramName() {
		return programName;
	}

	public void setProgramName(String programName) {
		this.programName = programName;
	}

	public String getFullCnName() {
		return fullCnName;
	}

	public void setFullCnName(String fullCnName) {
		this.fullCnName = fullCnName;
	}

	public String getProgramCode() {
		return programCode;
	}

	public void setProgramCode(String programCode) {
		this.programCode = programCode;
	}

	public String getExtraPoster() {
		return extraPoster;
	}

	public void setExtraPoster(String extraPoster) {
		this.extraPoster = extraPoster;
	}

	public String getProgramInfo() {
		return programInfo;
	}

	public void setProgramInfo(String programInfo) {
		this.programInfo = programInfo;
	}

	public String getCastList() {
		return castList;
	}

	public void setCastList(String castList) {
		this.castList = castList;
	}


	public Timestamp getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Timestamp updatetime) {
		this.updatetime = updatetime;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getApprovalNum() {
		return approvalNum;
	}

	public void setApprovalNum(String approvalNum) {
		this.approvalNum = approvalNum;
	}

	public String getShowType() {
		return showType;
	}

	public void setShowType(String showType) {
		this.showType = showType;
	}

	public String getSupportSeat() {
		return supportSeat;
	}

	public void setSupportSeat(String supportSeat) {
		this.supportSeat = supportSeat;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getHorizontalPoster() {
		return horizontalPoster;
	}

	public void setHorizontalPoster(String horizontalPoster) {
		this.horizontalPoster = horizontalPoster;
	}

	public String getVerticalPoster() {
		return verticalPoster;
	}

	public void setVerticalPoster(String verticalPoster) {
		this.verticalPoster = verticalPoster;
	}
}

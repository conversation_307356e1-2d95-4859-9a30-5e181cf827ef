package com.llwh.dcenter.job.impl;

import cn.fancylab.job.JobService;

import com.llwh.dcenter.untrans.report.CombPayFlowUntransService;
import com.llwh.dcenter.untrans.report.CombRefundFlowUntransService;
import com.llwh.dcenter.untrans.report.DepositUntransService;
import com.llwh.dcenter.untrans.report.ReportAdditionalProductSaleDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportCheckRecordUntransService;
import com.llwh.dcenter.untrans.report.ReportCouponCheckRecordUntransService;
import com.llwh.dcenter.untrans.report.ReportDouyinSeatSaleCheckCpsDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportDouyinStandSaleCheckCpsDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportGroupReserveCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportGroupReserveCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportHuifuDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMallCheckRecordUntransService;
import com.llwh.dcenter.untrans.report.ReportMallDiscountApportionmentUntransService;
import com.llwh.dcenter.untrans.report.ReportMallOrderDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMallOrderRefundOnlyUntransService;
import com.llwh.dcenter.untrans.report.ReportMallRentAmountDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMallRentStatusDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMealDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMemberInfoCountByDayUntransService;
import com.llwh.dcenter.untrans.report.ReportMemberLevelCountByDayUntransService;
import com.llwh.dcenter.untrans.report.ReportMembershipCountByDayUntransService;
import com.llwh.dcenter.untrans.report.ReportMembershipMulticardDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportOrderDiscountDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportProgramUntransService;
import com.llwh.dcenter.untrans.report.ReportReserveCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportReserveCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatDiscountApportionmentUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatOrderDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherDayUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherProgramUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherSeatAttrUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherVenueAreaUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatTicketChangeDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportShowTicketTypeSaleGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportSportCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportSportSaleGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportStandCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportStandCheckGatherTicketUntransService;
import com.llwh.dcenter.untrans.report.ReportStandCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportStandDiscountApportionmentUntransService;
import com.llwh.dcenter.untrans.report.ReportStandSaleGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportStandTicketChangeDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportStorageBoxDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportTicketTransactionSummaryCombPayUntransService;
import com.llwh.dcenter.untrans.report.ReportTicketTransactionSummaryUntransService;
import com.llwh.dcenter.untrans.report.SeatApportionmentCheckUntransService;
import com.llwh.dcenter.untrans.report.StandApportionmentCheckUntransService;
import com.llwh.dcenter.untrans.report.impl.ReportStandSaleCheckGatherUntransServiceImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 数据库：theatre
 * 以下定时任务自theater数据库并汇聚进入报表
 */
@Service("OrderReportJob")
public class OrderReportJobImpl extends JobService {

	@Autowired
	private ReportStandCheckDetailUntransService standCheckDetailUntransService;
	@Autowired
	private ReportReserveCheckDetailUntransService reserveCheckDetailUntransService;
	@Autowired
	private ReportStandSaleCheckGatherUntransServiceImpl standSaleCheckGatherUntransService;
	@Autowired
	private ReportReserveCheckGatherUntransService reserveCheckGatherUntransService;
	@Autowired
	private ReportStandSaleGatherUntransService standSaleGatherUntransService;
	@Autowired
	private ReportStandCheckGatherUntransService standCheckGatherUntransService;
	@Autowired
	private ReportSeatSaleCheckDetailUntransService seatSaleCheckDetailUntransService;
	@Autowired
	private ReportDouyinSeatSaleCheckCpsDetailUntransService douyinSeatSaleCheckCpsDetailUntransService;
	@Autowired
	private ReportDouyinStandSaleCheckCpsDetailUntransService douyinSeatStandCheckCpsDetailUntransService;
	@Autowired
	private ReportGroupReserveCheckDetailUntransService groupReserveCheckDetailUntransService;
	@Autowired
	private ReportGroupReserveCheckGatherUntransService groupReserveCheckGatherUntransService;
	@Autowired
	private ReportSeatSaleGatherDayUntransService seatSaleGatherDayUntransService;
	@Autowired
	private ReportSeatSaleGatherProgramUntransService seatSaleGatherProgramUntransService;
	@Autowired
	private ReportSeatCheckGatherUntransService seatCheckGatherUntransService;
	@Autowired
	private ReportCheckRecordUntransService checkRecordUntransService;
	@Autowired
	private ReportStandCheckGatherTicketUntransService standCheckGatherTicketUntransService;
	@Autowired
	private ReportOrderDiscountDetailUntransService orderDiscountDetailUntransService;
	@Autowired
	private ReportMallOrderDetailUntransService mallPointOrderDetailUntransService;
	@Autowired
	private ReportMallRentStatusDetailUntransService mallRentStatusDetailUntransService;
	@Autowired
	private ReportMallRentAmountDetailUntransService mallRentAmountDetailUntransService;
	@Autowired
	private ReportTicketTransactionSummaryUntransService reportTicketTransactionSummaryUntransService;
	@Autowired
	private ReportSportSaleGatherUntransService sportSaleGatherUntransService;
	@Autowired
	private ReportSportCheckDetailUntransService sportCheckDetailUntransService;
	@Autowired
	private ReportSeatDiscountApportionmentUntransService seatDiscountApportionmentUntransService;
	@Autowired
	private ReportStandDiscountApportionmentUntransService standDiscountApportionmentUntransService;
	@Autowired
	private ReportMallDiscountApportionmentUntransService reportMallDiscountApportionmentUntransService;
	@Autowired
	private ReportSeatSaleGatherSeatAttrUntransService seatSaleGatherSeatAttrUntransService;
	@Autowired
	private CombPayFlowUntransService combPayFlowUntransService;
	@Autowired
	private StandApportionmentCheckUntransService standApportionmentCheckUntransService;
	@Autowired
	private SeatApportionmentCheckUntransService seatApportionmentCheckUntransService;
	@Autowired
	private ReportTicketTransactionSummaryCombPayUntransService reportTicketTransactionSummaryCombPayUntransService;
	@Autowired
	private ReportSeatSaleGatherVenueAreaUntransService reportSeatSaleGatherVenueAreaUntransService;
	@Autowired
	private ReportMemberInfoCountByDayUntransService memberInfoCountByDayUntransService;
	@Autowired
	private CombRefundFlowUntransService combRefundFlowUntransService;
	@Autowired
	private ReportSeatOrderDetailUntransService seatOrderDetailUntransService;
	@Autowired
	private ReportMallCheckRecordUntransService mallCheckRecordUntransService;
    @Autowired
	private DepositUntransService depositUntransService;
	@Autowired
	private ReportAdditionalProductSaleDetailUntransService additionalProductSaleDetailUntransService;
	@Autowired
	private ReportMembershipMulticardDetailUntransService reportMembershipMulticardDetailUntransService;
	@Autowired
	private ReportShowTicketTypeSaleGatherUntransService showTicketTypeSaleGatherUntransService;
	@Autowired
	private ReportMemberLevelCountByDayUntransService reportMemberLevelCountByDayUntransService;
	@Autowired
	private ReportCouponCheckRecordUntransService reportCouponCheckRecordUntransService;
	@Autowired
	private ReportMembershipCountByDayUntransService reportMembershipCountByDayUntransService;
	@Autowired
	private ReportMallOrderRefundOnlyUntransService mallOrderRefundOnlyUntransService;
	@Autowired
	private ReportStorageBoxDetailUntransService reportStorageBoxDetailUntransService;
	@Autowired
	private ReportProgramUntransService reportProgramUntransService;
	@Autowired
	private ReportSeatTicketChangeDetailUntransService reportSeatTicketChangeDetailUntransService;
	@Autowired
	private ReportStandTicketChangeDetailUntransService reportStandTicketChangeDetailUntransService;
	@Autowired
	private ReportHuifuDetailUntransService reportHuifuDetailUntransService;
	@Autowired
	private ReportMealDetailUntransService reportMealDetailUntransService;

	/**
	 * 定时刷新预约及入场明细表(theatre)
	 */
	public void updateReserveCheckDetailJob() {
		reserveCheckDetailUntransService.updateReserveCheckDetailJob();
	}

	/**
	 * 定时刷新预约及入场汇总表(theatre)
	 */
	public void updateReserveCheckGatherJob() {
		reserveCheckGatherUntransService.updateReserveCheckGatherJob();
	}

	/**
	 * 定时刷新站票及入场明细表(theatre)
	 */
	public void updateStandCheckDetailJob() {
		standCheckDetailUntransService.updateStandCheckDetailJob();
	}

	/**
	 * 定时刷新站票销售及入场汇总表（按项目）(theatre)
	 */
	public void updateStandSaleCheckGatherJob() {
		standSaleCheckGatherUntransService.updateStandSaleCheckGatherJob();
	}

	/**
	 * 定时刷新站票销售及入场汇总表（按天）(theatre)
	 */
	public void updateStandSaleGatherJob() {
		standSaleGatherUntransService.updateStandSaleGatherJob();
	}

	/**
	 * 定时刷新站票入场汇总表（按天）(theatre)
	 */
	public void updateStandCheckGatherJob() {
		standCheckGatherUntransService.updateStandCheckGatherJob();
	}



	/**
	 * 定时刷新座票明细表(theatre)
	 */
	public void updateSeatSaleCheckDetailJob() {
		seatSaleCheckDetailUntransService.updateSeatSaleCheckDetailJob();
	}

	public void updateDouyinSeatSaleCheckCpsDetailJob() {
		douyinSeatSaleCheckCpsDetailUntransService.updateDouyinSeatSaleCheckCpsDetailJob();
	}

	public void updateDouyinStandSaleCheckCpsDetailJob() {
		douyinSeatStandCheckCpsDetailUntransService.updateDouyinStandSaleCheckCpsDetailJob();
	}

	/**
	 * 定时刷新团体预约及入场明细表(theatre)
	 */
	public void updateGroupReserveCheckDetailJob() {
		groupReserveCheckDetailUntransService.updateGroupReserveCheckDetailJob();
	}

	/**
	 * 定时刷新团体预约销售收入统计表(theatre)
	 */
	public void updateGroupReserveCheckGatherJob() {
		groupReserveCheckGatherUntransService.updateGroupReserveCheckGatherJob();
	}

	/**
	 * 定时刷新座票销售汇总表(按天)(theatre)
	 */
	public void updateSeatSaleGatherDayJob() {
		seatSaleGatherDayUntransService.updateSeatSaleGatherDayJob();
	}

	/**
	 * 定时刷新座票销售汇总表(按项目)(theatre)
	 */
	public void updateSeatSaleGatherProgramJob() {
		seatSaleGatherProgramUntransService.updateSeatSaleGatherProgramJob();
	}

	/**
	 * 定时刷新座票入场汇总表（按天）(theatre)
	 */
	public void updateSeatCheckGatherJob() {
		seatCheckGatherUntransService.updateSeatCheckGatherJob();
	}

	/**
	 * 定时刷新入场明细表（）(anterminal)
	 */
	public void updateCheckRecordJob() {
		checkRecordUntransService.updateCheckRecordJob();
	}

	/**
	 * 定时刷新站票入场汇总表（按票）(theatre)
	 */
	public void updateStandCheckGatherTicketJob() {
		standCheckGatherTicketUntransService.updateStandCheckGatherTicketJob();
	}

	/**
	 * 定时刷新订单优惠明细表，座票、站票、商城(theatre/mall)
	 */
	public void updateOrderDiscountDetailJob() {
		orderDiscountDetailUntransService.updateOrderDiscountDetailJob();
	}

	/**
	 * 定时刷新商城订单明细(mall)
	 */
	public void updateMallOrderDetailJob() {
		mallPointOrderDetailUntransService.updateMallOrderDetailJob();
	}

	/**
	 * 定时刷新物品租赁明细报表(mall)
	 */
	public void updateMallRentStatusDetailJob() {
		mallRentStatusDetailUntransService.updateMallRentStatusDetailJob();
	}

	/**
	 * 定时刷新租赁商品金额明细报表(mall)
	 */
	public void updateMallRentAmountDetailJob() {
		mallRentAmountDetailUntransService.updateMallRentAmountDetailJob();
	}


	/**
	 * 定时刷新票款交易汇总表（）
	 */
	public void updateTicketTransactionSummaryJob() {
		reportTicketTransactionSummaryUntransService.updateReportJob(null);
	}

	/**
	 * 体育订场订单汇总表（theatre）
	 */
	public void updateSportSaleGatherJob() {
		sportSaleGatherUntransService.updateSportSaleGatherJob();
	}

	/**
	 * 体育订场订单汇总表明细表(theatre)
	 */
	public void updateSportCheckDetailJob() {
		sportCheckDetailUntransService.updateSportCheckDetailJob();
	}

	/**
	 * 定时刷新座票优惠分摊表(theatre)
	 */
	public void updateSeatDiscountApportionmentJob() {
		seatDiscountApportionmentUntransService.updateSeatDiscountApportionmentJob();
	}

	/**
	 * 定时刷新站票优惠分摊表(theatre)
	 */
	public void updateStandDiscountApportionmentJob() {
		standDiscountApportionmentUntransService.updateStandDiscountApportionmentJob();
	}

	/**
	 * 定时刷新商城优惠分摊表(mall)
	 */
	public void updateMallDiscountApportionmentJob() {
		reportMallDiscountApportionmentUntransService.updateMallDiscountApportionmentJob();
	}

	/**
	 * 定时刷新坐票票房汇总(按座位属性)(theatre)
	 */
	public void updateSeatSaleGatherSeatAttrJob() {
		seatSaleGatherSeatAttrUntransService.updateSeatSaleGatherSeatAttrJob();
	}

	/**
	 * 定时刷新CombPayFlow表(theatre)
	 */
	public void updateCombPayFlowJob() {
		combPayFlowUntransService.updateCombPayFlowJob();
	}

	/**
	 * 定时刷新CombRefundFlow表(theatre)
	 */
	public void updateCombRefundFlowJob() {
		combRefundFlowUntransService.updateCombRefundFlowJob();
	}

	/**
	 * 定时更新DepositBillReport
	 */
	public void gatherDepositBillJob() {
		depositUntransService.gatherDepositBillJob();
	}

	/**
	 * 定时检查站票优惠金额分摊(theatre)
	 */
	public void updateStandApportionmentCheckJob() {
		standApportionmentCheckUntransService.updateStandApportionmentCheckJob();
	}

	/**
	 * 定时检查座票优惠金额分摊(theatre)
	 */
	public void updateSeatApportionmentCheckJob() {
		seatApportionmentCheckUntransService.updateSeatApportionmentCheckJob();
	}

	/**
	 * 定时刷新票款交易聚合表（）
	 */
	public void updateTicketTransactionSummaryCombPayJob() {
		reportTicketTransactionSummaryCombPayUntransService.updateTicketTransactionSummaryCombPayJob();
	}

	/**
	 * 选座订单明细报表（区域票种细分）
	 */
	public void updateSeatSaleGatherVenueAreaJob() {
		reportSeatSaleGatherVenueAreaUntransService.updateSeatSaleGatherVenueAreaJob();
	}

	/**
	 * 会员各等级人数统计（按天）(thvendor)
	 */
	public void updateMemberInfoCountByDayJob() {
		memberInfoCountByDayUntransService.updateMemberInfoCountByDayJob();
	}

	/**
	 * 会员各等级人数积分统计（按天）(thvendor)
	 */
	public void updateMemberLevelCountByDayJob() {
		reportMemberLevelCountByDayUntransService.updateMemberLevelCountByDayJob();
	}

	/**
	 * 会员卡人数统计（按天）(leaguer)
	 */
	public void updateMembershipCountByDayJob() {
		reportMembershipCountByDayUntransService.updateMembershipCountByDayJob();
	}

	/**
	 * 座票订单明细表(theatre)
	 */
	public void updateSeatOrderDetailJob() {
		seatOrderDetailUntransService.updateSeatOrderDetailJob();
	}

	/**
	 * 定时获取座位划价的场次，用于跑其他报表(theatre)
	 */
	public void updateScheduleChangeJob() {
		//scheduleChangeUntransService.updateScheduleChangeJob();
	}
	/**
	 * 定时刷新商品核销报表(anterminal、mall)
	 */
	public void updateMallCheckRecordJob() {
		mallCheckRecordUntransService.updateMallCheckRecordJob();
	}

	/**
	 * 定时刷新加购商品（销售明细）报表(mall、theatre)
	 */
	public void updateAdditionalProductSaleDetailJob() {
		additionalProductSaleDetailUntransService.updateAdditionalProductSaleDetailJob();
	}

	/**
	 * 定时刷新会员卡兑换卡明细表(theatre/leaguer)
	 */
	public void updateMembershipMulticardDetailJob() {
		reportMembershipMulticardDetailUntransService.updateMembershipMulticardDetailJob();
	}

	/**
	 * 定时刷新-门票整体运营表(theatre)
	 */
	public void updateShowTicketTypeSaleGatherJob() {
		showTicketTypeSaleGatherUntransService.updateShowTicketTypeSaleGatherJob();
	}

	/**
	 * 定时刷新-优惠券核销明细(anterminal)
	 */
	public void updateCouponCheckRecordJob() {
		reportCouponCheckRecordUntransService.updateCouponCheckRecordJob();
	}

	/**
	 * 定时刷新商城仅退款单报表(mall)
	 */
	public void updateMallOrderRefundOnlyJob() {
		mallOrderRefundOnlyUntransService.updateMallOrderRefundOnlyJob();
	}
	/**
	 * 定时刷新寄存柜订单明细表(thvendor)
	 */
	public void updateStorageBoxDetailJob() {
		reportStorageBoxDetailUntransService.updateStorageBoxDetailJob();
	}

	/**
	 * 定时刷新-项目报表(theatre)
	 */
	public void updateReportProgramJob() {
		reportProgramUntransService.updateReportProgramJob();
	}

	/**
	 * 定时刷新座票改签明细表(theatre)
	 */
	public void updateSeatTicketChangeDetailJob() {
		reportSeatTicketChangeDetailUntransService.updateJob();
	}

	/**
	 * 定时刷新站票改签明细表(theatre)
	 */
	public void updateStandTicketChangeDetailJob() {
		reportStandTicketChangeDetailUntransService.updateJob();
	}

	/**
	 * 定时刷新惠付码明细表(thvendor)
	 */
	public void updateHuifuDetailJob() {
		reportHuifuDetailUntransService.updateHuifuDetailJob();
	}

	/**
	 * 定时刷新到店套餐明细表(thvendor)
	 */
	public void updateMealDetailJob() {
		reportMealDetailUntransService.updateMealDetailJob();
	}
}

package com.llwh.dcenter.utils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;

import com.llwh.dcenter.vo.report.ReportTitleVo;
import com.llwh.dcenter.vo.report.ReportWriteDataVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import static org.apache.poi.ss.usermodel.CellType.NUMERIC;

public class XLSImporterUtil {
	private static final transient Logger dbLogger = LoggerUtils.getLogger(XLSImporterUtil.class);
	private static Map<String, CellStyle> cellStyleMap = new HashMap<>();

	public static void write(InputStream inputStream, OutputStream outputStream, List<ReportTitleVo> titles,
	                         List<List<Object>> datas, int writeStartRow) {
		ReportWriteDataVo vo = new ReportWriteDataVo(writeStartRow, 0, datas);
		write(inputStream, outputStream, titles, Collections.singletonList(vo));
	}

	/**
	 * excel 写入数据
	 *
	 * @param inputStream   模板文件流
	 * @param outputStream
	 * @param titles        标题
	 * @param datas         数据集合
	 * @param datas         写入数据
	 */
	public static void write(InputStream inputStream, OutputStream outputStream, List<ReportTitleVo> titles,
	                         List<ReportWriteDataVo> datas) {
		if (inputStream == null || outputStream == null || CollectionUtils.isEmpty(datas)) {
			dbLogger.warn("excel 写入数据不全");
			return;
		}

//		Workbook workbook = importCompatibleFile(inputStream);
//		Sheet sheet = workbook.getSheetAt(0);

		SXSSFWorkbook workbook = getSXSSFWorkbook(inputStream);
		if (workbook == null) {
			dbLogger.warn("excel 获取表格失败");
			return;
		}
		// 样式初始化
		cellStyleMap = getCellStyle(workbook);
		// SXSSFWorkbook 虽然有 getSheetAt 方法，但是获取不到 Row 对象，所以如果需要修改模板，还是需要用到 XSSFWorkbook，设置样式也一样
		XSSFWorkbook xssfWorkbook = workbook.getXSSFWorkbook();
		Sheet sheet = xssfWorkbook.getSheetAt(0);
		sheet.setDisplayGridlines(true);
		// 填充标题
		if (CollectionUtils.isNotEmpty(titles)) {
			for (ReportTitleVo titleVo : titles) {
				// 合并单元格并居中
				Integer mergeFirstRow = titleVo.getMergeFirstRow();
				Integer mergeLastRow = titleVo.getMergeLastRow();
				Integer mergeFirstCol = titleVo.getMergeFirstCol();
				Integer mergeLastCol = titleVo.getMergeLastCol();
				Object title = titleVo.getTitle();
				if (mergeFirstRow != null && mergeLastRow != null && mergeFirstCol != null && mergeLastCol != null) {
					mergeAndCenter(xssfWorkbook, sheet, mergeFirstRow, mergeLastRow, mergeFirstCol, mergeLastCol, title);
				} else {
					Integer rowNo = titleVo.getRowNo();
					Integer cellNo = titleVo.getCellNo();
					if (rowNo != null && cellNo != null) {
						writeCell(sheet, rowNo, cellNo, title);
					}
				}
			}
		}
		// 如果写入的行小于以前的行，则可能以前的行已写入磁盘，此时再写则会报类似如下异常
		// 同样的，如果模版存在空行，再在该行写入也会报如下错误
		// Attempting to write a row[4] in the range [0,21] that is already written to disk
		int lastRowNum = sheet.getLastRowNum();
		ReportWriteDataVo min = Collections.min(datas, Comparator.comparing(ReportWriteDataVo::getWriteStartRow));
		if (min.getWriteStartRow() < lastRowNum) {
			sheet = xssfWorkbook.getSheetAt(0);
		} else {
			sheet = workbook.getSheetAt(0);
		}
		for (ReportWriteDataVo vo : datas) {
			Integer writeStartRow = vo.getWriteStartRow();
			Integer writeStartCell = vo.getWriteStartCell();
			List<List<Object>> dataList = vo.getDatas();
			for (List<Object> strings : dataList) {
				Row row = sheet.getRow(writeStartRow);
				if (row == null) {
					row = sheet.createRow(writeStartRow);
				}
				writeStartRow++;
				for (int i = 0; i < strings.size(); i++) {
					Object value = strings.get(i);
					if (value == null) {
						continue;
					}
					writeRow(row, writeStartCell + i, value);
				}
			}
		}
		try {
			// 将内容写入excel 文件中
			workbook.write(outputStream);
			// 调用flush方法强制刷新写入文件
			outputStream.flush();
		} catch (IOException e) {
			e.printStackTrace();
			dbLogger.warn("Excel 写入异常");
		} finally {
			try {
				inputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			try {
				outputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 单元格写数据
	 *
	 * @param sheet
	 * @param rowNo
	 * @param cellNo
	 * @param value
	 */
	public static void writeCell(Sheet sheet, int rowNo, int cellNo, Object value) {
		Row row = sheet.getRow(rowNo);
		if (row == null) {
			row = sheet.createRow(rowNo);
		}
		writeRow(row, cellNo, value);
	}

	/**
	 * 单元格写数据
	 *
	 * @param row
	 * @param cellNo
	 * @param value
	 */
	public static void writeRow(Row row, int cellNo, Object value) {
		Cell cell = row.getCell(cellNo, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
		// 当单元格对象是null 的时候，则创建单元格。如果单元格为空，无法直接调用单元格对象的setCellValue 方法设定单元格的值
		if (cell == null) {
			cell = row.createCell(cellNo);
		}
		if (value instanceof BigDecimal || value instanceof Double) {
			// 千分位 + 两位小数
			cell.setCellStyle(cellStyleMap.get("cellStyle4"));
			cell.setCellValue(Double.parseDouble(String.valueOf(value)));
			return;
		}
		if (value instanceof Long || value instanceof Integer) {
			// 千分位 + 无小数
			cell.setCellStyle(cellStyleMap.get("cellStyle3"));
			cell.setCellValue(Double.parseDouble(String.valueOf(value)));
			return;
		}
		String valueStr;
		if (value instanceof LocalDateTime) {
			valueStr = DateUtil.formatTimestamp(Timestamp.valueOf((LocalDateTime) value));
		} else if (value instanceof Timestamp) {
			valueStr = DateUtil.formatTimestamp((Timestamp) value);
		} else if (value instanceof Date) {
			valueStr = DateUtil.formatDate((Date) value);
		} else {
			valueStr = String.valueOf(value);
		}
		if (StringUtils.equals(cell.getStringCellValue(), "bitch")){
			cell.setCellStyle(cellStyleMap.get("bitch"));
			valueStr = String.valueOf(value);
			cell.setCellValue(valueStr);
			return;
		}
		if (StringUtils.equals(cell.getStringCellValue(), "bitchTitle")){
			cell.setCellStyle(cellStyleMap.get("bitchTitle"));
			valueStr = String.valueOf(value);
			cell.setCellValue(valueStr);
			return;
		}
		cell.setCellStyle(cellStyleMap.get("defaultCellStyle"));
		cell.setCellValue(valueStr);
	}

	/**
	 * 合并单元格
	 *
	 * @param sheet
	 * @param firstRow
	 * @param lastRow
	 * @param firstCol
	 * @param lastCol
	 * @param value
	 * @return
	 */
	public static Cell merge(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol, Object value) {
		sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
		Row row = sheet.getRow(firstRow);
		if (row == null) {
			row = sheet.createRow(firstRow);
		}
		return CellUtil.createCell(row, firstCol, String.valueOf(value));
	}

	/**
	 * 合并单元格并居中
	 *
	 * @param workbook
	 * @param sheet
	 * @param firstRow
	 * @param lastRow
	 * @param firstCol
	 * @param lastCol
	 * @param value
	 */
	public static void mergeAndCenter(Workbook workbook, Sheet sheet, int firstRow, int lastRow, int firstCol,
	                                  int lastCol, Object value) {
		Cell cell = merge(sheet, firstRow, lastRow, firstCol, lastCol, value);
		center(workbook, cell);
	}

	/**
	 * 单元格居中
	 *
	 * @param workbook
	 * @param cell
	 */
	public static void center(Workbook workbook, Cell cell) {
		CellUtil.setAlignment(cell, HorizontalAlignment.CENTER);
	}

	/**
	 * 兼容方法读取 Excel
	 *
	 * @param inputStream
	 * @return
	 */
	public static Workbook importCompatibleFile(InputStream inputStream) {
		try {
			return WorkbookFactory.create(inputStream);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 读取 2007 版 excel 模板并用 SXSSFWorkbook 降低内存消耗，默认超过 100 行数据会把文件写入磁盘 /tmp/poifiles 从而降低内存占用
	 * @param inputStream
	 * @return
	 */
	public static SXSSFWorkbook getSXSSFWorkbook(InputStream inputStream) {
		try {
			XSSFWorkbook xssfWorkbook = new XSSFWorkbook(inputStream);
			return new SXSSFWorkbook(xssfWorkbook, 1000);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static boolean isNull(Cell cell) {
		return cell == null;
	}

	public static boolean isNumberOrNull(Cell cell) {
		return isNull(cell) || cell.getCellType() != NUMERIC || Double.valueOf(cell.getNumericCellValue()) == null;
	}

	public static boolean isBlank(Cell cell) {
		return isNull(cell) || cell.getCellType() != NUMERIC || StringUtils.isBlank(cell.getRichStringCellValue().getString());
	}

	public static Double getDouble(Cell cell) {
		if (isNumberOrNull(cell)) {
			return null;
		}
		return Double.valueOf(cell.getNumericCellValue());
	}

	public static Long getLong(Cell cell) {
		if (isNumberOrNull(cell)) {
			return null;
		}
		return Double.valueOf(cell.getNumericCellValue()).longValue();
	}

	public static Integer getInteger(Cell cell) {
		if (isNumberOrNull(cell)) {
			return null;
		}
		return Double.valueOf(cell.getNumericCellValue()).intValue();
	}

	public static String getString(Cell cell) {
		if (isBlank(cell)) {
			return null;
		}
		return StringUtils.trim(cell.getRichStringCellValue().getString());
	}

	/**
	 * 读取数据
	 *
	 * @param inputStream
	 * @param sheetNo
	 * @param startRowNo
	 * @param cellNo
	 * @return
	 */
	public static List<String> read(InputStream inputStream, int sheetNo, int startRowNo, int cellNo) {
		List<List<String>> read = read(inputStream, sheetNo, startRowNo, Collections.singletonList(cellNo));
		if (read == null) {
			return null;
		}
		List<String> results = new ArrayList<>();
		for (List<String> strings : read) {
			results.add(strings.get(0));
		}
		return results;
	}


	/**
	 * 读取数据
	 *
	 * @param inputStream
	 * @param sheetNo
	 * @param startRowNo
	 * @param cellNos
	 * @param filterCell 过滤数据的列
	 * @param filterValue 过滤数据的值
	 * @return
	 */
	public static List<List<String>> read(InputStream inputStream, int sheetNo, int startRowNo, List<Integer> cellNos,
	                                      Integer filterCell, String filterValue) {
		if (inputStream == null) {
			dbLogger.warn("文件不存在");
			return null;
		}
		Workbook workbook = importCompatibleFile(inputStream);
		if (workbook == null) {
			dbLogger.warn("excel 获取表格失败");
			return null;
		}
		Sheet sheet = workbook.getSheetAt(sheetNo);
		if (sheet == null) {
			dbLogger.warn("sheet 不存在");
			return null;
		}
		int lastRowNum = sheet.getLastRowNum();
		if (lastRowNum == 0) {
			dbLogger.warn("数据不存在");
			return null;
		}
		List<List<String>> datas = new ArrayList<>();
		for (int i = startRowNo; i <= lastRowNum; i++) {
			Row row = sheet.getRow(i);
			if (row == null) {
				continue;
			}
			List<String> cellDatas = new ArrayList<>();
			for (Integer cellNo : cellNos) {
				Cell cell = row.getCell(cellNo);
				if (cell == null) {
					continue;
				}
				String cellValue = getCellValue(cell);
				if (filterCell != null && StringUtils.isNotBlank(filterValue)) {
					if (filterCell.equals(cellNo) && !StringUtils.equals(cellValue, filterValue)) {
						cellDatas = null;
						break;
					}
				}
				cellDatas.add(cellValue);
			}
			if (CollectionUtils.isNotEmpty(cellDatas)) {
				datas.add(cellDatas);
			}
		}
		return datas;
	}

	public static List<List<String>> read(Workbook workbook, int sheetNo, int startRowNo, List<Integer> cellNos,
	                                      Integer filterCell, String filterValue) {
		if (workbook == null) {
			dbLogger.warn("excel 获取表格失败");
			return null;
		}
		Sheet sheet = workbook.getSheetAt(sheetNo);
		if (sheet == null) {
			dbLogger.warn("sheet 不存在");
			return null;
		}
		int lastRowNum = sheet.getLastRowNum();
		if (lastRowNum == 0) {
			dbLogger.warn("数据不存在");
			return null;
		}
		List<List<String>> datas = new ArrayList<>();
		for (int i = startRowNo; i <= lastRowNum; i++) {
			Row row = sheet.getRow(i);
			if (row == null) {
				continue;
			}
			List<String> cellDatas = new ArrayList<>();
			for (Integer cellNo : cellNos) {
				Cell cell = row.getCell(cellNo);
				if (cell == null) {
					continue;
				}
				String cellValue = getCellValue(cell);
				if (filterCell != null && StringUtils.isNotBlank(filterValue)) {
					if (filterCell.equals(cellNo) && !StringUtils.equals(cellValue, filterValue)) {
						cellDatas = null;
						break;
					}
				}
				cellDatas.add(cellValue);
			}
			if (CollectionUtils.isNotEmpty(cellDatas)) {
				datas.add(cellDatas);
			}
		}
		return datas;
	}
	public static List<List<String>> read(InputStream inputStream, int sheetNo, int startRowNo, List<Integer> cellNos) {
		return read(inputStream, sheetNo, startRowNo, cellNos, null, null);
	}

	/**
	 * 获取 sheet 某一行的列数
	 * @param inputStream
	 * @param sheetNo
	 * @param startRowNo
	 * @return
	 */
	public static ResultCode<Map> getCellNos(InputStream inputStream, int sheetNo, int startRowNo) {
		if (inputStream == null) {
			dbLogger.warn("文件不存在");
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("文件不存在");
		}
		Workbook workbook = importCompatibleFile(inputStream);
		if (workbook == null) {
			dbLogger.warn("excel 获取表格失败");
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("excel 获取表格失败");
		}
		Sheet sheet = workbook.getSheetAt(sheetNo);
		if (sheet == null) {
			dbLogger.warn("sheet 不存在");
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("sheet 不存在");
		}
		int lastRowNum = sheet.getLastRowNum();
		if (lastRowNum == 0) {
			dbLogger.warn("数据不存在");
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("数据不存在");
		}
		Row row = sheet.getRow(startRowNo);
		if (row == null) {
			dbLogger.warn("数据不存在");
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("数据不存在");
		}
		HashMap result = new HashMap();
		result.put("cellNo", (int) row.getLastCellNum());
		result.put("workBook", workbook);
		return ResultCode.getSuccessReturn(result);
	}

	/**
	 * 未处理公式
	 */
	public static String getCellValue(Cell cell) {
		if (cell == null) {
			return null;
		}
		switch (cell.getCellType()) {
			case STRING:
				return cell.getRichStringCellValue().getString().trim();
			case NUMERIC:
				if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");//非线程安全
					return sdf.format(cell.getDateCellValue());
				} else {
					return String.valueOf(cell.getNumericCellValue());
				}
			case BOOLEAN:
				return String.valueOf(cell.getBooleanCellValue());
			case FORMULA:
				return cell.getCellFormula();
			default:
				return null;
		}
	}
	public static Map<String, CellStyle> getCellStyle(SXSSFWorkbook workbook) {
		HashMap<String, CellStyle> map = new HashMap<>();
		map.put("cellStyle3", createCellStyle(workbook, 3)); // 千分位 + 无小数
		map.put("cellStyle4", createCellStyle(workbook, 4)); // 千分位 + 两位小数

		CellStyle bitch = workbook.createCellStyle();
		bitch.setAlignment(HorizontalAlignment.LEFT);
		bitch.setVerticalAlignment(VerticalAlignment.CENTER);
		map.put("bitch", bitch); // 特殊格式

		CellStyle bitchTitle = workbook.createCellStyle();
		Font font = workbook.createFont();
		font.setBold(true);
		font.setFontHeight((short) 320);
		font.setBold(true);
		bitchTitle.setFont(font);
		bitchTitle.setAlignment(HorizontalAlignment.CENTER);
		bitchTitle.setVerticalAlignment(VerticalAlignment.CENTER);
		map.put("bitchTitle", bitchTitle); // 特殊格式

		CellStyle defaultCellStyle = createCellStyle(workbook, 0);
		defaultCellStyle.setAlignment(HorizontalAlignment.CENTER);
		map.put("defaultCellStyle", defaultCellStyle);

		return map;
	}
	/**
	 * 表格样式，49种内置数据格式
	 * 注意：针对不同的样式，创建不同的对象，
	 * 否则样式会被覆盖，整个excel的样式跟最后一次样式一致
	 * @param wb
	 * @return
	 */
	public static CellStyle createCellStyle(SXSSFWorkbook wb, int index) {
		CellStyle cellStyle = wb.createCellStyle();
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setTopBorderColor(IndexedColors.GREY_25_PERCENT.index);
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBottomBorderColor(IndexedColors.GREY_25_PERCENT.index);
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setLeftBorderColor(IndexedColors.GREY_25_PERCENT.index);
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setRightBorderColor(IndexedColors.GREY_25_PERCENT.index);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		cellStyle.setDataFormat((short)index);
		return cellStyle;
	}
}

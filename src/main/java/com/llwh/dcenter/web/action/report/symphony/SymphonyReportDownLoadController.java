package com.llwh.dcenter.web.action.report.symphony;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.fancylab.support.AuthUser;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;

import com.llwh.dcenter.helper.ReportMemberinfoStatisticsSearch;
import com.llwh.dcenter.untrans.report.ReportMemberinfoStatisticsUntransService;
import com.llwh.dcenter.utils.XLSImporterUtil;
import com.llwh.dcenter.vo.report.ReportTitleVo;
import com.llwh.dcenter.web.action.TbsBaseController;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
public class SymphonyReportDownLoadController extends TbsBaseController {

	@Autowired
	private ReportMemberinfoStatisticsUntransService reportMemberinfoStatisticsUntransService;

	/**
	 * 会员分析统计导出表
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadByActiveDate.xhtml")
	public String downloadMembershipDate(HttpServletRequest request, HttpServletResponse response, ReportMemberinfoStatisticsSearch search) {
		return download(request,response,search);
	}

	private String download(HttpServletRequest request, HttpServletResponse response, ReportMemberinfoStatisticsSearch search) {
		AuthUser user = getTbsLogonUser();

		String filename = "会员分析统计" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyy-MM-dd") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/symphony/MemberinfoStatistics.xlsx");
		List<List<Object>> totalCounts = reportMemberinfoStatisticsUntransService.getTotalCount(user,search);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "姓名");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}
}

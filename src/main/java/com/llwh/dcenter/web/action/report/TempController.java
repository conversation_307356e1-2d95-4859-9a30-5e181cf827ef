package com.llwh.dcenter.web.action.report;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.JsonUtils;
import cn.fancylab.util.ValueUtil;

import com.google.common.collect.Lists;
import com.llwh.base.constant.OrderType;
import com.llwh.dcenter.mapper.common.CommonMapper;
import com.llwh.dcenter.web.action.TbsBaseController;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
public class TempController extends TbsBaseController {
	@Autowired
	private CommonMapper commonMapper;

	@RequestMapping("/inner/temp/discountQuery.xhtml")
	public String discountQuery(Timestamp timefrom, Timestamp timeto) {
		List<Map> discountTradeNos = commonMapper.getDiscountTradeNos(timefrom, timeto);
		Map<String, List<Map>> discountMap = BeanUtil.groupBeanList(discountTradeNos, "orderType");
		int diff = 0;
		for (String orderType : discountMap.keySet()) {
			List<Map> maps = discountMap.get(orderType);
			List<String> tradeNos = BeanUtil.getBeanPropertyList(maps, "tradeNo", true);
			List<List<String>> partition = Lists.partition(tradeNos, 200);
			for (List<String> trds : partition) {
				List<Map> discounts = commonMapper.getOrderDiscounts(trds);
				if (StringUtils.equals(OrderType.TYPE_SHOW, orderType)) {
					List<Map> showDiscounts = commonMapper.getShowDiscounts(trds);
					diff += comparison(discounts, showDiscounts, OrderType.TYPE_SHOW);
				} else if (StringUtils.equals(OrderType.TYPE_TICKET, orderType)) {
					List<Map> ticketDiscounts = commonMapper.getSeatDiscounts(trds);
					diff += comparison(discounts, ticketDiscounts, OrderType.TYPE_TICKET);
				}
			}
		}
		return jsonSuccessReturn(diff);
	}

	private int comparison(List<Map> discounts, List<Map> orderDiscounts, String orderType) {
		int diff = 0;
		Map<String, List<Map>> discountMap = BeanUtil.groupBeanList(discounts, "tradeNo");
		Map<String, List<Map>> orderMap = BeanUtil.groupBeanList(orderDiscounts, "tradeNo");
		for (String tradeNo : discountMap.keySet()) {
			List<Map> discountList = discountMap.get(tradeNo);
			for (Map map : discountList) {
				Long id = MapUtils.getLong(map, "id");
				double discountAmount = MapUtils.getDoubleValue(map, "discountAmount");
				List<Map> orderDiscountList = orderMap.get(tradeNo);
				if (CollectionUtils.isEmpty(orderDiscountList)) {
					dbLogger.warn("优惠对应的订单不存在！tradeNo:{}", tradeNo);
					break;
				}
				double itemDiscount = 0D;
				Map<Long, Map> detailMap = new HashMap<>(orderDiscountList.size());
				for (Map odMap : orderDiscountList) {
					Long detailId = MapUtils.getLong(odMap, "id");
					Map discountInfoMap = detailMap.get(detailId);
					if (MapUtils.isEmpty(discountInfoMap)) {
						String discountInfo = MapUtils.getString(odMap, "discountInfo");
						discountInfoMap = JsonUtils.readJsonToMap(discountInfo);
						detailMap.put(detailId, discountInfoMap);
					}
					double doubleValue = MapUtils.getDoubleValue(discountInfoMap, id + "");
					itemDiscount = ValueUtil.round(itemDiscount + doubleValue);
				}
				if (discountAmount != itemDiscount) {
					dbLogger.warn("订单优惠金额不匹配！tradeNo:{}, discountAmount:{}, itemDiscount:{}, orderType:{}", tradeNo, discountAmount, itemDiscount, orderType);
					diff += 1;
					break;
				}
			}
		}
		return diff;
	}

}

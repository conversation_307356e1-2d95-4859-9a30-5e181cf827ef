package com.llwh.dcenter.web.action.report;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.util.DateUtil;

import com.llwh.dcenter.helper.CouponReportSearch;
import com.llwh.dcenter.helper.MallOrderGatherByDetailSear;
import com.llwh.dcenter.helper.MembershipSaleSearch;
import com.llwh.dcenter.helper.ReportAdditionalProductSaleDetailSearch;
import com.llwh.dcenter.helper.ReportOverallCount;
import com.llwh.dcenter.helper.ReportSeatStandSaleCheckDetailSearch;
import com.llwh.dcenter.helper.ReportStandCheckDetailSearch;
import com.llwh.dcenter.helper.SearReportBalanceDetail;
import com.llwh.dcenter.helper.SearReportBalanceGather;
import com.llwh.dcenter.helper.SearReportMulticardDetail;
import com.llwh.dcenter.helper.SearReportSportCheckDetailCount;
import com.llwh.dcenter.helper.SearReportSportSaleCount;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.helper.SearReportStandCheckGatherCount;
import com.llwh.dcenter.helper.SearReportStandSaleCount;
import com.llwh.dcenter.helper.SearchReserveCheckDetail;
import com.llwh.dcenter.helper.SeatSaleGatherByDetailSear;
import com.llwh.dcenter.helper.StandSaleGatherByDetailSear;
import com.llwh.dcenter.service.report.ReportMemberInfoService;
import com.llwh.dcenter.untrans.report.ReportAdditionalProductSaleDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportBalanceDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportBalanceGatherDayUntransService;
import com.llwh.dcenter.untrans.report.ReportCheckRecordUntransService;
import com.llwh.dcenter.untrans.report.ReportCouponUntransService;
import com.llwh.dcenter.untrans.report.ReportMallOrderDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMallOrderGatherByDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMemberSaleDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMemberSaleGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportMulticardDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportReserveCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportReserveCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatOverAllOperateUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatRevenueGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherByDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherDayUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherProgramUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherSeatAttrUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatStandSaleCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportShowOverAllOperateUntransService;
import com.llwh.dcenter.untrans.report.ReportSportCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportSportSaleGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportStandCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportStandCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportStandRevenueGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportStandSaleCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportStandSaleGatherByDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportStandSaleGatherUntransService;
import com.llwh.dcenter.vo.req.ReportReserveCheckGatherReq;
import com.llwh.dcenter.untrans.report.ReportStorageBoxDetailUntransService;
import com.llwh.dcenter.vo.req.ReportStorageBoxDetailSearch;
import com.llwh.dcenter.web.action.TbsBaseController;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
public class ReportTotalController extends TbsBaseController {

	@Autowired
	private ReportStandCheckDetailUntransService standCheckDetailUntransService;
	@Autowired
	private ReportStandSaleCheckGatherUntransService standSaleCheckGatherUntransService;
	@Autowired
	private ReportStandSaleGatherByDetailUntransService standSaleGatherByDetailUntransService;
	@Autowired
	private ReportStandSaleGatherUntransService standSaleGatherUntransService;
	@Autowired
	private ReportCheckRecordUntransService checkRecordUntransService;
	@Autowired
	private ReportSeatSaleCheckDetailUntransService seatSaleCheckDetailUntransService;
	@Autowired
	private ReportSeatSaleGatherDayUntransService seatSaleGatherDayUntransService;
	@Autowired
	private ReportSeatSaleGatherProgramUntransService seatSaleGatherProgramUntransService;
	@Autowired
	private ReportStandRevenueGatherUntransService standRevenueGatherUntransService;
	@Autowired
	private ReportSeatRevenueGatherUntransService seatRevenueGatherUntransService;
	@Autowired
	private ReportCouponUntransService couponService;
	@Autowired
	private ReportStandCheckGatherUntransService standCheckGatherUntransService;
	@Autowired
	private ReportMemberInfoService reportMemberInfoService;
	@Autowired
	private ReportMallOrderGatherByDetailUntransService mallOrderGatherByDetailUntransService;
	@Autowired
	private ReportSeatOverAllOperateUntransService reportSeatOverAllOperateUntransService;
	@Autowired
	private ReportMallOrderDetailUntransService mallOrderDetailUntransService;
	@Autowired
	private ReportShowOverAllOperateUntransService reportShowOverAllOperateUntransService;
	@Autowired
	private ReportSeatSaleGatherByDetailUntransService seatSaleGatherByDetailUntransService;
	@Autowired
	private ReportMemberSaleGatherUntransService memberSaleGatherUntransService;
	@Autowired
	private ReportMemberSaleDetailUntransService memberSaleDetailUntransService;
	@Autowired
	private ReportSportSaleGatherUntransService sportSaleGatherUntransService;
	@Autowired
	private ReportSportCheckDetailUntransService sportCheckDetailUntransService;
	@Autowired
	private ReportBalanceDetailUntransService balanceDetailUntransService;
	@Autowired
	private ReportBalanceGatherDayUntransService balanceGatherDayUntransService;
	@Autowired
	private ReportMulticardDetailUntransService multicardDetailUntransService;
	@Autowired
	private ReportSeatSaleGatherSeatAttrUntransService reportSeatSaleGatherSeatAttrUntransService;
	@Autowired
	private ReportSeatCheckGatherUntransService seatCheckGatherUntransService;
	@Autowired
	private ReportSeatStandSaleCheckDetailUntransService seatStandSaleCheckDetailUntransService;
	@Autowired
	private ReportAdditionalProductSaleDetailUntransService additionalProductSaleDetailUntransService;
	@Autowired
	private ReportReserveCheckDetailUntransService reserveCheckDetailUntransService;
	@Autowired
	private ReportReserveCheckGatherUntransService reserveCheckGatherUntransService;
	@Autowired
	private ReportStorageBoxDetailUntransService reportStorageBoxDetailUntransService;

	/**
	 * 查询站票销售及入场明细表Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getStandCheckDetailTotal.xhtml")
	public String getStandCheckDetailTotal(SearReportStandCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(standCheckDetailUntransService.getTotals(user, search));
	}

	/**
	 * 查询站票销售及入场汇总表（按项目）Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getStandSaleCheckGatherTotal.xhtml")
	public String getStandSaleCheckGatherTotal(SearReportStandCheckGatherCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(standSaleCheckGatherUntransService.getTotals(user, search));
	}

	/**
	 * 查询站票销售及入场汇总表（按明细）Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getStandSaleGatherTotalByDetail.xhtml")
	public String getStandSaleGatherTotalByDetail(StandSaleGatherByDetailSear search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(standSaleGatherByDetailUntransService.getTotals(user, search));
	}

	/**
	 * 查询站票销售汇总表（按天）第二批Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getStandSaleGatherTotal.xhtml")
	public String getStandSaleGatherTotal(SearReportStandSaleCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(standSaleGatherUntransService.getTotals(user, search));
	}

	/**
	 * 查询入场明细表Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/check/getCheckRecordTotal.xhtml")
	public String getCheckRecordTotal(SearReportStandCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(checkRecordUntransService.getTotals(user, search));
	}

	/**
	 * 获取座票销售及入场明细表Total
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/seat/getSeatSaleCheckTotal.xhtml")
	public String getSeatSaleCheckTotal(SearReportStandCheckDetailCount search){
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(seatSaleCheckDetailUntransService.getTotals(user, search));
	}

	/**
	 * 查询座票销售汇总表（按天）Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getSeatSaleGatherDayTotal.xhtml")
	public String getSeatSaleGatherDayTotal(SearReportStandSaleCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(seatSaleGatherDayUntransService.getTotals(user, search));
	}

	/**
	 * 查询座票销售及入场汇总表（按项目）Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getSeatSaleGatherProgramTotal.xhtml")
	public String getSeatSaleGatherProgramTotal(SearReportStandCheckGatherCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(seatSaleGatherProgramUntransService.getTotals(user, search));
	}

	/**
	 * 查询座票销售及入场汇总表（按项目）Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getStandRevenueGatherTotal.xhtml")
	public String getStandRevenueGatherTotal(SearReportStandSaleCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(standRevenueGatherUntransService.getTotals(user, search));
	}

	/**
	 * 查询座票销售及入场汇总表（按项目）Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getSeatRevenueGatherTotal.xhtml")
	public String getSeatRevenueGatherTotal(SearReportStandSaleCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(seatRevenueGatherUntransService.getTotals(user, search));
	}

	@RequestMapping("/home/<USER>/total.xhtml")
	public String couponTotal(CouponReportSearch search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(couponService.getCouponTotal(user.getCompanyId(), search));
	}

	@RequestMapping("/home/<USER>/sale/getStandCheckGatherTotal.xhtml")
	public String getStandCheckGatherTotal(SearReportStandCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(standCheckGatherUntransService.getTotals(user, search));
	}

	@RequestMapping("/home/<USER>/getTotalPointRecordById.xhtml")
	public String getTotalPointRecordById(Long memberId) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(reportMemberInfoService.getTotalPointRecordById(memberId, user.getCompanyId()));
	}

	@RequestMapping("/home/<USER>/getMemberUserTotal.xhtml")
	public String getMemberUserTotal() {
		AuthUser user = getTbsLogonUser();
		ResultCode<Map> memberTotal = reportMemberInfoService.getMemberTotal(user.getCompanyId());
		ResultCode<List<Map>> memberLevelTotal = reportMemberInfoService.getMemberLevelTotal(user.getCompanyId());
		Map<String,Object> result = new HashMap<>();
		result.put("memberUserTotal",memberTotal.getData());
		result.put("memberLevelTotal",memberLevelTotal.getData());
		return jsonSuccessReturn(result);
	}
	@RequestMapping("/home/<USER>/getMemberUserDayCount.xhtml")
	public String getMemberUserByDayRange(Timestamp startTime,Timestamp endTime) {
		AuthUser user = getTbsLogonUser();
		ResultCode<List<Map>> memberUserDayCount = reportMemberInfoService.getMemberCountByDayRange(user.getCompanyId(),startTime,endTime);
		List<Map> listData = memberUserDayCount.getData();
		Map<String,Map> dayMap =listData.stream().collect(Collectors.toMap(item -> {
			Date date = (Date)item.get("date");
			return DateUtil.format(date,"yyyy/MM/dd");
		},item -> item));
		Calendar calendarStart = Calendar.getInstance();
		calendarStart.setTime(DateUtil.getDateFromTimestamp(startTime));

		Date start = calendarStart.getTime();

		Calendar calendarEnd = Calendar.getInstance();
		calendarEnd.setTime(DateUtil.getDateFromTimestamp(endTime));
		calendarEnd.add(Calendar.DATE,1);
		Date end = calendarEnd.getTime();

		Map result = new LinkedHashMap();
		while (start.before(end)){
			String date = DateUtil.format(start,"yyyy/MM/dd");
			Map countMap = dayMap.get(date);
			Integer count = countMap == null ? 0 : MapUtils.getInteger(countMap,"userCount");
			result.put(date,count);
			calendarStart.add(Calendar.DATE,1);
			start = calendarStart.getTime();
		}
		return jsonSuccessReturn(result);
	}


	/**
	 * 查询积分商城订单汇总表（按明细）Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getMallPointOrderGatherTotalByDetail.xhtml")
	public String getMallPointOrderGatherTotalByDetail(MallOrderGatherByDetailSear search) {
		AuthUser user = getTbsLogonUser();
		search.setOrderType("mallpoint");
		return jsonResultCode(mallOrderGatherByDetailUntransService.getTotals(user, search));
	}

	/**
	 * 查询积分商城订单汇总表（按明细）Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getMallCulturalOrderGatherTotalByDetail.xhtml")
	public String getMallCulturalOrderGatherTotalByDetail(MallOrderGatherByDetailSear search) {
		AuthUser user = getTbsLogonUser();
		search.setOrderType("mallcultural");
		return jsonResultCode(mallOrderGatherByDetailUntransService.getTotals(user, search));
	}

	/**
	 * 查询积分商城订单汇总表（按明细）Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getMallRetailOrderGatherTotalByDetail.xhtml")
	public String getMallRetailOrderGatherTotalByDetail(MallOrderGatherByDetailSear search) {
		AuthUser user = getTbsLogonUser();
		search.setOrderType("mallretail");
		return jsonResultCode(mallOrderGatherByDetailUntransService.getTotals(user, search));
	}
	/**
	 * 查询零售商城订单汇总Total
	 *
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getMallRetailUserTotalCount.xhtml")
	public String getMallRetailUserTotalCount() {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(mallOrderDetailUntransService.getUserTotalCount(user, "mallretail"));
	}

	/**
	 * 坐票整体运营报表
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/ticket/getOverAllOperate.xhtml")
	public String getOverAllOperate(ReportOverallCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(reportSeatOverAllOperateUntransService.getCounts(user, search));
	}
	/**
	 * 站票整体运营报表
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/show/getOverAllOperate.xhtml")
	public String getTikcetOverAllOperate(ReportOverallCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(reportShowOverAllOperateUntransService.getCounts(user, search));
	}

	/**
	 * 获取座票销售票版
	 * @return
	 */
	@RequestMapping("/home/<USER>/seat/getTodayTotalsCount.xhtml")
	public String getTodayTotalsCount(String strDate){
		AuthUser user = getTbsLogonUser();
		Date date = null;
		if (StringUtils.isNotBlank(strDate)){
			date = DateUtil.parseDate(strDate);
		}
		return jsonResultCode(seatSaleCheckDetailUntransService.getTodayTotalsCount(user.getCompanyId(),date));
	}

	/**
	 * 查询座票销售及入场汇总表（按明细）Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getSeatSaleGatherTotalByDetail.xhtml")
	public String getSeatSaleGatherTotalByDetail(SeatSaleGatherByDetailSear search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(seatSaleGatherByDetailUntransService.getTotals(user, search));
	}

	/**
	 * 查询会员卡销售汇总表Total
	 *
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getMemberSaleGatherTotal.xhtml")
	public String getMemberSaleGatherTotal(Timestamp datefrom, Timestamp dateto, String memberCardTypeId, Long userGroupId, String paymethod) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(memberSaleGatherUntransService.getTotals(user, datefrom, dateto, memberCardTypeId, userGroupId, paymethod));
	}

	/**
	 * 查询会员卡销售明细表Total
	 *
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getMemberSaleDetailTotal.xhtml")
	public String getMemberSaleGatherTotal(MembershipSaleSearch search) {
		AuthUser user = getTbsLogonUser();
		search.setCompanyId(user.getCompanyId());
		return jsonResultCode(memberSaleDetailUntransService.getTotals(search));
	}

	/**
	 * 体育订场订单汇总表Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getSportRevenueGatherTotal.xhtml")
	public String getSportRevenueGatherTotal(SearReportSportSaleCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(sportSaleGatherUntransService.getTotals(user, search));
	}

	/**
	 * 体育订场订单明细表Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getSportCheckDetailCountTotal.xhtml")
	public String getSportCheckDetailCountTotal(SearReportSportCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(sportCheckDetailUntransService.getTotals(user, search));
	}

	/**
	 * 储值卡收支明细表Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/balance/getBalanceDetailTotal.xhtml")
	public String getBalanceDetailTotal(SearReportBalanceDetail search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(balanceDetailUntransService.getTotals(user, search));
	}

	/**
	 * 储值卡按天汇总Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/balance/getBalanceGatherDayTotal.xhtml")
	public String getBalanceGatherDayTotal(SearReportBalanceGather search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(balanceGatherDayUntransService.getTotals(user, search));
	}

	/**
	 * 次兑卡使用明细Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/multicard/getMulticardDetailTotal.xhtml")
	public String getMulticardDetailTotal(SearReportMulticardDetail search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(multicardDetailUntransService.getTotals(user, search));
	}
	/**
	 * 坐票整体运营表Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getSeatSaleGatherSeatAttrTotal.xhtml")
	public String getSeatSaleGatherSeatAttrTotal(ReportOverallCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(reportSeatSaleGatherSeatAttrUntransService.getTotals(user, search));
	}

	/**
	 * 选座核销日报表Total
	 *
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/getSeatCheckGatherTotal.xhtml")
	public String getSeatCheckGatherTotal(SearReportStandCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(seatCheckGatherUntransService.getTotals(user, search));
	}

	/**
	 * 获取选座、站票明细表Total
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/getSeatStandSaleCheckDetailTotal.xhtml")
	public String getSeatStandSaleCheckDetailTotal(ReportSeatStandSaleCheckDetailSearch search){
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(seatStandSaleCheckDetailUntransService.getTotals(user, search));
	}

	/**
	 * 加购商品（销售明细）报表Total
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/getAdditionalProductSaleDetailTotal.xhtml")
	public String getAdditionalProductSaleDetailTotal(ReportAdditionalProductSaleDetailSearch search){
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(additionalProductSaleDetailUntransService.getTotals(user, search));
	}

	/**
	 * 门票销售明细表Total
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/stand/getReportStandCheckDetailTotal.xhtml")
	public String getReportStandCheckDetailTotal(ReportStandCheckDetailSearch search){
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(standCheckDetailUntransService.getTotalsBySql(user, search));
	}

	/**
	 * 查询入场明细表Total v3.0
	 * 入场明细表只包含站票和通票
	 *
	 * @param search 查询条件
	 * @return 统计数据
	 */
	@RequestMapping("/home/<USER>/check/getShowCheckRecordTotal.xhtml")
	public String getShowCheckRecordTotal(SearReportStandCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(checkRecordUntransService.getCheckRecordTotals(user, search));
	}

	/**
	 * 查询入场明细表Total v3.0
	 * 入场明细表只包含兑换和通票
	 *
	 * @param search 查询条件
	 * @return 统计数据
	 */
	@RequestMapping("/home/<USER>/check/getExchangeCheckRecordTotal.xhtml")
	public String getExchangeCheckRecordTotal(SearReportStandCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(checkRecordUntransService.getCheckRecordTotals(user, search));
	}

	/**
	 * 门票入场明细表汇总表（时间维度）
	 * @param search 查询条件
	 * @return 统计数据
	 */
	@RequestMapping("/home/<USER>/check/showCheckTotalByDayDimension.xhtml")
	public String showCheckTotalByDayDimension(SearReportStandCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(checkRecordUntransService.showCheckTotalByDayDimension(user,search));
	}

	/**
	 * 门票入场明细表汇总表（场馆维度）
	 * @param search 查询条件
	 * @return 统计数据
	 */
	@RequestMapping("/home/<USER>/check/showCheckTotalByStadiumDimension.xhtml")
	public String showCheckTotalByStadiumDimension(SearReportStandCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(checkRecordUntransService.showCheckTotalByStadiumDimension(user,search));
	}

	/**
	 * 门票销售明细表 (场馆维度)
	 * @param search 查询条件
	 * @return result
	 */
	@RequestMapping("/home/<USER>/check/showSaleTotalByStadiumDimension.xhtml")
	public String showSaleTotalByStadiumDimension(ReportStandCheckDetailSearch search) {
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(standCheckDetailUntransService.showSaleTotalByStadiumDimension(user, search));
	}

	/**
	 * 查询预约及入场明细表（新）Total
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/reserve/getReserveCheckDetailTotal4New.xhtml")
	public String getReserveCheckDetailTotal4New(SearchReserveCheckDetail search){
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(reserveCheckDetailUntransService.getTotals4New(user, search));
	}

	/**
	 * 查询预约及入场汇总表（新）Total
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/reserve/getReserveCheckGatherTotal4New.xhtml")
	public String getReserveCheckGatherTotal4New(ReportReserveCheckGatherReq search){
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(reserveCheckGatherUntransService.getTotals4New(user, search));
	}

	/**
	 * 获取寄存柜订单明细表Total
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/storageBox/getStorageBoxTotal.xhtml")
	public String getStorageBoxTotal(ReportStorageBoxDetailSearch search){
		AuthUser user = getTbsLogonUser();
		return jsonResultCode(reportStorageBoxDetailUntransService.getTotals(user, search));
	}
}

package com.llwh.dcenter.web.action.report;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.fancylab.support.AuthUser;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.LoggerUtils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearReportMemberInfo;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.helper.SearReportStandCheckGatherCount;
import com.llwh.dcenter.helper.SearReportStandSaleCount;
import com.llwh.dcenter.helper.SearchGroupReserveCheckDetail;
import com.llwh.dcenter.helper.SearchReserveCheckDetail;
import com.llwh.dcenter.untrans.report.ReportCheckRecordUntransService;
import com.llwh.dcenter.untrans.report.ReportGroupReserveCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportGroupReserveCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportMemberInfoUntransService;
import com.llwh.dcenter.untrans.report.ReportMemberSaleDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMemberSaleGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportMembershipCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMembershipCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportMembershipVerifyDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportMembershipVerifyGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportReserveCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportReserveCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatRevenueGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherDayUntransService;
import com.llwh.dcenter.untrans.report.ReportSeatSaleGatherProgramUntransService;
import com.llwh.dcenter.untrans.report.ReportStandCheckDetailUntransService;
import com.llwh.dcenter.untrans.report.ReportStandCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportStandRevenueGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportStandSaleCheckGatherUntransService;
import com.llwh.dcenter.untrans.report.ReportStandSaleGatherUntransService;
import com.llwh.dcenter.utils.XLSImporterUtil;
import com.llwh.dcenter.vo.report.ReportTitleVo;
import com.llwh.dcenter.vo.req.ReportReserveCheckGatherReq;
import com.llwh.dcenter.web.action.TbsBaseController;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
public class ReportDownLoadController extends TbsBaseController {

	@Autowired
	private ReportStandCheckDetailUntransService standCheckDetailUntransService;
	@Autowired
	private ReportReserveCheckDetailUntransService reserveCheckDetailUntransService;
	@Autowired
	private ReportReserveCheckGatherUntransService reserveCheckGatherUntransService;
	@Autowired
	private ReportMemberSaleDetailUntransService  memberSaleDetailUntransService;
	@Autowired
	private ReportStandSaleCheckGatherUntransService saleCheckGatherUntransService;
	@Autowired
	private ReportMemberSaleGatherUntransService memberSaleGatherUntransService;
	@Autowired
	private ReportMembershipCheckDetailUntransService membershipCheckDetailUntransService;
	@Autowired
	private ReportStandSaleGatherUntransService standSaleGatherUntransService;
	@Autowired
	private ReportMembershipCheckGatherUntransService membershipCheckGatherUntransService;
	@Autowired
	private ReportCheckRecordUntransService checkRecordUntransService;
	@Autowired
	private ReportStandCheckGatherUntransService standCheckGatherUntransService;
	@Autowired
	private ReportMembershipVerifyDetailUntransService membershipVerifyDetailUntransService;
	@Autowired
	private ReportMembershipVerifyGatherUntransService membershipVerifyGatherUntransService;
	@Autowired
	private ReportSeatSaleCheckDetailUntransService seatSaleCheckDetailUntransService;
	@Autowired
	private ReportGroupReserveCheckDetailUntransService groupReserveCheckDetailUntransService;
	@Autowired
	private ReportGroupReserveCheckGatherUntransService groupReserveCheckGatherUntransService;
	@Autowired
	private ReportSeatSaleGatherDayUntransService seatSaleGatherDayUntransService;
	@Autowired
	private ReportSeatSaleGatherProgramUntransService seatSaleGatherProgramUntransService;
	@Autowired
	private ReportSeatCheckGatherUntransService seatCheckGatherUntransService;
	@Autowired
	private ReportMemberInfoUntransService memberInfoUntransService;
	@Autowired
	private ReportStandRevenueGatherUntransService standRevenueGatherUntransService;
	@Autowired
	private ReportSeatRevenueGatherUntransService seatRevenueGatherUntransService;

	/**
	 * 导出站票销售及入场明细表
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadStandCheckDetail.xhtml")
	public String downloadStandCheckDetail(HttpServletRequest request, HttpServletResponse response, SearReportStandCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		String filename = "站票销售及入场明细表_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportStandCheckDetailCount.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = standCheckDetailUntransService.getTotalCount(user,search,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "站票销售及入场明细表");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 导出预约及入场明细表
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/reserve/downloadReserveCheckDetail.xhtml")
	public String downloadReserveCheckDetail(HttpServletRequest request, HttpServletResponse response, SearchReserveCheckDetail search) {
		AuthUser user = getTbsLogonUser();
		String filename = "预约及入场明细表_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportReserveCheckDetail.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = reserveCheckDetailUntransService.getTotalCount(user,search,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "预约及入场明细表");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 导出会员卡明细表
	 * @param request
	 * @param response
	 * @param datefrom
	 * @param dateto
	 * @param memberCardTypeId
	 * @return
	 */
	@RequestMapping("/home/<USER>/member/downloadMemberSaleDetail.xhtml")
	public String downloadMemberSaleDetail(HttpServletRequest request, HttpServletResponse response, Date datefrom, Date dateto, String memberCardTypeId){
		AuthUser user = getTbsLogonUser();
		String filename = "会员卡销售明细表_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportMemberSaleDetail.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = memberSaleDetailUntransService.getTotalCount(user,datefrom, dateto, memberCardTypeId,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "会员卡销售明细表");
		ReportTitleVo vo1 = new ReportTitleVo(1, 0,
				"日期：" + DateUtil.formatDate(datefrom) + " 至 " + DateUtil.formatDate(dateto));
		titleVos.add(vo0);
		titleVos.add(vo1);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 3);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 下载站票销售及入场汇总表（按项目）
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadStandSaleCheckGather.xhtml")
	public String downloadStandSaleCheckGather(HttpServletRequest request, HttpServletResponse response, SearReportStandCheckGatherCount search){
		AuthUser user = getTbsLogonUser();
		String filename = "站票销售汇总表(按项目)_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportStandSaleGather.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = saleCheckGatherUntransService.getTotalCount(user, search,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "站票销售汇总表(按项目)");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 导出会员卡销售汇总表
	 * @param request
	 * @param response
	 * @param datefrom
	 * @param dateto
	 * @param membershipCardId
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadReportMembershipSaleGather.xhtml")
	public String downloadReportMembershipSaleGather(HttpServletRequest request, HttpServletResponse response,Date datefrom, Date dateto, String membershipCardId) {
		AuthUser user = getTbsLogonUser();
		String filename = "会员卡销售汇总表_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportMembershipSaleGather.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = memberSaleGatherUntransService.getTotalCount(user, datefrom, dateto, membershipCardId,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "会员卡销售汇总表");
		ReportTitleVo vo1 = new ReportTitleVo(1, 0,
				"日期：" + DateUtil.formatDate(datefrom) + " 至 " + DateUtil.formatDate(dateto));
		titleVos.add(vo0);
		titleVos.add(vo1);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 3);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 导出预约及入场明细表
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/reserve/downloadReserveCheckGather.xhtml")
	public String downloadReserveCheckGather(HttpServletRequest request, HttpServletResponse response, ReportReserveCheckGatherReq search) {
		AuthUser user = getTbsLogonUser();
		String filename = "预约及入场汇总表_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportReserveCheckGather.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = reserveCheckGatherUntransService.getTotalCount(user, search,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "预约及入场汇总表");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 下载会员卡入场明细表
	 * @param request
	 * @param response
	 * @param datefrom
	 * @param dateto
	 * @param memberCardTypeId
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadReportMembershipCheckDetail.xhtml")
	public String downloadReportMembershipCheckDetail(HttpServletRequest request, HttpServletResponse response,Date datefrom, Date dateto, String memberCardTypeId) {
		AuthUser user = getTbsLogonUser();
		String filename = "会员卡入场明细表_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportMembershipCheckDetail.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = membershipCheckDetailUntransService.getTotalCount(datefrom, dateto, memberCardTypeId, user,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "会员卡入场明细表");
		ReportTitleVo vo1 = new ReportTitleVo(1, 0,
				"日期：" + DateUtil.formatDate(datefrom) + " 至 " + DateUtil.formatDate(dateto));
		titleVos.add(vo0);
		titleVos.add(vo1);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 3);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 下载站票销售及入场汇总表（按天）
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadStandSaleGather.xhtml")
	public String downloadStandSaleGather(HttpServletRequest request, HttpServletResponse response, SearReportStandSaleCount search){
		AuthUser user = getTbsLogonUser();
		String filename = "站票销售汇总表(按天)_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportStandSaleCheckGather.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = standSaleGatherUntransService.getTotalCount(user, search,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "站票销售汇总表(按天)");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	@RequestMapping("/home/<USER>/sale/downloadMembershipCheckGather.xhtml")
	public String downloadMembershipCheckGather(HttpServletRequest request, HttpServletResponse response, Date datefrom, Date dateto,
	                                            String membershipCardTypeId){
		AuthUser user = getTbsLogonUser();
		String filename = "会员卡入场汇总表" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportMembershipCheckGather.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = membershipCheckGatherUntransService.getTotalCount(datefrom, dateto, membershipCardTypeId, user,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "会员卡入场汇总表");
		ReportTitleVo vo1 = new ReportTitleVo(1, 0,
				"日期：" + DateUtil.formatDate(datefrom) + " 至 " + DateUtil.formatDate(dateto));
		titleVos.add(vo0);
		titleVos.add(vo1);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 3);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	@RequestMapping("/home/<USER>/sale/downloadCheckRecord.xhtml")
	public String downloadCheckRecord(HttpServletRequest request, HttpServletResponse response, SearReportStandCheckDetailCount search){
		AuthUser user = getTbsLogonUser();
		String filename = "入场明细表" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportCheckRecord.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = checkRecordUntransService.getTotalCount(user,search,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "入场明细表");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	@RequestMapping("/home/<USER>/sale/downloadStandCheckGather.xhtml")
	public String downloadStandCheckGather(HttpServletRequest request, HttpServletResponse response, SearReportStandCheckDetailCount search){
		AuthUser user = getTbsLogonUser();
		String filename = "站票入场汇总表" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportStandCheckGather.xlsx");
		Page page = new Page(1,60000);
		List<List<Object>> totalCounts = standCheckGatherUntransService.getTotalCount(user,search,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "站票入场汇总表");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	@RequestMapping("/home/<USER>/membership/downloadMembershipVerifyDetail.xhtml")
	public String downloadMembershipVerifyDetail(HttpServletRequest request, HttpServletResponse response, Date datefrom,
	                                       Date dateto, String membershipCardTypeId, Long checkGroupId,Page page){
		AuthUser user = getTbsLogonUser();
		String filename = "会员卡验证明细表" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportMembershipVerifyDetail.xlsx");
		List<List<Object>> totalCounts = membershipVerifyDetailUntransService.getTotalCount(user, datefrom, dateto, membershipCardTypeId, checkGroupId,page);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "会员卡验证明细表");
		ReportTitleVo vo1 = new ReportTitleVo(1, 0,
				"日期：" + DateUtil.formatDate(datefrom) + " 至 " + DateUtil.formatDate(dateto));
		titleVos.add(vo0);
		titleVos.add(vo1);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 3);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	@RequestMapping("/home/<USER>/membership/downloadMembershipVerifyGather.xhtml")
	public String downloadMembershipVerifyGather(HttpServletRequest request, HttpServletResponse response, Date datefrom,
	                                             Date dateto, String membershipCardTypeId){
		AuthUser user = getTbsLogonUser();
		String filename = "会员卡验证汇总表" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportMembershipVerifyGather.xlsx");
		List<List<Object>> totalCounts = membershipVerifyGatherUntransService.getTotalCount(user, datefrom, dateto, membershipCardTypeId);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "会员卡验证汇总表");
		ReportTitleVo vo1 = new ReportTitleVo(1, 0,
				"日期：" + DateUtil.formatDate(datefrom) + " 至 " + DateUtil.formatDate(dateto));
		titleVos.add(vo0);
		titleVos.add(vo1);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 3);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 导出座票销售及入场明细表
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadSeatCheckDetail.xhtml")
	public String downloadSeatCheckDetail(HttpServletRequest request, HttpServletResponse response, SearReportStandCheckDetailCount search) {
		AuthUser user = getTbsLogonUser();
		String filename = "座票销售及入场明细表_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportSeatSaleCheckDetailCount.xlsx");
		List<List<Object>> totalCounts = seatSaleCheckDetailUntransService.getTotalCount(user,search);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "座票销售及入场明细表");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 导出团体预约及入场明细表
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadGroupReserveCheckDetail.xhtml")
	public String downloadGroupReserveCheckDetail(HttpServletRequest request, HttpServletResponse response, SearchGroupReserveCheckDetail search) {
		AuthUser user = getTbsLogonUser();
		if (search.getDatefrom() == null && search.getDateto() == null &&
				search.getCheckDateFrom() == null && search.getCheckDateTo() == null && search.getReserveDateFrom() == null && search.getReserveDateTo() == null &&
				search.getProgramId() == null && search.getShowId() == null && StringUtils.isBlank(search.getReserveNo()) &&
				StringUtils.isBlank(search.getDistributorName()) && StringUtils.isBlank(search.getStatuses())) {
			return jsonErrorMsg("筛选项不能为空");
		}
		String filename = "团体预约及入场明细表_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportGroupReserveCheckDetail.xlsx");
		List<List<Object>> totalCounts = groupReserveCheckDetailUntransService.getTotalCount(user,search);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "团体预约及入场明细表");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 导出团体预约销售收入统计
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadGroupReserveCheckGather.xhtml")
	public String downloadGroupReserveCheckGather(HttpServletRequest request, HttpServletResponse response, SearchGroupReserveCheckDetail search) {
		AuthUser user = getTbsLogonUser();
		if (search.getDatefrom() == null || search.getDateto() == null) {
			return jsonErrorMsg("日期不存在");
		}
		String filename = "团体预约销售收入统计表_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportGroupReserveCheckGather.xlsx");
		List<List<Object>> totalCounts = groupReserveCheckGatherUntransService.getTotalCount(user,search);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "团体预约销售收入统计表");
		ReportTitleVo vo1 = new ReportTitleVo(1, 0,
				"日期：" + DateUtil.formatDate(search.getDatefrom()) + " 至 " + DateUtil.formatDate(search.getDateto()));
		titleVos.add(vo0);
		titleVos.add(vo1);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 3);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 下载座票销售汇总表(按天)
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadSeatSaleGatherDay.xhtml")
	public String downloadSeatSaleGatherDay(HttpServletRequest request, HttpServletResponse response, SearReportStandSaleCount search){
		AuthUser user = getTbsLogonUser();
		String filename = "座票销售汇总表(按天)_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportSeatSaleGatherDay.xlsx");
		List<List<Object>> totalCounts = seatSaleGatherDayUntransService.getTotalCount(user, search);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "座票销售汇总表(按天)");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 下载座票销售及入场汇总表（按项目）
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadSeatSaleGatherProgram.xhtml")
	public String downloadSeatSaleGatherProgram(HttpServletRequest request, HttpServletResponse response, SearReportStandCheckGatherCount search){
		AuthUser user = getTbsLogonUser();
		String filename = "座票销售汇总表(按项目)_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportSeatSaleGatherProgram.xlsx");
		List<List<Object>> totalCounts = seatSaleGatherProgramUntransService.getTotalCount(user, search);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "座票销售汇总表(按项目)");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	@RequestMapping("/home/<USER>/sale/downloadSeatCheckGather.xhtml")
	public String downloadSeatCheckGather(HttpServletRequest request, HttpServletResponse response, SearReportStandCheckDetailCount search){
		AuthUser user = getTbsLogonUser();
		String filename = "座票入场汇总表" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportSeatCheckGather.xlsx");
		List<List<Object>> totalCounts = seatCheckGatherUntransService.getTotalCount(user,search);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "座票入场汇总表");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 下载会员信息列表
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/member/downloadMemberInfos.xhtml")
	public String downloadMemberInfos(HttpServletRequest request, HttpServletResponse response, SearReportMemberInfo search){
		AuthUser user = getTbsLogonUser();
		String filename = "会员信息列表_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportMemberInfo.xlsx");
		List<List<Object>> totalCounts = memberInfoUntransService.getTotalCount(user, search);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "会员信息列表");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 下载站票销售收入统计
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadStandRevenue.xhtml")
	public String downloadStandRevenue(HttpServletRequest request, HttpServletResponse response, SearReportStandSaleCount search){
		AuthUser user = getTbsLogonUser();
		String filename = "站票销售收入统计_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportStandRevenueGather.xlsx");
		List<List<Object>> totalCounts = standRevenueGatherUntransService.getTotalCount(user, search);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "站票销售收入统计");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}

	/**
	 * 下载站票销售及入场汇总表（按天）
	 * @param request
	 * @param response
	 * @param search
	 * @return
	 */
	@RequestMapping("/home/<USER>/sale/downloadSeatRevenue.xhtml")
	public String downloadSeatRevenue(HttpServletRequest request, HttpServletResponse response, SearReportStandSaleCount search){
		AuthUser user = getTbsLogonUser();
		String filename = "座票销售收入统计_" + DateUtil.format(DateUtil.getCurFullTimestamp(), "yyyyMMddHHmmss") + ".xlsx";
		this.setDownloadHeader(response, request, filename);
		InputStream inputStream = this.getClass().getResourceAsStream("/report/ReportSeatRevenueGather.xlsx");
		List<List<Object>> totalCounts = seatRevenueGatherUntransService.getTotalCount(user, search);
		if (CollectionUtils.isEmpty(totalCounts)) {
			return jsonErrorMsg("数据不存在");
		}
		List<ReportTitleVo> titleVos = new ArrayList<>();
		ReportTitleVo vo0 = new ReportTitleVo(0, 0, "座票销售收入统计");
		titleVos.add(vo0);
		try{
			XLSImporterUtil.write(inputStream, response.getOutputStream(), titleVos, totalCounts, 2);
		} catch (IOException e) {
			dbLogger.error(LoggerUtils.getExceptionTrace(e, 40));
		}
		return skipViewRender();
	}
}

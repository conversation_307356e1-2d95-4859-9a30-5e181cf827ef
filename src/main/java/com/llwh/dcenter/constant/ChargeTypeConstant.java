package com.llwh.dcenter.constant;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public abstract class ChargeTypeConstant {
	/**
	 * 充值
	 */
	public static final String CHARGE_TYPE_FIRST = "first";//首冲

	public static final String CHARGE_TYPE_RECHARGE = "recharge";//充值
	public static final String CHARGE_TYPE_RENEWAL = "renewal";//续卡
	public static final String CHARGE_TYPE_REFUND = "refund";//订单退款
	public static final String CHARGE_TYPE_ADJUST = "adjust_charge";//手工增加
	public static final String CHARGE_TYPE_HISTORY = "history";//历史记录
	/**
	 * 支付
	 */
	public static final String EXPEND_TYPE_SPEND = "spend";//消费

	public static final String EXPEND_TYPE_ADJUST = "adjust_expend";//手工扣除
	public static final String EXPEND_TYPE_HISTORY = "history";//历史记录

	public static final Map<String, String> chargeTypeMap;
	public static final Map<String, String> expendTypeMap;
	public static final Map<String, String> typeMap = new HashMap<>();
	static {
		Map<String, String> tmp = new LinkedHashMap<>();
		tmp.put(CHARGE_TYPE_FIRST, "首冲");
		tmp.put(CHARGE_TYPE_RECHARGE, "充值");
		tmp.put(CHARGE_TYPE_RENEWAL,"续卡");
		tmp.put(CHARGE_TYPE_REFUND,"订单退款");
		tmp.put(CHARGE_TYPE_ADJUST,"手工增加");
		tmp.put(CHARGE_TYPE_HISTORY,"历史记录");
		chargeTypeMap = Collections.unmodifiableMap(tmp);

		Map<String, String> tmpExpend = new LinkedHashMap<>();
		tmpExpend.put(EXPEND_TYPE_SPEND, "消费");
		tmpExpend.put(EXPEND_TYPE_ADJUST, "手工扣除");
		tmpExpend.put(EXPEND_TYPE_HISTORY, "历史记录");
		expendTypeMap = Collections.unmodifiableMap(tmpExpend);
		typeMap.putAll(chargeTypeMap);
		typeMap.putAll(expendTypeMap);
	}
}

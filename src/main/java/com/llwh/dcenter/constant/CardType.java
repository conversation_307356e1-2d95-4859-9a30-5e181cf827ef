package com.llwh.dcenter.constant;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;


public class CardType {
	public static final Map<String, String> CARDTYPEMAP;
	public static final String A = "A";
	public static final String C = "C";
	public static final String P = "P";
	public static final String W = "W";

	static {
		CARDTYPEMAP = Map.of(
				"A", "兑换券（兑换单张票）",
				"C", "抵用券（订单立减）",
				"P", "打折券（订单折扣）",
				"W", "微信现金券");

	}
	public static String getCardtypeText(String cardtype) {
		if(StringUtils.isBlank(cardtype)) {
			return "";
		}
		return CARDTYPEMAP.get(cardtype);
	}

	public static boolean isCardTypeA(String cardtype) {
		return A.equals(cardtype);
	}

	public static boolean isCardTypeC(String cardtype) {
		return C.equals(cardtype);
	}

	public static boolean isCardTypeP(String cardtype) {
		return P.equals(cardtype);
	}

	public static boolean isCardTypeW(String cardtype) {
		return W.equals(cardtype);
	}
}

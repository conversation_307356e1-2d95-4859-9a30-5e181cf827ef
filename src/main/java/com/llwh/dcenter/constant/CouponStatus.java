package com.llwh.dcenter.constant;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;


public class CouponStatus {
	public static final String NEW = "N"; // 待售
	public static final String SOLD = "Y"; // 售出
	public static final String DISCARD = "D";// 废弃
	public static final String USED = "U"; // 使用过
	public static final String LOCK = "L"; // 冻结使用, Y------>L

	public static final Map<String, String> STATUS_MAP;

	static {
		Map<String, String> tmp = new LinkedHashMap<>(5);
		tmp.put(NEW, "待售");
		tmp.put(SOLD, "可用");
		tmp.put(DISCARD, "废弃");
		tmp.put(USED, "已使用");
		tmp.put(LOCK, "冻结");

		STATUS_MAP = Collections.unmodifiableMap(tmp);
	}

	public static boolean isNew(String status) {
		return NEW.equals(status);
	}

	public static boolean isSold(String status) {
		return SOLD.equals(status);
	}

	public static boolean isUsed(String status) {
		return USED.equals(status);
	}

	public static boolean isLock(String status) {
		return LOCK.equals(status);
	}

	public static boolean isDiscard(String status) {
		return DISCARD.equals(status);
	}

}

package com.llwh.dcenter.mapper;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearReportStandSaleCount;
import com.llwh.dcenter.model.ReportStandSaleGather;

import org.apache.ibatis.annotations.Param;

public interface ReportStandSaleGatherMapper extends BaseMapper<ReportStandSaleGather> {

	@DS("theatre")
	List<Date> getStandSaleGatherTicketDate(@Param("startTime")Timestamp startTime, @Param("endTime") Timestamp endTime);

	@DS("theatre")
	List<ReportStandSaleGather> getStandCount(@Param("startTime")Timestamp startTime, @Param("endTime") Timestamp endTime);

	@DS("theatre")
	List<ReportStandSaleGather> getStandRefundCount(@Param("startTime")Timestamp startTime, @Param("endTime") Timestamp endTime);

	@DS("theatre")
	List<Double> getTicketPriceCount(@Param("ticketTypeId") Long ticketTypeId);

	List<ReportStandSaleGather> getTotalCount(@Param("search") SearReportStandSaleCount search,@Param("page") Page page);

	ReportStandSaleGather getTotals(@Param("search") SearReportStandSaleCount search);

	List<ReportStandSaleGather> getRevenueTotalCount(@Param("search") SearReportStandSaleCount search);

	Page<ReportStandSaleGather> getRevenueTotalCount(Page<ReportStandSaleGather> page, @Param("search") SearReportStandSaleCount search);

	ReportStandSaleGather getRevenueTotals(@Param("search") SearReportStandSaleCount search);

}

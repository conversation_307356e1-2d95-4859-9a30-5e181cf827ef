package com.llwh.dcenter.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.ReportSeatStandSaleCheckDetailSearch;
import com.llwh.dcenter.vo.ReportSeatStandSaleCheckDetailVo;
import com.llwh.dcenter.vo.report.ReportSeatStandSaleCheckDetailTotalVo;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2024/5/7 16:18
 */
public interface ReportSeatStandSaleCheckDetailMapper {

	Page<ReportSeatStandSaleCheckDetailVo> getCounts(Page<ReportSeatStandSaleCheckDetailVo> page, @Param("search") ReportSeatStandSaleCheckDetailSearch search);

	ReportSeatStandSaleCheckDetailTotalVo getTotals(@Param("search") ReportSeatStandSaleCheckDetailSearch search);

	Double getBalanceTotalAmount(@Param("search") ReportSeatStandSaleCheckDetailSearch search);
}

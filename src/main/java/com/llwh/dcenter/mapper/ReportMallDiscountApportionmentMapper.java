package com.llwh.dcenter.mapper;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.llwh.dcenter.model.ReportMallDiscountApportionment;

import org.apache.ibatis.annotations.Param;

public interface ReportMallDiscountApportionmentMapper extends BaseMapper<ReportMallDiscountApportionment> {

	@DS("mall")
	List<Map> getMallTradeNo(@Param("startTime")Timestamp startTime, @Param("endTime")Timestamp endTime, @Param("tradeNo") String tradeNo);

	@DS("mall")
	List<Map> getMallRefundSerialNo(@Param("startTime")Timestamp startTime, @Param("endTime")Timestamp endTime, @Param("serialNo") String serialNo);

}

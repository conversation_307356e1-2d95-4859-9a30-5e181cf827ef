package com.llwh.dcenter.mapper;

import java.util.Map;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.StpReportSearch;

import org.apache.ibatis.annotations.Param;

public interface ReportTicketOrShowGatherMapper {

	@DS("theatre")
	Page<Map<String, Object>> getShowGather(@Param("search")StpReportSearch search, Page<StpReportSearch> page);

	@DS("theatre")
	Page<Map<String, Object>> getTicketGather(@Param("search")StpReportSearch search, Page<StpReportSearch> page);
}

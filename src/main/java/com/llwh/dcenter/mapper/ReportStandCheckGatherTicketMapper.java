package com.llwh.dcenter.mapper;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.model.ReportStandCheckGatherTicket;

import org.apache.ibatis.annotations.Param;

public interface ReportStandCheckGatherTicketMapper extends BaseMapper<ReportStandCheckGatherTicket> {
		List<ReportStandCheckGatherTicket> getStandCheckCount(@Param("startTime")Timestamp startTime, @Param("endTime") Timestamp endTime);
		List<ReportStandCheckGatherTicket> getTotalCount(@Param("search") SearReportStandCheckDetailCount search);
		Map getTotals(@Param("search") SearReportStandCheckDetailCount search);
}

package com.llwh.dcenter.mapper.wlj;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.llwh.dcenter.vo.wlj.WljPriceCountVo;
import com.llwh.dcenter.vo.wlj.WljProgramVo;

import org.apache.ibatis.annotations.Param;

public interface WljSeatSaleGatherDayMapper {
	@DS("theatre")
	List<WljPriceCountVo> getSalePriceCountByPaidtime(@Param("companyId") Long companyId, @Param("programIds") List<Long> programIds, @Param("paidtime") Date paidtime);
	@DS("theatre")
	List<WljPriceCountVo> getAllSalePriceCountByPaidtime(@Param("companyId") Long companyId, @Param("programIds") List<Long> programIds,  @Param("paidtimeFrom") Timestamp paidtimeFrom, @Param("paidtimeTo") Timestamp paidtimeTo);

	@DS("theatre")
	List<WljPriceCountVo> getRefundPriceCountByRefundtime(@Param("companyId") Long companyId, @Param("programIds") List<Long> programIds, @Param("refundtime") Date refundtime);
	@DS("theatre")
	List<WljPriceCountVo> getAllRefundPriceCountByRefundtime(@Param("companyId") Long companyId, @Param("programIds") List<Long> programIds, @Param("refundtimeFrom") Timestamp refundtimeFrom, @Param("refundtimeTo") Timestamp refundtimeTo);

	@DS("theatre")
	List<WljProgramVo> getPageByUpdatetime(@Param("companyId") Long companyId, @Param("offset") int offset, @Param("pageSize") int pageSize, @Param("updatetime") Timestamp updatetime);

	@DS("theatre")
	WljProgramVo getWljProgram(@Param("companyId") Long companyId, @Param("programId") Long programId);

	@DS("theatre")
	List<WljProgramVo> getWljPrograms(@Param("companyId") Long companyId, @Param("programIds") List<Long> programIds);

	@DS("theatre")
	List<WljProgramVo> getPageWithStadium(@Param("companyId") Long companyId, @Param("offset") int offset, @Param("pageSize") int pageSize);

	@DS("theatre")
	List<WljProgramVo> getAllPageWithStadium(@Param("companyId") Long companyId, @Param("offset") int offset, @Param("pageSize") int pageSize);

	@DS("theatre")
	Integer getScheduleSeatCount(@Param("scheduleIds") List<Long> scheduleIds);
}

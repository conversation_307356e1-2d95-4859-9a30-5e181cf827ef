package com.llwh.dcenter.mapper;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.SearIdcardVerifyCheck;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.model.ReportCheckRecord;
import com.llwh.dcenter.vo.report.CheckRecordTotalVo;
import com.llwh.dcenter.vo.report.CheckRecordVo;
import com.llwh.dcenter.vo.report.IdcardVerifyCheckRecordVo;

import org.apache.ibatis.annotations.Param;

public interface CheckRecordMapper extends BaseMapper<ReportCheckRecord> {

	@DS("anterminal")
	List<ReportCheckRecord> getRecordCounts(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime, @Param("uuid") String uuid);

	@DS("anterminal")
	List<ReportCheckRecord> getExchangeRecordCounts(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime, @Param("uuid") String uuid);

	@DS("theatre")
	List<ReportCheckRecord> getShowInfos(List<String> uuids);

	@DS("theatre")
	List<ReportCheckRecord> getShowOrderInfos(List<String> uuids);

	@DS("theatre")
	List<ReportCheckRecord> getTicketInfos(List<String> uuids);

	@DS("theatre")
	List<ReportCheckRecord> getGeneralShowReserveInfos(List<String> uuids);


	List<ReportCheckRecord> getTotalCount(@Param("search") SearReportStandCheckDetailCount search, @Param("page") Page page);

	CheckRecordVo getTotals(@Param("search") SearReportStandCheckDetailCount search);

	@DS("anterminal")
	Page<IdcardVerifyCheckRecordVo> getIdcardVerifyChecks(@Param("search") SearIdcardVerifyCheck search, @Param("page") Page page);

	Page<ReportCheckRecord> pagingQuery(Page<ReportCheckRecord> page);

	CheckRecordVo getShowTotals(@Param("search") SearReportStandCheckDetailCount search);

	CheckRecordVo getExchangeTotals(@Param("search") SearReportStandCheckDetailCount search);

	List<CheckRecordTotalVo> showCheckTotalByDayDimension(@Param("search") SearReportStandCheckDetailCount search);

	List<CheckRecordTotalVo> showCheckTotalByStadiumDimension(@Param("search") SearReportStandCheckDetailCount search);
}

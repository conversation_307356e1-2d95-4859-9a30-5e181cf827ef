package com.llwh.dcenter.mapper;

import java.util.List;
import java.util.Map;

import com.llwh.dcenter.helper.ReportUserTicketingGatherSearch;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface ReportUserTicketingGatherMapper {

	List<Map<String, Object>> getCounts(@Param("companyId") Long companyId, @Param("search") ReportUserTicketingGatherSearch search);

	List<Map<String, Object>> getTotals(@Param("companyId") Long companyId, @Param("search") ReportUserTicketingGatherSearch search);
}

package com.llwh.dcenter.mapper;

import java.util.List;

import com.llwh.dcenter.helper.ProgramStatisticsSearch;
import com.llwh.dcenter.vo.programStatistics.SourceSaleTicketVo;

import org.apache.ibatis.annotations.Param;

public interface SeatSourceGatherMapper {

	List<SourceSaleTicketVo> getSaleDetailsBySellType(@Param("companyId") Long companyId, @Param("search") ProgramStatisticsSearch search);
	List<SourceSaleTicketVo> getSaleDetailsBySchedule(@Param("companyId") Long companyId, @Param("search") ProgramStatisticsSearch search);

	List<SourceSaleTicketVo> getSaleDetailsByProgram(@Param("companyId") Long companyId, @Param("search") ProgramStatisticsSearch search);
}

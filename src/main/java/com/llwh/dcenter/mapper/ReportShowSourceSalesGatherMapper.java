package com.llwh.dcenter.mapper;

import java.util.List;
import java.util.Map;

import com.llwh.dcenter.helper.ReportShowSourceSalesGatherSearch;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface ReportShowSourceSalesGatherMapper {

	List<Map<String, Object>> getCounts(@Param("companyId") Long companyId, @Param("search") ReportShowSourceSalesGatherSearch search);
	List<Map<String, Object>> getProgramCounts(@Param("companyId") Long companyId, @Param("search") ReportShowSourceSalesGatherSearch search);
	List<Map<String, Object>> getSourceProgramWithoutShowCounts(@Param("companyId") Long companyId, @Param("search") ReportShowSourceSalesGatherSearch search);
}

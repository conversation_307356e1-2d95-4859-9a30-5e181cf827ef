package com.llwh.dcenter.mapper;

import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.llwh.dcenter.helper.ProgramSettleSearch;
import com.llwh.dcenter.vo.ProgramSettle.ReserveNumVo;
import com.llwh.dcenter.vo.ProgramSettle.SaleDetailsVo;
import com.llwh.dcenter.vo.ProgramSettle.SaleNumVo;
import com.llwh.dcenter.vo.ProgramSettle.TotalNumVo;

import org.apache.ibatis.annotations.Param;

public interface ReportProgramSettleSeatMapper {

	@DS("theatre")
	List<TotalNumVo> getTotalNum(@Param("search") ProgramSettleSearch search);

	@DS("theatre")
	List<ReserveNumVo> getReserveNum(@Param("search") ProgramSettleSearch search);

	List<SaleNumVo> getSaleNum(@Param("search") ProgramSettleSearch search);

	List<SaleNumVo> getOwnSaleNum(@Param("search") ProgramSettleSearch search, @Param("otaList") List<Long> otaList);

	List<SaleDetailsVo> getSaleDetailsByChannel(@Param("search") ProgramSettleSearch search);

	List<SaleDetailsVo> getSaleDetailsBySellType(@Param("search") ProgramSettleSearch search);
}

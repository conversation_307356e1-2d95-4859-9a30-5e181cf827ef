package com.llwh.dcenter.mapper;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.llwh.dcenter.helper.ReportBalanceCardInfoSearch;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface ReportBalanceCardInfoMapper {

	@DS("leaguer")
	List<Map<String, Object>> getCounts(@Param("companyId") Long companyId, @Param("search") ReportBalanceCardInfoSearch search);

	@DS("leaguer")
	List<Map<String, Object>> getBalanceOfTheTime(@Param("companyId") Long companyId, @Param("cardnoList") List<String> cardnoList, @Param("updatetime") Timestamp updatetime);

	@DS("leaguer")
	List<Map<String, Object>> getPeriodChargeList(@Param("companyId") Long companyId, @Param("cardnoList") List<String> cardnoList, @Param("updatetimeFrom") Timestamp updatetimeFrom, @Param("updatetimeTo") Timestamp updatetimeTo);

	@DS("leaguer")
	List<Map<String, Object>> getPeriodExpendList(@Param("companyId") Long companyId, @Param("cardnoList") List<String> cardnoList, @Param("updatetimeFrom") Timestamp updatetimeFrom, @Param("updatetimeTo") Timestamp updatetimeTo);
}

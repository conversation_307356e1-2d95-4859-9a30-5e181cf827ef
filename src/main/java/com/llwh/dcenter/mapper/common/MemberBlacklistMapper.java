package com.llwh.dcenter.mapper.common;

import com.baomidou.dynamic.datasource.annotation.DS;
import java.util.Collection;
import java.util.List;

import com.llwh.dcenter.vo.common.MemberBlacklistVo;

import org.apache.ibatis.annotations.Param;

/*
 * 功能描述: <br>
 * @Author: zhangbiaoyan
 * @Date: 2023/2/22 10:00
 */
public interface MemberBlacklistMapper {

    @DS("ucenter")
    MemberBlacklistVo getByMemberId(@Param("companyId") Long companyId, @Param("memberId") Long memberId);

    @DS("ucenter")
    List<MemberBlacklistVo> getByMemberIds(@Param("companyId") Long companyId, @Param("memberIds") Collection<Long> memberIds);

}

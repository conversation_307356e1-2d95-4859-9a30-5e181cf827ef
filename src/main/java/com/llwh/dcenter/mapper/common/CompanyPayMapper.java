package com.llwh.dcenter.mapper.common;

import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.llwh.dcenter.vo.common.CompanyPayVo;

import org.apache.ibatis.annotations.Param;

public interface CompanyPayMapper {

	@DS("theatre")
	List<CompanyPayVo> getAvailableAll(@Param("companyId") Long companyId);

	@DS("theatre")
	List<CompanyPayVo> getAll(@Param("companyId") Long companyId);
}

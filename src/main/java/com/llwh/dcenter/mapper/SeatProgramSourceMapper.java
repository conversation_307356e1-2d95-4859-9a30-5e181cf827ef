package com.llwh.dcenter.mapper;

import java.util.List;

import com.llwh.dcenter.helper.ProgramStatisticsSearch;
import com.llwh.dcenter.vo.programStatistics.ProgramTicketPriceVo;

import org.apache.ibatis.annotations.Param;

public interface SeatProgramSourceMapper {

	List<ProgramTicketPriceVo> getSaleDetailsByDiscount(@Param("companyId") Long companyId, @Param("search") ProgramStatisticsSearch search);
	ProgramTicketPriceVo getTotals(@Param("companyId") Long companyId, @Param("search") ProgramStatisticsSearch search);

	Double getBalanceTotalAmount(@Param("companyId") Long companyId, @Param("search") ProgramStatisticsSearch search);
}

package com.llwh.dcenter.mapper;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.llwh.dcenter.helper.SearMemberInfoCountByDay;
import com.llwh.dcenter.model.ReportMembershipCountByDay;

import org.apache.ibatis.annotations.Param;

public interface ReportMembershipCountByDayMapper extends BaseMapper<ReportMembershipCountByDay> {
	@DS("leaguer")
	List<ReportMembershipCountByDay> getCountByDay(@Param("lastDay")Timestamp lastDay);

	List<ReportMembershipCountByDay> listCount(@Param("search") SearMemberInfoCountByDay search);
}

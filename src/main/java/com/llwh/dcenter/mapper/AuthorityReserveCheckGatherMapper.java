package com.llwh.dcenter.mapper;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.model.ReportReserveCheckGather;
import com.llwh.dcenter.vo.report.ReportReserveCheckGatherSumVo;
import com.llwh.dcenter.vo.req.ReportReserveCheckGatherReq;

import org.apache.ibatis.annotations.Param;

public interface AuthorityReserveCheckGatherMapper extends BaseMapper<ReportReserveCheckGather> {

	@DS("theatre")
	List<ReportReserveCheckGather> getReserveCheckGather(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

	Page<ReportReserveCheckGather> getTotalCount(Page<ReportReserveCheckGather> page, @Param("search") ReportReserveCheckGatherReq search, @Param("reserveProgramIds") List<Long> reserveProgramIds);

	List<ReportReserveCheckGather> getTotalCount(@Param("search") ReportReserveCheckGatherReq search);

	IPage<ReportReserveCheckGather> getCounts(Page<ReportReserveCheckGather> page, @Param("search") ReportReserveCheckGatherReq search);

	ReportReserveCheckGatherSumVo getSumReserveCheckGather(@Param("search") ReportReserveCheckGatherReq search, @Param("reserveProgramIds") List<Long> reserveProgramIds);
}

package com.llwh.dcenter.mapper;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.model.ReportMemberSaleGather;

import org.apache.ibatis.annotations.Param;

public interface ReportMemberSaleGatherMapper extends BaseMapper<ReportMemberSaleGather> {

		@DS("leaguer")
		List<Date> getMembershipUpdateDate(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

		@DS("leaguer")
		List<ReportMemberSaleGather> getMemberShipCount(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

		@DS("leaguer")
		List<ReportMemberSaleGather> getOfflineMemberShipCount(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

		List<ReportMemberSaleGather> getDaySaleCount(@Param("companyId") Long companyId,@Param("datefrom") Date datefrom, @Param("dateto") Date dateto);

		@DS("leaguer")
		List<ReportMemberSaleGather> getMemberShipRefund(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

		@DS("leaguer")
		List<ReportMemberSaleGather> getOfflineMemberShipRefund(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);


		@DS("leaguer")
		List<Double> getPrice(@Param("memberCardTypeId") String memberCardTypeId);

		@DS("theatre")
		List<String> getUserGroupName(@Param("userGroupId") Long userGroupId);

		List<ReportMemberSaleGather> getTotalCount(@Param("datefrom") Date datefrom, @Param("dateto") Date dateto,
		                                           @Param("memberCardTypeId") String memberCardTypeId, @Param("companyId") Long companyId,@Param("page") Page page);

		IPage<ReportMemberSaleGather> getCounts(Page<ReportMemberSaleGather> page, @Param("datefrom") Timestamp datefrom, @Param("dateto") Timestamp dateto,
		                                        @Param("memberCardTypeId") String memberCardTypeId, @Param("companyId") Long companyId,
		                                        @Param("userGroupId") Long userGroupId, @Param("paymethod") String paymethod);

		ReportMemberSaleGather getTotals(@Param("datefrom") Timestamp datefrom, @Param("dateto") Timestamp dateto,
	                                        @Param("memberCardTypeId") String memberCardTypeId, @Param("companyId") Long companyId,
	                                     @Param("userGroupId") Long userGroupId, @Param("paymethod") String paymethod);

}

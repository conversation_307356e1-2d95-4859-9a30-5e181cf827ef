package com.llwh.dcenter.mapper;

import java.util.List;
import java.util.Map;

import com.llwh.dcenter.helper.ReportShowProgramSourceSalesGatherSearch;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface ReportShowProgramSourceSalesGatherMapper {
	List<Map<String, Object>> getCounts(@Param("companyId") Long companyId, @Param("search") ReportShowProgramSourceSalesGatherSearch search);

	Map<String, Object> getTotals(@Param("companyId") Long companyId, @Param("search") ReportShowProgramSourceSalesGatherSearch search);
}

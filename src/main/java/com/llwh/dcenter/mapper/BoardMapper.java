package com.llwh.dcenter.mapper;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.llwh.dcenter.vo.report.CheckRecord;
import com.llwh.dcenter.vo.report.board.CheckSumVo;
import com.llwh.dcenter.vo.report.board.OpenmemberSumVo;
import com.llwh.dcenter.vo.report.board.ReserveSumVo;
import com.llwh.dcenter.vo.report.board.ShowOrderSumVo;
import com.llwh.dcenter.vo.report.board.StadiumPeopleSumVo;

import org.apache.ibatis.annotations.Param;

public interface BoardMapper extends BaseMapper<CheckRecord> {

	@DS("theatre")
	List<ShowOrderSumVo> getShowSaleSum(@Param("companyId") Long companyId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

	@DS("theatre")
	List<ShowOrderSumVo> getShowRefundSum(@Param("companyId") Long companyId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

	@DS("theatre")
	List<ReserveSumVo> getReserveSum(@Param("companyId") Long companyId, @Param("dateFrom") Date dateFrom, @Param("dateTo") Date dateTo);

	@DS("theatre")
	List<ReserveSumVo> getShowOrderReserveSum(@Param("companyId") Long companyId, @Param("dateFrom") Date dateFrom, @Param("dateTo") Date dateTo);

	@DS("theatre")
	List<CheckSumVo> getReserveCheckSum(@Param("companyId") Long companyId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

	@DS("theatre")
	List<CheckSumVo> getPassCheckSum(@Param("companyId") Long companyId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

	@DS("anterminal")
	List<CheckSumVo> getShowInfos(@Param("companyId") Long companyId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

	@DS("anterminal")
	List<CheckSumVo> getTicketInfos(@Param("companyId") Long companyId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

	@DS("ucenter")
	int getAllUserSum(Long companyId);

	@DS("ucenter")
	List<OpenmemberSumVo> getUserSum(@Param("companyId") Long companyId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

	@DS("anterminal")
	List<StadiumPeopleSumVo> getAllStadiumPeopleCount(@Param("companyId") Long companyId, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("type") String type);
}

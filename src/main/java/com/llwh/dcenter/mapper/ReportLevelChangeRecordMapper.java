package com.llwh.dcenter.mapper;

import java.util.Map;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.LevelChangeRecordReportSearch;

import org.apache.ibatis.annotations.Param;

public interface ReportLevelChangeRecordMapper {

	@DS("thvendor")
	Page<Map<String, Object>> reportLevelChangeRecord(@Param("search") LevelChangeRecordReportSearch search, Page<LevelChangeRecordReportSearch> page);
}

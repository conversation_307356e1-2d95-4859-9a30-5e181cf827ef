package com.llwh.dcenter.mapper;

import java.util.List;
import java.util.Map;

import com.llwh.dcenter.helper.ReportUserSalesGatherSearch;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface ReportUserSalesGatherMapper {

	List<Map<String, Object>> getCounts(@Param("companyId") Long companyId, @Param("search") ReportUserSalesGatherSearch search);

	Map<String, Object> getTotals(@Param("companyId") Long companyId, @Param("search") ReportUserSalesGatherSearch search);
}

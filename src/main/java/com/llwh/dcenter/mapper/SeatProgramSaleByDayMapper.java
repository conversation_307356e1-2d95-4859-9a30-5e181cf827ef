package com.llwh.dcenter.mapper;

import java.util.List;

import com.llwh.dcenter.helper.ProgramStatisticsSearch;
import com.llwh.dcenter.vo.programStatistics.ProgramSaleBySellTypeVo;

import org.apache.ibatis.annotations.Param;

public interface SeatProgramSaleByDayMapper {

	List<ProgramSaleBySellTypeVo> getSaleDetailsBySellType(@Param("companyId") Long companyId, @Param("search") ProgramStatisticsSearch search);
	ProgramSaleBySellTypeVo getTotals(@Param("companyId") Long companyId, @Param("search") ProgramStatisticsSearch search);

	Double getBalanceTotalAmount(@Param("companyId") Long companyId, @Param("search") ProgramStatisticsSearch search);
}

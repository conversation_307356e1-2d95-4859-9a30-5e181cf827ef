package com.llwh.dcenter.service.authority.impl;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SearchReserveCheckDetail;
import com.llwh.dcenter.mapper.AuthorityReserveCheckDetailMapper;
import com.llwh.dcenter.model.ReportReserveCheckDetail;
import com.llwh.dcenter.service.authority.AuthorityReserveCheckDetailService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AuthorityReserveCheckDetailServiceImpl extends ServiceImpl<AuthorityReserveCheckDetailMapper, ReportReserveCheckDetail> implements AuthorityReserveCheckDetailService {
	@Autowired
	private AuthorityReserveCheckDetailMapper reserveCheckDetailMapper;
	@Override
	public List<ReportReserveCheckDetail> getReserveOrderCount(Timestamp startTime, Timestamp endTime) {
		return reserveCheckDetailMapper.getReserveOrderCount(startTime, endTime);
	}

	@Override
	public Page<ReportReserveCheckDetail> getCounts(Page<ReportReserveCheckDetail> page, SearchReserveCheckDetail search, List<Long> reserveProgramIds) {
		return reserveCheckDetailMapper.getCounts(page, search, reserveProgramIds);
	}

	@Override
	public List<ReportReserveCheckDetail> getTotalCount(SearchReserveCheckDetail search) {
		return reserveCheckDetailMapper.getTotalCount(search);
	}
}

package com.llwh.dcenter.service.authority.impl;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SearReportStandCheckGatherCount;
import com.llwh.dcenter.mapper.AuthorityStandSaleCheckGatherMapper;
import com.llwh.dcenter.model.ReportStandSaleCheckGather;
import com.llwh.dcenter.service.authority.AuthorityStandSaleCheckGatherService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AuthorityStandSaleCheckGatherServiceImpl extends ServiceImpl<AuthorityStandSaleCheckGatherMapper, ReportStandSaleCheckGather> implements AuthorityStandSaleCheckGatherService {
	@Autowired
	private AuthorityStandSaleCheckGatherMapper saleCheckGatherMapper;

	@Override
	public List<Date> getStandUpdateDate(Timestamp startTime, Timestamp endTime) {
		return saleCheckGatherMapper.getStandUpdateDate(startTime, endTime);
	}

	@Override
	public List<ReportStandSaleCheckGather> getStandCount(Timestamp startTime, Timestamp endTime) {
		return saleCheckGatherMapper.getStandCount(startTime, endTime);
	}

	@Override
	public List<ReportStandSaleCheckGather> getStandRefundCount(Timestamp startTime, Timestamp endTime) {
		return saleCheckGatherMapper.getStandRefundCount(startTime, endTime);
	}

	@Override
	public List<Double> getTicketPriceCount(Long ticketTypeId) {
		return saleCheckGatherMapper.getTicketPriceCount(ticketTypeId);
	}

	@Override
	public Page<ReportStandSaleCheckGather> getCounts(Page<ReportStandSaleCheckGather> page, SearReportStandCheckGatherCount search, List<Long> searchProgramIds) {
		return saleCheckGatherMapper.getCounts(page, search, searchProgramIds);
	}

	@Override
	public List<ReportStandSaleCheckGather> getTotalCount(SearReportStandCheckGatherCount search) {
		return saleCheckGatherMapper.getTotalCount(search);
	}

	@Override
	public ReportStandSaleCheckGather getTotals(SearReportStandCheckGatherCount search){
		return saleCheckGatherMapper.getTotals(search);
	}
}

package com.llwh.dcenter.service.authority.impl;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.mapper.AuthorityStandCheckDetailMapper;
import com.llwh.dcenter.model.ReportStandCheckDetail;
import com.llwh.dcenter.service.authority.AuthorityStandCheckDetailService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AuthorityStandCheckDetailServiceImpl extends ServiceImpl<AuthorityStandCheckDetailMapper, ReportStandCheckDetail> implements AuthorityStandCheckDetailService {
	@Autowired
	private AuthorityStandCheckDetailMapper standCheckDetailMapper;

	@Override
	public List<ReportStandCheckDetail> getOrderCount(Timestamp startTime, Timestamp endTime) {
		return standCheckDetailMapper.getOrderCount(startTime, endTime);
	}

	@Override
	public List<ReportStandCheckDetail> getRefundDetailCount(Timestamp startTime, Timestamp endTime) {
		return standCheckDetailMapper.getRefundDetailCount(startTime, endTime);
	}

	@Override
	public Page<ReportStandCheckDetail> getTotalCount(Page<ReportStandCheckDetail> page, SearReportStandCheckDetailCount search, List<Long> programIds) {
		return standCheckDetailMapper.getTotalCount(page, search, programIds);
	}

	@Override
	public List<ReportStandCheckDetail> getTotalCount(SearReportStandCheckDetailCount search) {
		return standCheckDetailMapper.getTotalCount(search);
	}

	@Override
	public ReportStandCheckDetail getTotals(SearReportStandCheckDetailCount search){
		return standCheckDetailMapper.getTotals(search);
	}
}

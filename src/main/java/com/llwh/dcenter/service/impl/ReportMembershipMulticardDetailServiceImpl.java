package com.llwh.dcenter.service.impl;

import cn.fancylab.util.LoggerUtils;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.mapper.ReportMembershipMulticardDetailMapper;
import com.llwh.dcenter.model.ReportMembershipMulticardDetail;
import com.llwh.dcenter.service.ReportMembershipMulticardDetailService;

import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 会员卡兑换卡明细 数据交互服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Service
public class ReportMembershipMulticardDetailServiceImpl extends ServiceImpl<ReportMembershipMulticardDetailMapper, ReportMembershipMulticardDetail> implements ReportMembershipMulticardDetailService {
    protected final transient Logger dbLogger = LoggerUtils.getLogger(getClass());

}

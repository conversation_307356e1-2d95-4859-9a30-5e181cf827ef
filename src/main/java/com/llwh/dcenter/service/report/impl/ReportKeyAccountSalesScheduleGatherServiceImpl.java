package com.llwh.dcenter.service.report.impl;

import java.util.List;
import java.util.Map;

import com.llwh.dcenter.helper.ReportKeyAccountSalesScheduleGatherSearch;
import com.llwh.dcenter.mapper.ReportKeyAccountSalesScheduleGatherMapper;
import com.llwh.dcenter.service.report.ReportKeyAccountSalesScheduleGatherService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ReportKeyAccountSalesScheduleGatherServiceImpl implements ReportKeyAccountSalesScheduleGatherService {
	@Autowired
	private ReportKeyAccountSalesScheduleGatherMapper keyAccountSalesScheduleGatherMapper;

	@Override
	public List<Map<String, Object>> getCounts(Long companyId, ReportKeyAccountSalesScheduleGatherSearch search) {
		return keyAccountSalesScheduleGatherMapper.getCounts(companyId, search);
	}

	@Override
	public Map<String, Object> getTotals(Long companyId, ReportKeyAccountSalesScheduleGatherSearch search) {
		return keyAccountSalesScheduleGatherMapper.getTotals(companyId, search);
	}
}

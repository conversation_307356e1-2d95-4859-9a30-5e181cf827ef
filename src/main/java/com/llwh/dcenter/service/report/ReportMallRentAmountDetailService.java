package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llwh.dcenter.model.ReportMallRentAmountDetail;

public interface ReportMallRentAmountDetailService extends IService<ReportMallRentAmountDetail> {

	List<ReportMallRentAmountDetail> getRentOutCount(Timestamp startTime, Timestamp endTime);

	List<ReportMallRentAmountDetail> getRepayCount(Timestamp startTime, Timestamp endTime);

	List<ReportMallRentAmountDetail> getOfflineCount(Timestamp startTime, Timestamp endTime);
	List<ReportMallRentAmountDetail> getPickupRefundCount(Timestamp startTime, Timestamp endTime);



}

package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llwh.dcenter.helper.SearMemberInfoCountByDay;
import com.llwh.dcenter.model.ReportMembershipCountByDay;

public interface ReportMembershipCountByDayService extends IService<ReportMembershipCountByDay> {

	List<ReportMembershipCountByDay> getCountByDay(Timestamp lastDay);
	List<ReportMembershipCountByDay> listCount(SearMemberInfoCountByDay search);
}

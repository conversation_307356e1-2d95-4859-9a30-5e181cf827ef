package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SearchReportDouyinSeatOrderCheckCpsDetailCount;
import com.llwh.dcenter.mapper.ReportDouyinStandSaleCheckCpsDetailMapper;
import com.llwh.dcenter.model.ReportDouyinStandSaleCheckCpsDetail;
import com.llwh.dcenter.service.report.ReportDouyinStandSaleCheckCpsDetailService;
import com.llwh.dcenter.vo.DouyinOrderSettleVo;
import com.llwh.dcenter.vo.report.ReportDouyinStandSaleCheckCpsDetailVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportDouyinStandSaleCheckCpsDetailServiceImpl extends ServiceImpl<ReportDouyinStandSaleCheckCpsDetailMapper, ReportDouyinStandSaleCheckCpsDetail> implements ReportDouyinStandSaleCheckCpsDetailService {

	@Autowired
	private ReportDouyinStandSaleCheckCpsDetailMapper douyinSeatSaleCheckCpsDetailMapper;

	@Override
	public List<ReportDouyinStandSaleCheckCpsDetail> getStandCount(Timestamp startTime, Timestamp endTime, String tradeNo) {
		return douyinSeatSaleCheckCpsDetailMapper.getStandCount(startTime, endTime, tradeNo);
	}

	@Override
	public List<ReportDouyinStandSaleCheckCpsDetail> getRefundStandCount(Timestamp startTime, Timestamp endTime, String tradeNo) {
		return douyinSeatSaleCheckCpsDetailMapper.getRefundStandCount(startTime, endTime, tradeNo);
	}

	@Override
	public List<ReportDouyinStandSaleCheckCpsDetail> getStandCps(List<String> tradeNos) {
		return douyinSeatSaleCheckCpsDetailMapper.getStandCps(tradeNos);
	}

	@Override
	public List<DouyinOrderSettleVo> getStandSettle(List<String> tradeNos) {
		return douyinSeatSaleCheckCpsDetailMapper.getStandSettle(tradeNos);
	}

	@Override
	public List<String> getLatestUpdatedStandCps(Timestamp startTime, Timestamp endTime) {
		return douyinSeatSaleCheckCpsDetailMapper.getLatestUpdatedStandCps(startTime, endTime);
	}

	@Override
	public List<ReportDouyinStandSaleCheckCpsDetail> getTotalCount(SearchReportDouyinSeatOrderCheckCpsDetailCount search) {
		return douyinSeatSaleCheckCpsDetailMapper.getTotalCount(search);
	}


	@Override
	public Page<ReportDouyinStandSaleCheckCpsDetailVo> getCounts(Page<ReportDouyinStandSaleCheckCpsDetailVo> page, SearchReportDouyinSeatOrderCheckCpsDetailCount search) {
		return douyinSeatSaleCheckCpsDetailMapper.getCounts(page, search);
	}

	@Override
	public void removeNotDouyinPayOrder(String paymethod) {
		douyinSeatSaleCheckCpsDetailMapper.removeNotDouyinPayOrder(paymethod);
	}
}

package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.mapper.ReportStorageBoxDetailMapper;
import com.llwh.dcenter.model.ReportStorageBoxDetail;
import com.llwh.dcenter.service.report.ReportStorageBoxDetailService;
import com.llwh.dcenter.vo.report.ReportStorageBoxDetailTotalVo;
import com.llwh.dcenter.vo.report.ReportStorageBoxGroupVo;
import com.llwh.dcenter.vo.req.ReportStorageBoxDetailSearch;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportStorageBoxDetailServiceImpl extends ServiceImpl<ReportStorageBoxDetailMapper, ReportStorageBoxDetail> implements ReportStorageBoxDetailService {

	@Autowired
	private ReportStorageBoxDetailMapper reportStorageBoxDetailMapper;

	@Override
	public List<ReportStorageBoxDetail> getStorageBoxCount(Timestamp startTime, Timestamp endTime, String tradeNo, String uuid) {
		return reportStorageBoxDetailMapper.getStorageBoxCount(startTime, endTime, tradeNo, uuid);
	}

	@Override
	public List<ReportStorageBoxDetail> getRefundStorageBoxCount(Timestamp startTime, Timestamp endTime, String tradeNo, String uuid) {
		return reportStorageBoxDetailMapper.getRefundStorageBoxCount(startTime, endTime, tradeNo, uuid);
	}

	@Override
	public Page<ReportStorageBoxDetail> getCounts(Page<ReportStorageBoxDetail> page, ReportStorageBoxDetailSearch search, Long companyId) {
		return reportStorageBoxDetailMapper.getCounts(page, search, companyId);
	}

	@Override
	public ReportStorageBoxDetailTotalVo getTotals(ReportStorageBoxDetailSearch search, Long companyId) {
		return reportStorageBoxDetailMapper.getTotals(search, companyId);
	}

	@Override
	public List<ReportStorageBoxGroupVo> groupStorageBoxByDay(ReportStorageBoxDetailSearch search, Long companyId) {
		return reportStorageBoxDetailMapper.groupStorageBoxByDay(search, companyId);
	}
}

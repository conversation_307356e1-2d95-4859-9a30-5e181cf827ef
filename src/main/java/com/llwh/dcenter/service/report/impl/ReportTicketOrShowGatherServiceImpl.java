package com.llwh.dcenter.service.report.impl;

import java.util.List;
import java.util.Map;

import cn.fancylab.support.AuthUser;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.util.ValueUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.llwh.dcenter.helper.StpReportSearch;
import com.llwh.dcenter.mapper.ReportTicketOrShowGatherMapper;
import com.llwh.dcenter.service.report.ReportTicketOrShowGatherService;
import com.llwh.dcenter.untrans.common.DataLevelUntransService;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportTicketOrShowGatherServiceImpl implements ReportTicketOrShowGatherService {

	@Autowired
	private ReportTicketOrShowGatherMapper ticketOrShowGatherMapper;
	@Autowired
	private DataLevelUntransService dataLevelUntransService;

	@Override
	public ResultCode<Page<Map<String, Object>>> getScheduleTicketPriceReport(Long companyId, StpReportSearch search, Page<StpReportSearch> page) {
		if (search.getPlayTimeFrom() == null || search.getPlayTimeTo() == null) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不能为空");
		}
		search.setCompanyId(companyId);
		if(StringUtils.isNotBlank(search.getCategory())){
			search.setCategory("%" + search.getCategory() + "%");
		}
		if(StringUtils.isNotBlank(search.getSmallCategory())){
			search.setSmallCategory("%" + search.getSmallCategory() + "%");
		}
		Page<Map<String, Object>> resultPage = ticketOrShowGatherMapper.getTicketGather(search, page);
		for(Map<String, Object> map : resultPage.getRecords()){
			Double price = MapUtils.getDouble(map, "price");
			Integer totalNum = MapUtils.getInteger(map, "totalNum");
			Integer sellNum = MapUtils.getInteger(map, "sellNum");
			Integer holdNum = MapUtils.getInteger(map, "holdNum");
			Integer reserveNum = MapUtils.getInteger(map, "reserveNum");
			Integer planningNum = MapUtils.getInteger(map, "planningNum");
			Integer remainingNum = MapUtils.getInteger(map, "remainingNum");
			if(totalNum == null){
				map.replace("totalNum", 0);
				map.put("totalBoxOffice", 0);
			}else{
				map.put("totalBoxOffice", ValueUtil.round(price * totalNum));
			}
			if(sellNum == null){
				map.replace("sellNum", 0);
				map.put("sellBoxOffice", 0);
			}else{
				map.put("sellBoxOffice", ValueUtil.round(price * sellNum));
			}
			if(holdNum == null){
				map.replace("holdNum", 0);
				map.put("holdBoxOffice", 0);
			}else{
				map.put("holdBoxOffice", ValueUtil.round(price * holdNum));
			}
			if(reserveNum == null){
				map.replace("reserveNum", 0);
				map.put("reserveBoxOffice", 0);
			}else{
				map.put("reserveBoxOffice", ValueUtil.round(price * reserveNum));
			}
			if(planningNum == null){
				map.replace("planningNum", 0);
				map.put("planningBoxOffice", 0);
			}else{
				map.put("planningBoxOffice", ValueUtil.round(price * planningNum));
			}
			if(remainingNum == null){
				map.replace("remainingNum", 0);
				map.put("remainingBoxOffice", 0);
			}else{
				map.put("remainingBoxOffice", ValueUtil.round(price * remainingNum));
			}

		}
		return ResultCode.getSuccessReturn(resultPage);
	}

	@Override
	public ResultCode<Page<Map<String, Object>>> getShowTicketTypeReport(AuthUser user, StpReportSearch search, Page<StpReportSearch> page) {
		if (search.getPlayTimeFrom() == null || search.getPlayTimeTo() == null) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("日期不能为空");
		}
		List<Long> stadiumIds = dataLevelUntransService.userBaseDataAllowStadiumIds(user);
		search.setAuthorityStadiumIds(stadiumIds);
		search.setCompanyId(user.getCompanyId());
		if(StringUtils.isNotBlank(search.getCategory())){
			search.setCategory("%" + search.getCategory() + "%");
		}
		if(StringUtils.isNotBlank(search.getSmallCategory())){
			search.setSmallCategory("%" + search.getSmallCategory() + "%");
		}
		Page<Map<String, Object>> resultPage = ticketOrShowGatherMapper.getShowGather(search, page);
		for(Map<String, Object> map : resultPage.getRecords()){
			Double price = MapUtils.getDouble(map, "price");
			Integer maxnum = MapUtils.getInteger(map, "maxnum");
			Integer sellNum = MapUtils.getInteger(map, "sellNum");
			Integer reserveNum = MapUtils.getInteger(map, "reserveNum");
			Integer remainingNum = MapUtils.getInteger(map, "remainingNum");
			if(maxnum == null){
				map.replace("maxnum", 0);
				map.put("maxBoxOffice", 0);
			}else{
				map.put("maxBoxOffice", ValueUtil.round(price * maxnum));
			}
			if(sellNum == null){
				map.replace("sellNum", 0);
				map.put("sellBoxOffice", 0);
			}else{
				map.put("sellBoxOffice", ValueUtil.round(price * sellNum));
			}
			if(reserveNum == null){
				map.replace("reserveNum", 0);
				map.put("reserveBoxOffice", 0);
			}else{
				map.put("reserveBoxOffice", ValueUtil.round(price * reserveNum));
			}
			if(remainingNum == null){
				map.replace("remainingNum", 0);
				map.put("remainingBoxOffice", 0);
			}else{
				map.put("remainingBoxOffice", ValueUtil.round(price * remainingNum));
			}
		}
		return ResultCode.getSuccessReturn(resultPage);
	}
}

package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SearReportStandCheckGatherCount;
import com.llwh.dcenter.mapper.ReportStandSaleCheckGatherMapper;
import com.llwh.dcenter.model.ReportStandSaleCheckGather;
import com.llwh.dcenter.service.report.ReportStandSaleCheckGatherService;
import com.llwh.dcenter.vo.report.ReportStandSaleProgramTotalVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportStandSaleCheckGatherServiceImpl extends ServiceImpl<ReportStandSaleCheckGatherMapper, ReportStandSaleCheckGather> implements ReportStandSaleCheckGatherService {
	@Autowired
	private ReportStandSaleCheckGatherMapper saleCheckGatherMapper;

	@Override
	public List<Date> getStandUpdateDate(Timestamp startTime, Timestamp endTime) {
		return saleCheckGatherMapper.getStandUpdateDate(startTime, endTime);
	}

	@Override
	public List<ReportStandSaleCheckGather> getStandCount(Timestamp startTime, Timestamp endTime) {
		return saleCheckGatherMapper.getStandCount(startTime, endTime);
	}

	@Override
	public List<ReportStandSaleCheckGather> getStandRefundCount(Timestamp startTime, Timestamp endTime) {
		return saleCheckGatherMapper.getStandRefundCount(startTime, endTime);
	}

	@Override
	public List<Double> getTicketPriceCount(Long ticketTypeId) {
		return saleCheckGatherMapper.getTicketPriceCount(ticketTypeId);
	}

	@Override
	public IPage<ReportStandSaleCheckGather> getCounts(Page<ReportStandSaleCheckGather> page, SearReportStandCheckGatherCount search) {
		return saleCheckGatherMapper.getCounts(page, search);
	}

	@Override
	public List<ReportStandSaleCheckGather> getTotalCount(SearReportStandCheckGatherCount search,Page page) {
		return saleCheckGatherMapper.getTotalCount(search,page);
	}

	@Override
	public ReportStandSaleProgramTotalVo getTotals(SearReportStandCheckGatherCount search){
		return saleCheckGatherMapper.getTotals(search);
	}
}

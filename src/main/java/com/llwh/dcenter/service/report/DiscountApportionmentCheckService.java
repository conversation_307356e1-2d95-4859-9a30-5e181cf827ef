package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llwh.dcenter.model.DiscountApportionmentCheck;
import com.llwh.dcenter.vo.report.ApportionmentCheckVo;

public interface DiscountApportionmentCheckService extends IService<DiscountApportionmentCheck> {

	List<ApportionmentCheckVo> getShowDiscountApportionment(Timestamp startTime, Timestamp endTime);
	List<ApportionmentCheckVo> getSeatDiscountApportionment(Timestamp startTime, Timestamp endTime);
}

package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llwh.dcenter.model.ReportMallCheckRecord;

/**
 * <AUTHOR>
 * @since 2024/4/8 20:28
 */
public interface ReportMallCheckRecordService extends IService<ReportMallCheckRecord> {
	List<ReportMallCheckRecord> getRecordCounts(Timestamp startTime, Timestamp endTime, String uuid);

	List<ReportMallCheckRecord> getMallChecknoInfos(List<String> uuids);

	List<ReportMallCheckRecord> getMallOneYardCheckInfos(List<String> uuids);
}

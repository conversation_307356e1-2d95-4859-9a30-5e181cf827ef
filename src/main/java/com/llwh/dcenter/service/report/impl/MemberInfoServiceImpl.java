package com.llwh.dcenter.service.report.impl;

import com.llwh.dcenter.mapper.common.MemberInfoMapper;
import com.llwh.dcenter.service.report.MemberInfoService;
import com.llwh.dcenter.vo.common.MemberInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
 * 功能描述: <br>
 * @Author: zhangbiaoyan
 * @Date: 2023/2/22 15:06
 */
@Service
public class MemberInfoServiceImpl implements MemberInfoService {
    @Autowired
    private MemberInfoMapper memberInfoMapper;
    @Override
    public MemberInfoVo getByMemberId(Long companyId, Long memberId) {
        return memberInfoMapper.getByMemberId(companyId,memberId);
    }
}

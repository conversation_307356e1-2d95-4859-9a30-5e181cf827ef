package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.List;

import com.llwh.dcenter.helper.ReportOverallCount;
import com.llwh.dcenter.vo.overalloperate.OverallCountScheduleVo;
import com.llwh.dcenter.vo.overalloperate.OverallPriceCountVo;
import com.llwh.dcenter.vo.overalloperate.SellTypeCountVo;

public interface ReportShowOverAllOperateService {
    List<OverallCountScheduleVo> getTotalTicketCount(ReportOverallCount search);
    List<OverallPriceCountVo> getTotalReserveCount(ReportOverallCount search);

    List<OverallPriceCountVo> getSaleTotalTicketCount(ReportOverallCount search);

    List<SellTypeCountVo> getSellTypeTotalCount(ReportOverallCount search);

    List<Long> getScheduleIdsBySaleType(Long companyId, String saleType, Timestamp date, List<Long> ids);
}

package com.llwh.dcenter.service.report;

import java.util.List;

import cn.fancylab.support.ResultCode;

import com.llwh.dcenter.helper.ProgramStatisticsSearch;
import com.llwh.dcenter.vo.programStatistics.SourceSaleBySellTypeVo;

public interface SeatSourceSaleByDayService {

	ResultCode<List<SourceSaleBySellTypeVo>> getSaleDetailsBySellType(Long companyId, ProgramStatisticsSearch search);
	ResultCode<List<SourceSaleBySellTypeVo>> getProgramSaleDetailsBySellType(Long companyId, ProgramStatisticsSearch search);

	ResultCode<SourceSaleBySellTypeVo> getTotals(Long companyId, ProgramStatisticsSearch search);

	Double getBalanceTotalAmount(Long companyId, ProgramStatisticsSearch search);
}

package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.mapper.ReportSeatCheckGatherMapper;
import com.llwh.dcenter.model.ReportSeatCheckGather;
import com.llwh.dcenter.service.report.ReportSeatCheckGatherService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportSeatCheckGatherServiceImpl extends ServiceImpl<ReportSeatCheckGatherMapper, ReportSeatCheckGather> implements ReportSeatCheckGatherService {

	@Autowired
	private ReportSeatCheckGatherMapper seatCheckGatherMapper;


	@Override
	public List<Date> getCheckDate(Timestamp startTime, Timestamp endTime) {
		return seatCheckGatherMapper.getCheckDate(startTime, endTime);
	}

	@Override
	public List<ReportSeatCheckGather> getSeatCheckCount(Timestamp startTime, Timestamp endTime, String uuid) {
		return seatCheckGatherMapper.getSeatCheckCount(startTime, endTime, uuid);
	}

	@Override
	public List<ReportSeatCheckGather> getSeatSaleCount(Timestamp startTime, Timestamp endTime) {
		return seatCheckGatherMapper.getSeatSaleCount(startTime, endTime);
	}

	@Override
	public List<ReportSeatCheckGather> getTotalCount(SearReportStandCheckDetailCount search) {
		return seatCheckGatherMapper.getTotalCount(search);
	}
}

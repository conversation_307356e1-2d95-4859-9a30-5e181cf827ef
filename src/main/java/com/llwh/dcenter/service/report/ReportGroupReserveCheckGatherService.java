package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llwh.dcenter.helper.SearchGroupReserveCheckDetail;
import com.llwh.dcenter.model.ReportGroupReserveCheckGather;

public interface ReportGroupReserveCheckGatherService extends IService<ReportGroupReserveCheckGather> {

	List<ReportGroupReserveCheckGather> getSaleCount(Timestamp startTime, Timestamp endTime);

	List<ReportGroupReserveCheckGather> getRefundCount(Timestamp startTime, Timestamp endTime);

	List<ReportGroupReserveCheckGather> getTotalCount(Long companyId, SearchGroupReserveCheckDetail search);

	ReportGroupReserveCheckGather getGroupReserveCheckGatherSum(Long companyId, SearchGroupReserveCheckDetail search);

}

package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.api.dcenter.vo.ReportMemberDisplayPointGatherVo;
import com.llwh.dcenter.mapper.ReportMemberDisplayPointGatherMapper;
import com.llwh.dcenter.model.ReportMemberDisplayPointGather;
import com.llwh.dcenter.service.report.ReportMemberDisplayPointGatherService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON><PERSON> on 2024/1/18.
 */
@Service
public class ReportMemberDisplayPointGatherServiceImpl extends ServiceImpl<ReportMemberDisplayPointGatherMapper, ReportMemberDisplayPointGather> implements ReportMemberDisplayPointGatherService {
	@Autowired
	private ReportMemberDisplayPointGatherMapper memberDisplayPointGatherMapper;

	@Override
	public List<ReportMemberDisplayPointGather> getCount(Timestamp startTime, Timestamp endTime) {
		return memberDisplayPointGatherMapper.getCount(startTime, endTime);
	}

	@Override
	public Page<ReportMemberDisplayPointGatherVo> getCounts(Long companyId, Long memberId, Page<ReportMemberDisplayPointGatherVo> page) {
		return memberDisplayPointGatherMapper.getCounts(companyId, memberId, page);
	}

	@Override
	public List<ReportMemberDisplayPointGatherVo> getCountsByMemberId(Long companyId, Long memberId) {
		return memberDisplayPointGatherMapper.getCountsByMemberId(companyId, memberId);
	}

}

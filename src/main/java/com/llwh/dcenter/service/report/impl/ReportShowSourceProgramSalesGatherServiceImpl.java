package com.llwh.dcenter.service.report.impl;

import java.util.List;
import java.util.Map;

import com.llwh.dcenter.helper.ReportShowSourceProgramSalesGatherSearch;
import com.llwh.dcenter.mapper.ReportShowSourceProgramSalesGatherMapper;
import com.llwh.dcenter.service.report.ReportShowSourceProgramSalesGatherService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ReportShowSourceProgramSalesGatherServiceImpl implements ReportShowSourceProgramSalesGatherService {
	@Autowired
	private ReportShowSourceProgramSalesGatherMapper showSourceProgramSalesGatherMapper;

	@Override
	public List<Map<String, Object>> getCounts(Long companyId, ReportShowSourceProgramSalesGatherSearch search) {
		return showSourceProgramSalesGatherMapper.getCounts(companyId, search);
	}

	@Override
	public Map<String, Object> getTotals(Long companyId, ReportShowSourceProgramSalesGatherSearch search) {
		return showSourceProgramSalesGatherMapper.getTotals(companyId, search);
	}
}

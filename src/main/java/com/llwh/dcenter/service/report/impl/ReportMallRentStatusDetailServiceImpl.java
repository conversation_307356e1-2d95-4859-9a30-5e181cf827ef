package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.mapper.ReportMallRentStatusDetailMapper;
import com.llwh.dcenter.model.ReportMallRentStatusDetail;
import com.llwh.dcenter.service.report.ReportMallRentStatusDetailService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportMallRentStatusDetailServiceImpl extends ServiceImpl<ReportMallRentStatusDetailMapper, ReportMallRentStatusDetail> implements ReportMallRentStatusDetailService {

	@Autowired
	private ReportMallRentStatusDetailMapper mallRentStatusDetailMapper;

	@Override
	public List<ReportMallRentStatusDetail> getRentOutCount(Timestamp startTime, Timestamp endTime) {
		return mallRentStatusDetailMapper.getRentOutCount(startTime, endTime);
	}

	@Override
	public List<ReportMallRentStatusDetail> getRepayCount(Timestamp startTime, Timestamp endTime) {
		return mallRentStatusDetailMapper.getRepayCount(startTime, endTime);
	}

	@Override
	public List<ReportMallRentStatusDetail> getLoseCount(Timestamp startTime, Timestamp endTime) {
		return mallRentStatusDetailMapper.getLoseCount(startTime, endTime);
	}
}

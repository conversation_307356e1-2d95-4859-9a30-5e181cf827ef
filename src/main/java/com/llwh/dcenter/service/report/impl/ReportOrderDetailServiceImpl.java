package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.List;
import com.llwh.dcenter.vo.common.MemberActionStatsVo;
import com.llwh.dcenter.vo.report.ReportMemberOrderStatisticsVo;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.mapper.ReportOrderDetailMapper;
import com.llwh.dcenter.model.ReportOrderDetail;
import com.llwh.dcenter.service.report.ReportOrderDetailService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportOrderDetailServiceImpl extends ServiceImpl<ReportOrderDetailMapper, ReportOrderDetail> implements ReportOrderDetailService {

	@Autowired
	private ReportOrderDetailMapper reportOrderDetailMapper;



	@Override
	public List<ReportOrderDetail> getPageShowReportOrderDetailCount(Long companyId, Timestamp startTime, Timestamp endTime, int offset, int pageSize) {
		return reportOrderDetailMapper.getPageShowReportOrderDetailCount(companyId,startTime, endTime,offset,pageSize);
	}



	@Override
	public List<ReportOrderDetail> getPageTicketReportOrderDetailCount(Long companyId, Timestamp startTime, Timestamp endTime, int offset, int pageSize) {
		return reportOrderDetailMapper.getPageTicketReportOrderDetailCount(companyId,startTime, endTime,offset,pageSize);
	}

	@Override
	public List<ReportMemberOrderStatisticsVo> getByShowMemberIds(Long companyId,List<Long> memberIds) {
		return reportOrderDetailMapper.getByShowMemberIds(companyId,memberIds);
	}

	@Override
	public List<ReportMemberOrderStatisticsVo> getByTicketMemberIds(Long companyId,List<Long> memberIds) {
		return reportOrderDetailMapper.getByTicketMemberIds(companyId,memberIds);
	}
	@Override
	public MemberActionStatsVo getShowMemberActionStatsByMemberId(Long companyId, Long memberId) {
		return reportOrderDetailMapper.getShowMemberActionStatsByMemberId(companyId,memberId);
	}
	@Override
	public MemberActionStatsVo getTicketMemberActionStatsByMemberId(Long companyId, Long memberId) {
		return reportOrderDetailMapper.getTicketMemberActionStatsByMemberId(companyId,memberId);
	}

}

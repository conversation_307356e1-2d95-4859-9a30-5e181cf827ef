package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.llwh.dcenter.helper.MembershipSaleSearch;
import com.llwh.dcenter.model.ReportMemberSaleDetail;
import com.llwh.dcenter.model.ReportMemberSaleGather;
import com.llwh.dcenter.vo.report.ReportStadiumSaleDayVo;

public interface ReportMemberSaleDetailService extends IService<ReportMemberSaleDetail> {

	List<ReportMemberSaleDetail> getMemberSaleDetailCount(Timestamp startTime, Timestamp endTime);

	List<ReportMemberSaleDetail> getOfflineMemberSaleDetailCount(Timestamp startTime, Timestamp endTime);

	List<ReportMemberSaleDetail> getMemberRefundDetailCount(Timestamp startTime, Timestamp endTime);

	List<ReportMemberSaleDetail> getOfflineMemberRefundDetailCount(Timestamp startTime, Timestamp endTime);

	List<ReportMemberSaleDetail> getTheatreName(Long tbsUserId, Long userGroupId);

	List<ReportMemberSaleDetail> getTheatreNames(List<Long> tbsUserIds, List<Long> userGroupIds);

	List<ReportMemberSaleDetail> getTotalCount(Date datefrom, Date dateto, String memberCardTypeId, Long companyId, Page page);

	List<ReportStadiumSaleDayVo> getMemberShipActiveCount(Long companyId, Date datefrom, Date dateto);
	List<ReportMemberSaleGather> getTotals(MembershipSaleSearch search);
	Page<ReportMemberSaleDetail> listReportMemberSaleDetail(Page<ReportMemberSaleDetail> page, MembershipSaleSearch search, Long companyId);

	List<ReportMemberSaleDetail> getBalanceCardSaleDetailCount(Timestamp startTime, Timestamp endTime);
	List<ReportMemberSaleDetail> getOfflineBalanceCardSaleDetailCount(Timestamp startTime, Timestamp endTime);
	List<ReportMemberSaleDetail> getBalanceCardRefundDetailCount(Timestamp startTime, Timestamp endTime);
	List<ReportMemberSaleDetail> getOfflineBalanceCardRefundDetailCount(Timestamp startTime, Timestamp endTime);
	List<ReportMemberSaleDetail> getBalanceCardChargeDetailCount(Timestamp startTime, Timestamp endTime);
}


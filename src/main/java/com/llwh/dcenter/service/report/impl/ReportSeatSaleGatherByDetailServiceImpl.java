package com.llwh.dcenter.service.report.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SeatSaleGatherByDetailSear;
import com.llwh.dcenter.mapper.ReportSeatSaleGatherByDetailMapper;
import com.llwh.dcenter.model.ReportSeatSaleGatherProgram;
import com.llwh.dcenter.service.report.ReportSeatSaleGatherByDetailService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportSeatSaleGatherByDetailServiceImpl extends ServiceImpl<ReportSeatSaleGatherByDetailMapper, ReportSeatSaleGatherProgram> implements ReportSeatSaleGatherByDetailService {
	@Autowired
	private ReportSeatSaleGatherByDetailMapper saleGatherByDetailMapper;

	@Override
	public List<ReportSeatSaleGatherProgram> getSeatCount(SeatSaleGatherByDetailSear search) {
		return saleGatherByDetailMapper.getSeatCount(search);
	}

	@Override
	public List<ReportSeatSaleGatherProgram> getSeatRefundCount(SeatSaleGatherByDetailSear search) {
		return saleGatherByDetailMapper.getSeatRefundCount(search);
	}
}

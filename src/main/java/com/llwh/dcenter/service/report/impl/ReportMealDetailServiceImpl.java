package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SearMealDetail;
import com.llwh.dcenter.mapper.ReportMealDetailMapper;
import com.llwh.dcenter.model.ReportMealDetail;
import com.llwh.dcenter.service.report.ReportMealDetailService;
import com.llwh.dcenter.vo.report.huifu.MealTotalVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportMealDetailServiceImpl extends ServiceImpl<ReportMealDetailMapper, ReportMealDetail> implements ReportMealDetailService {

	@Autowired(required = false)
	private ReportMealDetailMapper reportMealDetailMapper;

	@Override
	public List<ReportMealDetail> getMealCount(Timestamp startTime, Timestamp endTime, String tradeNo) {
		return reportMealDetailMapper.getMealCount(startTime, endTime, tradeNo);
	}

	@Override
	public List<ReportMealDetail> getRefundMealCount(Timestamp startTime, Timestamp endTime, String tradeNo) {
		return reportMealDetailMapper.getRefundMealCount(startTime, endTime, tradeNo);
	}

	@Override
	public List<ReportMealDetail> getDetailForGroup(Long companyId, SearMealDetail search) {
		return reportMealDetailMapper.getDetailForGroup(companyId, search);
	}

	@Override
	public MealTotalVo getDetailTotal(Long companyId, SearMealDetail search) {
		return reportMealDetailMapper.getDetailTotal(companyId, search);
	}
}

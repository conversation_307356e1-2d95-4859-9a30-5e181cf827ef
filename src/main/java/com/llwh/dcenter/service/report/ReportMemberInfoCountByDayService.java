package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llwh.dcenter.helper.SearMemberInfoCountByDay;
import com.llwh.dcenter.model.ReportMemberInfoCountByDay;

public interface ReportMemberInfoCountByDayService extends IService<ReportMemberInfoCountByDay> {

	List<ReportMemberInfoCountByDay> getMemberInfoCountByDay(Timestamp lastDay);
	List<ReportMemberInfoCountByDay> listMemberInfoCount(SearMemberInfoCountByDay search);
}

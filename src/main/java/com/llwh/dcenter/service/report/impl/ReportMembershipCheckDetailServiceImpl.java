package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.mapper.ReportMembershipCheckDetailMapper;
import com.llwh.dcenter.model.ReportMembershipCheckDetail;
import com.llwh.dcenter.service.report.ReportMembershipCheckDetailService;
import com.llwh.dcenter.vo.report.ReportStadiumSaleDayVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportMembershipCheckDetailServiceImpl extends ServiceImpl<ReportMembershipCheckDetailMapper, ReportMembershipCheckDetail> implements ReportMembershipCheckDetailService {
	@Autowired
	private ReportMembershipCheckDetailMapper membershipCheckDetailMapper;

	@Override
	public List<ReportMembershipCheckDetail> getCheckDetailCount(Timestamp startTime, Timestamp endTime) {
		return membershipCheckDetailMapper.getCheckDetailCount(startTime, endTime);
	}

	@Override
	public List<ReportMembershipCheckDetail> getCheckDetailCount2(Timestamp startTime, Timestamp endTime) {
		return membershipCheckDetailMapper.getCheckDetailCount2(startTime, endTime);
	}

	@Override
	public List<ReportMembershipCheckDetail> getCheckNoAndGroupName(Long tbsUserId) {
		return membershipCheckDetailMapper.getCheckNoAndGroupName(tbsUserId);
	}

	@Override
	public List<ReportMembershipCheckDetail> getTotalCount(Date datefrom, Date dateto, String membershipCardTypeId, Long companyId, Page page) {
		return membershipCheckDetailMapper.getTotalCount(datefrom, dateto, membershipCardTypeId, companyId,page);
	}
	@Override
	public List<ReportStadiumSaleDayVo> getCheckCount(Long companyId,Date datefrom, Date dateto) {
		return membershipCheckDetailMapper.getCheckCount(companyId,datefrom, dateto);
	}

	@Override
	public List<ReportMembershipCheckDetail> getCheckDetailCountOffline(Timestamp startTime, Timestamp endTime) {
		return membershipCheckDetailMapper.getCheckDetailCountOffline(startTime, endTime);
	}
}

package com.llwh.dcenter.service.report.impl;

import com.llwh.dcenter.mapper.common.DynamicFieldMapper;
import com.llwh.dcenter.service.report.DynamicFieldService;
import com.llwh.dcenter.vo.common.DynamicFieldVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DynamicFieldServiceImpl implements DynamicFieldService {

    @Autowired
    private DynamicFieldMapper dynamicFieldMapper;

    @Override
    public DynamicFieldVo getByTagAndCategoryFromThreatre(Long companyId, String tag, String category) {
        return dynamicFieldMapper.getByTagAndCategoryFromThreatre(companyId,tag,category);
    }

    @Override
    public DynamicFieldVo getByTagAndCategoryFromThreatreByProId(Long companyId, String tag, String category, Long programId) {
        return dynamicFieldMapper.getByTagAndCategoryFromThreatreByProId(companyId,tag,category, programId);
    }

    @Override
    public DynamicFieldVo getByTagAnCategoryFromThvendor(Long companyId, String tag, String category) {
        return dynamicFieldMapper.getByTagAnCategoryFromThvendor(companyId,tag,category);
    }
}

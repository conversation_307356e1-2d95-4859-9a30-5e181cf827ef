package com.llwh.dcenter.service.report.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SearReportStandCheckDetailCount;
import com.llwh.dcenter.mapper.ReportStandCheckGatherMapper;
import com.llwh.dcenter.model.ReportStandCheckGather;
import com.llwh.dcenter.service.report.ReportStandCheckGatherService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportStandCheckGatherServiceImpl extends ServiceImpl<ReportStandCheckGatherMapper, ReportStandCheckGather> implements ReportStandCheckGatherService {

	@Autowired
	private ReportStandCheckGatherMapper standCheckGatherMapper;
	@Override
	public List<ReportStandCheckGather> getStandCheckCount(Timestamp startTime, Timestamp endTime) {
		return standCheckGatherMapper.getStandCheckCount(startTime, endTime);
	}

	@Override
	public List<ReportStandCheckGather> getStandSaleCount(Timestamp startTime, Timestamp endTime) {
		return standCheckGatherMapper.getStandSaleCount(startTime, endTime);
	}

	@Override
	public List<ReportStandCheckGather> getTotalCount(SearReportStandCheckDetailCount search, Page page) {
		return standCheckGatherMapper.getTotalCount(search,page);
	}

	@Override
	public Map getTotals(SearReportStandCheckDetailCount search){
		return standCheckGatherMapper.getTotals(search);
	}
}

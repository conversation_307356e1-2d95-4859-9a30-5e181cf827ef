package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.mapper.CombPayFlowMapper;
import com.llwh.dcenter.model.CombPayFlow;
import com.llwh.dcenter.service.report.CombPayFlowService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON><PERSON> on 2023/9/2.
 */
@Service
public class CombPayFlowServiceImpl extends ServiceImpl<CombPayFlowMapper, CombPayFlow> implements CombPayFlowService {
	@Autowired
	private CombPayFlowMapper combPayFlowMapper;

	@Override
	public List<CombPayFlow> getCount(Timestamp startTime, Timestamp endTime, String tradeNo) {
		return combPayFlowMapper.getCount(startTime, endTime, tradeNo);
	}


}

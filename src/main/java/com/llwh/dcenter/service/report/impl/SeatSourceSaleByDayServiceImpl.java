package com.llwh.dcenter.service.report.impl;

import java.util.List;

import cn.fancylab.support.ResultCode;

import com.llwh.dcenter.helper.ProgramStatisticsSearch;
import com.llwh.dcenter.mapper.SeatSourceSaleByDayMapper;
import com.llwh.dcenter.service.report.SeatSourceSaleByDayService;
import com.llwh.dcenter.vo.programStatistics.SourceSaleBySellTypeVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SeatSourceSaleByDayServiceImpl implements SeatSourceSaleByDayService {

	@Autowired
	private SeatSourceSaleByDayMapper seatSourceSaleByDayMapper;

	@Override
	public ResultCode<List<SourceSaleBySellTypeVo>> getSaleDetailsBySellType(Long companyId, ProgramStatisticsSearch search) {
		List<SourceSaleBySellTypeVo> result = seatSourceSaleByDayMapper.getSaleDetailsBySellType(companyId, search);
		return ResultCode.getSuccessReturn(result);
	}

	@Override
	public ResultCode<List<SourceSaleBySellTypeVo>> getProgramSaleDetailsBySellType(Long companyId, ProgramStatisticsSearch search) {
		List<SourceSaleBySellTypeVo> result = seatSourceSaleByDayMapper.getProgramSaleDetailsBySellType(companyId, search);
		return ResultCode.getSuccessReturn(result);
	}

	@Override
	public ResultCode<SourceSaleBySellTypeVo> getTotals(Long companyId, ProgramStatisticsSearch search) {
		SourceSaleBySellTypeVo result = seatSourceSaleByDayMapper.getTotals(companyId, search);
		return ResultCode.getSuccessReturn(result);
	}

	@Override
	public Double getBalanceTotalAmount(Long companyId, ProgramStatisticsSearch search) {
		return seatSourceSaleByDayMapper.getBalanceTotalAmount(companyId, search);
	}
}

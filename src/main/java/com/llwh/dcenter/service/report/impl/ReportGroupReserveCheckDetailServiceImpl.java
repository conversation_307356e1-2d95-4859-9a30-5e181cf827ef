package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SearchGroupReserveCheckDetail;
import com.llwh.dcenter.mapper.ReportGroupReserveCheckDetailMapper;
import com.llwh.dcenter.model.ReportGroupReserveCheckDetail;
import com.llwh.dcenter.service.report.ReportGroupReserveCheckDetailService;
import com.llwh.dcenter.vo.report.ReportGroupReserveCheckDetailVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportGroupReserveCheckDetailServiceImpl extends ServiceImpl<ReportGroupReserveCheckDetailMapper, ReportGroupReserveCheckDetail> implements ReportGroupReserveCheckDetailService {
	@Autowired
	private ReportGroupReserveCheckDetailMapper groupReserveCheckDetailMapper;

	@Override
	public List<ReportGroupReserveCheckDetailVo> getSaleCount(Timestamp startTime, Timestamp endTime) {
		return groupReserveCheckDetailMapper.getSaleCount(startTime, endTime);
	}

	@Override
	public List<ReportGroupReserveCheckDetailVo> getCheckList(List<String> uuids){
		return groupReserveCheckDetailMapper.getCheckList(uuids);
	}

	@Override
	public List<ReportGroupReserveCheckDetail> getRefundCount(Timestamp startTime, Timestamp endTime) {
		return groupReserveCheckDetailMapper.getRefundCount(startTime, endTime);
	}

	@Override
	public List<ReportGroupReserveCheckDetail> getTotalCount(Long companyId, SearchGroupReserveCheckDetail search) {
		return groupReserveCheckDetailMapper.getTotalCount(companyId, search);
	}
}

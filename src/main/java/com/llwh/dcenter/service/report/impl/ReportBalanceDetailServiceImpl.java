package com.llwh.dcenter.service.report.impl;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llwh.dcenter.helper.SearReportBalanceDetail;
import com.llwh.dcenter.mapper.ReportBalanceDetailMapper;
import com.llwh.dcenter.model.ReportBalanceDetail;
import com.llwh.dcenter.service.report.ReportBalanceDetailService;
import com.llwh.dcenter.vo.report.ReportBalanceDetailTotalVo;
import com.llwh.dcenter.vo.report.ReportBalanceGroupTotalVo;
import com.llwh.dcenter.vo.req.ReportBalanceGroupSear;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportBalanceDetailServiceImpl extends ServiceImpl<ReportBalanceDetailMapper, ReportBalanceDetail> implements ReportBalanceDetailService {

	@Autowired
	private ReportBalanceDetailMapper balanceDetailMapper;

	@Override
	public List<ReportBalanceDetail> getChargeCount(Timestamp startTime, Timestamp endTime) {
		return balanceDetailMapper.getChargeCount(startTime, endTime);
	}

	@Override
	public List<ReportBalanceDetail> getExpendCount(Timestamp startTime, Timestamp endTime) {
		return balanceDetailMapper.getExpendCount(startTime, endTime);
	}

	@Override
	public ReportBalanceDetailTotalVo getTotals(SearReportBalanceDetail search){
		return balanceDetailMapper.getTotals(search);
	}

	@Override
	public List<ReportBalanceGroupTotalVo> groupBalanceByProgram(ReportBalanceGroupSear search, Long companyId){
		return balanceDetailMapper.groupBalanceByProgram(search, companyId);
	}
}

package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.llwh.dcenter.helper.SearchReportDouyinSeatOrderCheckCpsDetailCount;
import com.llwh.dcenter.model.ReportDouyinStandSaleCheckCpsDetail;
import com.llwh.dcenter.vo.DouyinOrderSettleVo;
import com.llwh.dcenter.vo.report.ReportDouyinStandSaleCheckCpsDetailVo;

public interface ReportDouyinStandSaleCheckCpsDetailService extends IService<ReportDouyinStandSaleCheckCpsDetail> {

	List<ReportDouyinStandSaleCheckCpsDetail> getStandCount(Timestamp startTime, Timestamp endTime, String tradeNo);
	List<ReportDouyinStandSaleCheckCpsDetail> getRefundStandCount(Timestamp startTime, Timestamp endTime, String tradeNo);
	List<ReportDouyinStandSaleCheckCpsDetail> getStandCps(List<String> tradeNos);
	List<DouyinOrderSettleVo> getStandSettle(List<String> tradeNos);
	List<String> getLatestUpdatedStandCps(Timestamp startTime, Timestamp endTime);


	List<ReportDouyinStandSaleCheckCpsDetail> getTotalCount(SearchReportDouyinSeatOrderCheckCpsDetailCount search);


	Page<ReportDouyinStandSaleCheckCpsDetailVo> getCounts(Page<ReportDouyinStandSaleCheckCpsDetailVo> page, SearchReportDouyinSeatOrderCheckCpsDetailCount search);

	void removeNotDouyinPayOrder(String paymethod);
}

package com.llwh.dcenter.service.report;

import java.util.List;
import java.util.Map;

import com.llwh.dcenter.helper.ReportOverallCount;

public interface ReportTicketTransactionGatherService {
	List<Map<String, Object>> getCounts(ReportOverallCount search);
	List<Map<String, Object>> getSourceCounts(ReportOverallCount search);

	List<Map<String, Object>> getOrderDetailIds(ReportOverallCount search);
	List<Map<String, Object>> getInfos4pay(List<Long> orderDetailIds);
	List<Map<String, Object>> getInfos4refund(List<Long> orderDetailIds);
	List<Map<String, Object>> getSeatDiscountApportionmentCounts(List<String> nos);

	List<Map<String, Object>> getOrderDetailIds4Source(ReportOverallCount search);
	List<Map<String, Object>> getInfos4pay4Source(List<Long> orderDetailIds);
	List<Map<String, Object>> getInfos4refund4Source(List<Long> orderDetailIds);
}

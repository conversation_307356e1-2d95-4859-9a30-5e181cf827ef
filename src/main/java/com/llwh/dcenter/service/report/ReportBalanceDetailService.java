package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llwh.dcenter.helper.SearReportBalanceDetail;
import com.llwh.dcenter.model.ReportBalanceDetail;
import com.llwh.dcenter.vo.report.ReportBalanceDetailTotalVo;
import com.llwh.dcenter.vo.report.ReportBalanceGroupTotalVo;
import com.llwh.dcenter.vo.req.ReportBalanceGroupSear;

public interface ReportBalanceDetailService extends IService<ReportBalanceDetail> {

	List<ReportBalanceDetail> getChargeCount(Timestamp startTime, Timestamp endTime);

	List<ReportBalanceDetail> getExpendCount(Timestamp startTime, Timestamp endTime);

	ReportBalanceDetailTotalVo getTotals(SearReportBalanceDetail search);
	List<ReportBalanceGroupTotalVo> groupBalanceByProgram(ReportBalanceGroupSear search, Long companyId);
}

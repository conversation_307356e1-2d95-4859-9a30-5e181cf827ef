package com.llwh.dcenter.service.report;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llwh.dcenter.helper.SearchGroupReserveCheckDetail;
import com.llwh.dcenter.model.ReportGroupReserveCheckDetail;
import com.llwh.dcenter.vo.report.ReportGroupReserveCheckDetailVo;

public interface ReportGroupReserveCheckDetailService extends IService<ReportGroupReserveCheckDetail> {

	List<ReportGroupReserveCheckDetailVo> getSaleCount(Timestamp startTime, Timestamp endTime);

	List<ReportGroupReserveCheckDetailVo> getCheckList(List<String> uuids);

	List<ReportGroupReserveCheckDetail> getRefundCount(Timestamp startTime, Timestamp endTime);

	List<ReportGroupReserveCheckDetail> getTotalCount(Long companyId, SearchGroupReserveCheckDetail search);
}
